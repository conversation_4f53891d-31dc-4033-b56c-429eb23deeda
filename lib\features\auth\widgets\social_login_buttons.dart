import 'package:flutter/material.dart';
import 'auth_button.dart';

class SocialLoginButtons extends StatelessWidget {
  final bool showGoogle;
  final bool showApple;
  final bool showFacebook;
  final VoidCallback? onGooglePressed;
  final VoidCallback? onApplePressed;
  final VoidCallback? onFacebookPressed;

  const SocialLoginButtons({
    super.key,
    this.showGoogle = true,
    this.showApple = false, // Apple sign-in is typically iOS only
    this.showFacebook = false,
    this.onGooglePressed,
    this.onApplePressed,
    this.onFacebookPressed,
  });

  @override
  Widget build(BuildContext context) {
    final buttons = <Widget>[];

    if (showGoogle) {
      buttons.add(
        GoogleSignInButton(
          onPressed: onGooglePressed ?? () => _handleGoogleSignIn(context),
        ),
      );
    }

    if (showApple) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(height: 12));
      buttons.add(
        AppleSignInButton(
          onPressed: onApplePressed ?? () => _handleAppleSignIn(context),
        ),
      );
    }

    if (showFacebook) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(height: 12));
      buttons.add(
        FacebookSignInButton(
          onPressed: onFacebookPressed ?? () => _handleFacebookSignIn(context),
        ),
      );
    }

    if (buttons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: buttons,
    );
  }

  void _handleGoogleSignIn(BuildContext context) {
    // Google Sign-In implementation
    debugPrint('Google Sign-In pressed');
    if (onGooglePressed != null) {
      onGooglePressed!();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Google Sign-In coming soon!'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _handleAppleSignIn(BuildContext context) {
    // Apple Sign-In implementation
    debugPrint('Apple Sign-In pressed');
    if (onApplePressed != null) {
      onApplePressed!();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Apple Sign-In coming soon!'),
          backgroundColor: Colors.black,
        ),
      );
    }
  }

  void _handleFacebookSignIn(BuildContext context) {
    // Facebook Sign-In implementation
    debugPrint('Facebook Sign-In pressed');
    if (onFacebookPressed != null) {
      onFacebookPressed!();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Facebook Sign-In coming soon!'),
          backgroundColor: Colors.indigo,
        ),
      );
    }
  }
}

// Alternative layout with horizontal buttons
class SocialLoginButtonsHorizontal extends StatelessWidget {
  final bool showGoogle;
  final bool showApple;
  final bool showFacebook;
  final VoidCallback? onGooglePressed;
  final VoidCallback? onApplePressed;
  final VoidCallback? onFacebookPressed;

  const SocialLoginButtonsHorizontal({
    super.key,
    this.showGoogle = true,
    this.showApple = false,
    this.showFacebook = false,
    this.onGooglePressed,
    this.onApplePressed,
    this.onFacebookPressed,
  });

  @override
  Widget build(BuildContext context) {
    final buttons = <Widget>[];

    if (showGoogle) {
      buttons.add(
        Expanded(
          child: _SocialIconButton(
            icon: Icons.g_mobiledata,
            onPressed: onGooglePressed ?? _handleGoogleSignIn,
            backgroundColor: Colors.white,
            iconColor: Colors.red,
          ),
        ),
      );
    }

    if (showApple) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));
      buttons.add(
        Expanded(
          child: _SocialIconButton(
            icon: Icons.apple,
            onPressed: onApplePressed ?? _handleAppleSignIn,
            backgroundColor: Colors.black,
            iconColor: Colors.white,
          ),
        ),
      );
    }

    if (showFacebook) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));
      buttons.add(
        Expanded(
          child: _SocialIconButton(
            icon: Icons.facebook,
            onPressed: onFacebookPressed ?? _handleFacebookSignIn,
            backgroundColor: const Color(0xFF1877F2),
            iconColor: Colors.white,
          ),
        ),
      );
    }

    if (buttons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      children: buttons,
    );
  }

  void _handleGoogleSignIn() {
    debugPrint('Google Sign-In pressed');
  }

  void _handleAppleSignIn() {
    debugPrint('Apple Sign-In pressed');
  }

  void _handleFacebookSignIn() {
    debugPrint('Facebook Sign-In pressed');
  }
}

class _SocialIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color backgroundColor;
  final Color iconColor;

  const _SocialIconButton({
    required this.icon,
    this.onPressed,
    required this.backgroundColor,
    required this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Center(
            child: Icon(
              icon,
              size: 24,
              color: iconColor,
            ),
          ),
        ),
      ),
    );
  }
}

// Quick access social login widget for minimal UI
class QuickSocialLogin extends StatelessWidget {
  final VoidCallback? onGooglePressed;

  const QuickSocialLogin({
    super.key,
    this.onGooglePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onGooglePressed ?? _handleGoogleSignIn,
          borderRadius: BorderRadius.circular(12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.g_mobiledata,
                size: 24,
                color: Colors.red,
              ),
              const SizedBox(width: 12),
              Text(
                'Continue with Google',
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleGoogleSignIn() {
    debugPrint('Quick Google Sign-In pressed');
  }
}
