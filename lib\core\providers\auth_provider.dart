import 'package:flutter/foundation.dart';
import '../services/storage_service.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AuthProvider extends ChangeNotifier {
  AuthStatus _status = AuthStatus.initial;
  User? _user;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AuthStatus get status => _status;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _status == AuthStatus.authenticated;

  // Initialize auth state
  Future<void> initializeAuth() async {
    try {
      _setLoading(true);

      final isLoggedIn = StorageService.isLoggedIn();
      final rememberMe = StorageService.getRememberMe();
      final token = StorageService.getAuthToken();
      final userData = StorageService.getUserData();

      if (isLoggedIn && rememberMe && token != null && userData != null) {
        _user = User.fromJson(userData);
        _status = AuthStatus.authenticated;

        // Verify token with backend (with timeout) - run in background
        _verifyTokenInBackground();
      } else {
        if (!rememberMe) {
          await StorageService.clearUserData();
        }
        _status = AuthStatus.unauthenticated;
      }
    } catch (e) {
      _status = AuthStatus.unauthenticated;
      _errorMessage = 'Failed to initialize authentication: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Verify token in background without blocking initialization
  Future<void> _verifyTokenInBackground() async {
    try {
      final result = await AuthService.verifyToken()
          .timeout(const Duration(seconds: 10));

      // Only logout if token is actually invalid (401), not for network errors
      if (result.isError && result.error?.contains('401') == true) {
        await logout();
      }
    } catch (e) {
      // Don't logout for network errors or timeouts
      // Let the user continue and handle token issues on next API call
    }
  }

  // Login
  Future<bool> login(String email, String password, {bool rememberMe = true}) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.login(email: email, password: password);

      if (result.isSuccess) {
        final loginResponse = result.data!;

        // Update state
        _user = User.fromJson(loginResponse.user);
        _status = AuthStatus.authenticated;

        // Store remember me preference
        await StorageService.setRememberMe(rememberMe);

        notifyListeners();
        return true;
      } else {
        _setError(result.error ?? 'Login failed');
        return false;
      }
    } catch (e) {
      _setError('Login failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Send OTP for registration
  Future<bool> sendRegistrationOtp({
    required String email,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.sendRegistrationOtp(email: email);

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.data ?? 'Failed to send OTP');
        return false;
      }
    } catch (e) {
      _setError('Failed to send OTP: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Verify OTP
  Future<bool> verifyOtp({
    required String email,
    required String otp,
    required String type,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.verifyOtp(
        email: email,
        otp: otp,
        type: type,
      );

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.data ?? 'Failed to verify OTP');
        return false;
      }
    } catch (e) {
      _setError('Failed to verify OTP: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Register with OTP verification
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String phone,
    required String otp,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.register(
        name: name,
        email: email,
        password: password,
        phone: phone,
        otp: otp,
      );

      if (result.isSuccess) {
        final loginResponse = result.data!;

        // Update state
        _user = User.fromJson(loginResponse.user);
        _status = AuthStatus.authenticated;

        notifyListeners();
        return true;
      } else {
        _setError(result.error ?? 'Registration failed');
        return false;
      }
    } catch (e) {
      _setError('Registration failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      _setLoading(true);
      
      // Call logout API if user is authenticated
      if (_status == AuthStatus.authenticated) {
        try {
          await AuthService.logout();
        } catch (e) {
          // Continue with logout even if API call fails
        }
      }

      // Clear local storage
      await StorageService.clearUserData();

      // Update state
      _user = null;
      _status = AuthStatus.unauthenticated;
      _clearError();
      
      notifyListeners();
    } catch (e) {
      _setError('Logout failed: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Forgot Password
  Future<bool> forgotPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.forgotPassword(email);

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.error ?? 'Failed to send reset email');
        return false;
      }
    } catch (e) {
      _setError('Failed to send reset email: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Reset Password
  Future<bool> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.resetPassword(
        token: token,
        newPassword: newPassword,
      );

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.error ?? 'Failed to reset password');
        return false;
      }
    } catch (e) {
      _setError('Failed to reset password: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update Profile
  Future<bool> updateProfile({
    String? name,
    String? email,
    String? phone,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.updateProfile(
        name: name ?? _user?.name ?? '',
        email: email ?? _user?.email ?? '',
        phone: phone ?? _user?.phone ?? '',
      );

      if (result.isSuccess) {
        final updatedUser = result.data!;

        // Update state
        _user = updatedUser;

        notifyListeners();
        return true;
      } else {
        _setError(result.error ?? 'Failed to update profile');
        return false;
      }
    } catch (e) {
      _setError('Failed to update profile: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Change Password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result.isSuccess) {
        return true;
      } else {
        _setError(result.error ?? 'Failed to change password');
        return false;
      }
    } catch (e) {
      _setError('Failed to change password: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    if (_status != AuthStatus.authenticated) return;

    try {
      // For now, just refresh from stored data
      final userData = StorageService.getUserData();
      if (userData != null) {
        _user = User.fromJson(userData);
        notifyListeners();
      }
    } catch (e) {
      // Failed to refresh user data
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _status = AuthStatus.error;
    notifyListeners();
  }



  void _clearError() {
    _errorMessage = null;
    if (_status == AuthStatus.error) {
      _status = _user != null ? AuthStatus.authenticated : AuthStatus.unauthenticated;
    }
  }
}
