<?php
/**
 * Debug OTP functionality
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');

try {
    // Include configuration
    if (file_exists(__DIR__ . '/../config/env.php')) {
        require_once __DIR__ . '/../config/env.php';
    }
    
    // Include database
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../utils/helpers.php';
    
    $db = new Database();
    $conn = $db->getConnection();
    
    $debug = [];
    
    // Test 1: Database connection
    $debug['database_connection'] = $conn ? 'OK' : 'FAILED';
    
    // Test 2: Check if otps table exists
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'otps'");
        $result = $stmt->fetch();
        $debug['otps_table_exists'] = $result ? 'YES' : 'NO';
        
        if ($result) {
            // Get table structure
            $stmt = $conn->query("DESCRIBE otps");
            $debug['otps_table_structure'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (Exception $e) {
        $debug['otps_table_check'] = 'ERROR: ' . $e->getMessage();
    }
    
    // Test 3: Check other required tables
    $tables = ['users', 'chits', 'settings'];
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            $result = $stmt->fetch();
            $debug['table_' . $table] = $result ? 'EXISTS' : 'MISSING';
        } catch (Exception $e) {
            $debug['table_' . $table] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    // Test 4: Test OTP insertion (if table exists)
    if (isset($debug['otps_table_exists']) && $debug['otps_table_exists'] === 'YES') {
        try {
            $email = '<EMAIL>';
            $otp = '123456';
            $type = 'registration';
            $expires_at = date('Y-m-d H:i:s', time() + 600);
            
            // Try to insert a test OTP
            $sql = "INSERT INTO otps (email, otp, type, expires_at) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute([$email, $otp, $type, $expires_at]);
            
            if ($result) {
                $debug['otp_insert_test'] = 'SUCCESS';
                
                // Clean up test data
                $sql = "DELETE FROM otps WHERE email = ? AND otp = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$email, $otp]);
            } else {
                $debug['otp_insert_test'] = 'FAILED';
            }
        } catch (Exception $e) {
            $debug['otp_insert_test'] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    // Test 5: Check email configuration
    $debug['email_config'] = [
        'smtp_host' => defined('SMTP_HOST') ? SMTP_HOST : 'NOT_DEFINED',
        'smtp_username' => defined('SMTP_USERNAME') ? SMTP_USERNAME : 'NOT_DEFINED',
        'from_email' => defined('SMTP_FROM_EMAIL') ? SMTP_FROM_EMAIL : 'NOT_DEFINED',
    ];
    
    echo json_encode([
        'success' => true,
        'message' => 'OTP Debug Results',
        'debug' => $debug,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Debug failed',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_PRETTY_PRINT);
}

?>
