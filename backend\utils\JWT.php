<?php
/**
 * JWT (JSON Web Token) Utility Class
 * 
 * Simple JWT implementation for authentication tokens
 */

class JWT {
    
    /**
     * Generate JWT token
     * 
     * @param array $payload Token payload
     * @param string $secret Secret key
     * @param int $expiry Expiry time in seconds (default: 24 hours)
     * @return string JWT token
     */
    public static function encode($payload, $secret = null, $expiry = null) {
        $secret = $secret ?? (defined('JWT_SECRET') ? JWT_SECRET : 'default-secret');
        $expiry = $expiry ?? (defined('JWT_EXPIRY') ? JWT_EXPIRY : 86400);
        
        // Header
        $header = json_encode([
            'typ' => 'JWT',
            'alg' => 'HS256'
        ]);
        
        // Payload with expiry
        $payload['iat'] = time();
        $payload['exp'] = time() + $expiry;
        $payload = json_encode($payload);
        
        // Encode
        $headerEncoded = self::base64UrlEncode($header);
        $payloadEncoded = self::base64UrlEncode($payload);
        
        // Signature
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $secret, true);
        $signatureEncoded = self::base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }
    
    /**
     * Decode and verify JWT token
     * 
     * @param string $token JWT token
     * @param string $secret Secret key
     * @return array|false Decoded payload or false on failure
     */
    public static function decode($token, $secret = null) {
        $secret = $secret ?? (defined('JWT_SECRET') ? JWT_SECRET : 'default-secret');
        
        // Split token
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($headerEncoded, $payloadEncoded, $signatureEncoded) = $parts;
        
        // Decode header and payload
        $header = json_decode(self::base64UrlDecode($headerEncoded), true);
        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);
        
        if (!$header || !$payload) {
            return false;
        }
        
        // Verify signature
        $signature = self::base64UrlDecode($signatureEncoded);
        $expectedSignature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $secret, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return false;
        }
        
        // Check expiry
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return false;
        }
        
        return $payload;
    }
    
    /**
     * Verify if token is valid
     * 
     * @param string $token JWT token
     * @param string $secret Secret key
     * @return bool True if valid, false otherwise
     */
    public static function verify($token, $secret = null) {
        return self::decode($token, $secret) !== false;
    }
    
    /**
     * Get token from Authorization header
     * 
     * @return string|null Token or null if not found
     */
    public static function getTokenFromHeader() {
        $headers = getallheaders();
        
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
            if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
    
    /**
     * Get user ID from token
     * 
     * @param string $token JWT token
     * @return int|null User ID or null if not found
     */
    public static function getUserIdFromToken($token = null) {
        $token = $token ?? self::getTokenFromHeader();
        
        if (!$token) {
            return null;
        }
        
        $payload = self::decode($token);
        return $payload ? ($payload['user_id'] ?? null) : null;
    }
    
    /**
     * Get full payload from token
     * 
     * @param string $token JWT token
     * @return array|null Payload or null if invalid
     */
    public static function getPayloadFromToken($token = null) {
        $token = $token ?? self::getTokenFromHeader();
        
        if (!$token) {
            return null;
        }
        
        return self::decode($token);
    }
    
    /**
     * Refresh token (generate new token with same payload but extended expiry)
     * 
     * @param string $token Current token
     * @param int $expiry New expiry time in seconds
     * @return string|false New token or false on failure
     */
    public static function refresh($token, $expiry = null) {
        $payload = self::decode($token);
        
        if (!$payload) {
            return false;
        }
        
        // Remove old timestamps
        unset($payload['iat'], $payload['exp']);
        
        return self::encode($payload, null, $expiry);
    }
    
    /**
     * Base64 URL encode
     * 
     * @param string $data Data to encode
     * @return string Encoded data
     */
    private static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Base64 URL decode
     * 
     * @param string $data Data to decode
     * @return string Decoded data
     */
    private static function base64UrlDecode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
    
    /**
     * Generate a secure random secret key
     * 
     * @param int $length Key length
     * @return string Random key
     */
    public static function generateSecret($length = 64) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Create token for user
     * 
     * @param int $userId User ID
     * @param array $additionalData Additional data to include in token
     * @return string JWT token
     */
    public static function createUserToken($userId, $additionalData = []) {
        $payload = array_merge([
            'user_id' => $userId,
            'type' => 'access_token'
        ], $additionalData);
        
        return self::encode($payload);
    }
    
    /**
     * Create refresh token
     * 
     * @param int $userId User ID
     * @return string Refresh token
     */
    public static function createRefreshToken($userId) {
        $payload = [
            'user_id' => $userId,
            'type' => 'refresh_token'
        ];
        
        // Refresh tokens have longer expiry (30 days)
        return self::encode($payload, null, 30 * 24 * 60 * 60);
    }
    
    /**
     * Validate token type
     * 
     * @param string $token JWT token
     * @param string $expectedType Expected token type
     * @return bool True if token type matches
     */
    public static function validateTokenType($token, $expectedType) {
        $payload = self::decode($token);
        
        if (!$payload) {
            return false;
        }
        
        return isset($payload['type']) && $payload['type'] === $expectedType;
    }
}
?>
