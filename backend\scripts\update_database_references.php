<?php
/**
 * Update Database References Script
 * 
 * Replaces all LiteSpeedDatabase references with unified Database class
 */

echo "🔄 UPDATING DATABASE REFERENCES\n";
echo "===============================\n\n";

$filesToUpdate = [
    'backend/security/AdvancedSecurityManager.php',
    'backend/security/DataProtectionManager.php',
    'backend/security/SecurityMonitor.php',
    'backend/security/ThreatDetectionEngine.php',
    'backend/utils/helpers.php',
    'backend/controllers/AuthController.php',
    'backend/controllers/UserController.php',
    'backend/controllers/ChitController.php',
    'backend/controllers/MemberController.php',
    'backend/controllers/PaymentController.php',
    'backend/controllers/BiddingController.php',
    'backend/controllers/ReportController.php',
    'backend/controllers/NotificationController.php'
];

$oldPattern = "class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database()";
$newReplacement = "new Database(); // Unified database with smart LiteSpeed detection";

$totalReplacements = 0;

foreach ($filesToUpdate as $file) {
    if (!file_exists($file)) {
        echo "⚠️  File not found: $file\n";
        continue;
    }
    
    echo "Processing: $file\n";
    
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Replace the pattern
    $content = str_replace($oldPattern, $newReplacement, $content);
    
    // Count replacements
    $replacements = substr_count($originalContent, $oldPattern);
    
    if ($replacements > 0) {
        if (file_put_contents($file, $content)) {
            echo "  ✅ Updated $replacements references\n";
            $totalReplacements += $replacements;
        } else {
            echo "  ❌ Failed to write file\n";
        }
    } else {
        echo "  ℹ️  No references found\n";
    }
    
    echo "\n";
}

echo "🎉 UPDATE COMPLETE!\n";
echo "Total references updated: $totalReplacements\n";
echo "\n";

// Verify the changes
echo "🔍 VERIFICATION:\n";
echo "================\n";

foreach ($filesToUpdate as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $oldReferences = substr_count($content, 'LiteSpeedDatabase');
        $newReferences = substr_count($content, 'new Database()');
        
        echo sprintf("%-40s: Old refs: %d, New refs: %d\n", 
            basename($file), $oldReferences, $newReferences);
    }
}

echo "\n✅ All database references have been unified!\n";

?>
