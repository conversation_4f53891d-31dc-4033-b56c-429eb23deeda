import 'package:flutter/material.dart';
import '../../../core/models/chit_model.dart';

class ChitFilterChips extends StatelessWidget {
  final ChitStatus? selectedStatus;
  final Function(ChitStatus?) onStatusChanged;
  final String? searchQuery;
  final Function(String) onSearchChanged;

  const ChitFilterChips({
    super.key,
    this.selectedStatus,
    required this.onStatusChanged,
    this.searchQuery,
    required this.onSearchChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search Bar
          TextField(
            decoration: InputDecoration(
              hintText: 'Search chits...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surface,
            ),
            onChanged: onSearchChanged,
          ),
          
          const SizedBox(height: 16),
          
          // Status Filter Chips
          Text(
            'Filter by Status',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Wrap(
            spacing: 8,
            children: [
              FilterChip(
                label: const Text('All'),
                selected: selectedStatus == null,
                onSelected: (selected) {
                  onStatusChanged(null);
                },
              ),
              ...ChitStatus.values.map((status) => FilterChip(
                label: Text(status.displayName),
                selected: selectedStatus == status,
                onSelected: (selected) {
                  onStatusChanged(selected ? status : null);
                },
              )),
            ],
          ),
        ],
      ),
    );
  }
}

// Horizontal filter chips for compact layouts
class HorizontalChitFilters extends StatelessWidget {
  final ChitStatus? selectedStatus;
  final Function(ChitStatus?) onStatusChanged;

  const HorizontalChitFilters({
    super.key,
    this.selectedStatus,
    required this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildFilterChip(
            context,
            'All',
            selectedStatus == null,
            () => onStatusChanged(null),
          ),
          
          const SizedBox(width: 8),
          
          ...ChitStatus.values.map((status) => Padding(
            padding: const EdgeInsets.only(right: 8),
            child: _buildFilterChip(
              context,
              status.displayName,
              selectedStatus == status,
              () => onStatusChanged(selectedStatus == status ? null : status),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    BuildContext context,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      backgroundColor: Theme.of(context).colorScheme.surface,
      selectedColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).colorScheme.primary,
    );
  }
}

// Advanced filter dialog
class ChitFilterDialog extends StatefulWidget {
  final ChitStatus? initialStatus;
  final double? minAmount;
  final double? maxAmount;
  final int? minMembers;
  final int? maxMembers;
  final Function(ChitFilterOptions) onApply;

  const ChitFilterDialog({
    super.key,
    this.initialStatus,
    this.minAmount,
    this.maxAmount,
    this.minMembers,
    this.maxMembers,
    required this.onApply,
  });

  @override
  State<ChitFilterDialog> createState() => _ChitFilterDialogState();
}

class _ChitFilterDialogState extends State<ChitFilterDialog> {
  ChitStatus? _selectedStatus;
  double? _minAmount;
  double? _maxAmount;
  int? _minMembers;
  int? _maxMembers;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.initialStatus;
    _minAmount = widget.minAmount;
    _maxAmount = widget.maxAmount;
    _minMembers = widget.minMembers;
    _maxMembers = widget.maxMembers;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Chits'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Filter
            Text(
              'Status',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: _selectedStatus == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatus = null;
                    });
                  },
                ),
                ...ChitStatus.values.map((status) => FilterChip(
                  label: Text(status.displayName),
                  selected: _selectedStatus == status,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatus = selected ? status : null;
                    });
                  },
                )),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Amount Range
            Text(
              'Amount Range',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'Min Amount',
                      prefixText: '₹',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _minAmount = double.tryParse(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'Max Amount',
                      prefixText: '₹',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _maxAmount = double.tryParse(value);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Members Range
            Text(
              'Number of Members',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'Min Members',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _minMembers = int.tryParse(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'Max Members',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      _maxMembers = int.tryParse(value);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            setState(() {
              _selectedStatus = null;
              _minAmount = null;
              _maxAmount = null;
              _minMembers = null;
              _maxMembers = null;
            });
          },
          child: const Text('Clear'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApply(ChitFilterOptions(
              status: _selectedStatus,
              minAmount: _minAmount,
              maxAmount: _maxAmount,
              minMembers: _minMembers,
              maxMembers: _maxMembers,
            ));
            Navigator.of(context).pop();
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }
}

// Filter options data class
class ChitFilterOptions {
  final ChitStatus? status;
  final double? minAmount;
  final double? maxAmount;
  final int? minMembers;
  final int? maxMembers;

  const ChitFilterOptions({
    this.status,
    this.minAmount,
    this.maxAmount,
    this.minMembers,
    this.maxMembers,
  });

  bool get hasFilters =>
      status != null ||
      minAmount != null ||
      maxAmount != null ||
      minMembers != null ||
      maxMembers != null;

  bool matches(Chit chit) {
    if (status != null && chit.status != status) return false;
    if (minAmount != null && chit.totalAmount < minAmount!) return false;
    if (maxAmount != null && chit.totalAmount > maxAmount!) return false;
    if (minMembers != null && chit.numberOfMembers < minMembers!) return false;
    if (maxMembers != null && chit.numberOfMembers > maxMembers!) return false;
    return true;
  }
}
