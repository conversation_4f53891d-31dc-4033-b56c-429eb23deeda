<?php
/**
 * SMS Service - Handles SMS notifications
 */

require_once __DIR__ . '/../config/env.php';

class SMSService {
    
    /**
     * Send SMS notification
     */
    public static function sendSMS($phone, $message, $type = 'general') {
        if (!SMS_ENABLED) {
            error_log("SMS disabled - would send to $phone: $message");
            return true;
        }
        
        try {
            switch (SMS_PROVIDER) {
                case 'textlocal':
                    return self::sendTextLocal($phone, $message);
                case 'twilio':
                    return self::sendTwilio($phone, $message);
                case 'msg91':
                    return self::sendMsg91($phone, $message);
                default:
                    error_log("Unknown SMS provider: " . SMS_PROVIDER);
                    return false;
            }
        } catch (Exception $e) {
            error_log("SMS send error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send OTP SMS
     */
    public static function sendOTP($phone, $otp, $type = 'verification') {
        $message = "Your OTP for $type is: $otp. Valid for 10 minutes. Do not share with anyone.";
        return self::sendSMS($phone, $message, 'otp');
    }
    
    /**
     * Send payment reminder SMS
     */
    public static function sendPaymentReminder($phone, $chitName, $amount, $dueDate) {
        $message = "Payment reminder: ₹$amount due for $chitName on $dueDate. Please make payment on time.";
        return self::sendSMS($phone, $message, 'payment_reminder');
    }
    
    /**
     * Send bidding notification SMS
     */
    public static function sendBiddingNotification($phone, $chitName, $roundNumber) {
        $message = "Bidding started for $chitName - Round $roundNumber. Login to place your bid.";
        return self::sendSMS($phone, $message, 'bidding');
    }
    
    /**
     * Send TextLocal SMS
     */
    private static function sendTextLocal($phone, $message) {
        $apiKey = SMS_API_KEY;
        $sender = SMS_SENDER_ID;
        
        // Format phone number
        $phone = self::formatPhoneNumber($phone);
        
        $data = [
            'apikey' => $apiKey,
            'numbers' => $phone,
            'message' => $message,
            'sender' => $sender
        ];
        
        $url = 'https://api.textlocal.in/send/';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            $result = json_decode($response, true);
            if ($result && $result['status'] == 'success') {
                error_log("SMS sent successfully to $phone via TextLocal");
                return true;
            } else {
                error_log("TextLocal SMS error: " . ($result['errors'][0]['message'] ?? 'Unknown error'));
                return false;
            }
        } else {
            error_log("TextLocal HTTP error: $httpCode");
            return false;
        }
    }
    
    /**
     * Send Twilio SMS
     */
    private static function sendTwilio($phone, $message) {
        // Twilio implementation
        // Note: Requires Twilio PHP SDK
        error_log("Twilio SMS not implemented - would send to $phone: $message");
        return true;
    }
    
    /**
     * Send MSG91 SMS
     */
    private static function sendMsg91($phone, $message) {
        $apiKey = SMS_API_KEY;
        $sender = SMS_SENDER_ID;
        
        // Format phone number
        $phone = self::formatPhoneNumber($phone);
        
        $data = [
            'authkey' => $apiKey,
            'mobiles' => $phone,
            'message' => $message,
            'sender' => $sender,
            'route' => '4'
        ];
        
        $url = 'https://api.msg91.com/api/sendhttp.php';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            // MSG91 returns success codes differently
            if (strpos($response, 'success') !== false || is_numeric($response)) {
                error_log("SMS sent successfully to $phone via MSG91");
                return true;
            } else {
                error_log("MSG91 SMS error: $response");
                return false;
            }
        } else {
            error_log("MSG91 HTTP error: $httpCode");
            return false;
        }
    }
    
    /**
     * Format phone number for SMS
     */
    private static function formatPhoneNumber($phone) {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Add country code if not present (assuming India +91)
        if (strlen($phone) == 10) {
            $phone = '91' . $phone;
        } elseif (strlen($phone) == 11 && substr($phone, 0, 1) == '0') {
            $phone = '91' . substr($phone, 1);
        }
        
        return $phone;
    }
    
    /**
     * Validate phone number
     */
    public static function validatePhoneNumber($phone) {
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Check if it's a valid Indian mobile number
        if (strlen($phone) == 10 && preg_match('/^[6-9][0-9]{9}$/', $phone)) {
            return true;
        }
        
        // Check if it's a valid international number with country code
        if (strlen($phone) >= 10 && strlen($phone) <= 15) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Get SMS delivery status (if supported by provider)
     */
    public static function getDeliveryStatus($messageId) {
        // Implementation depends on SMS provider
        // This is a placeholder
        return [
            'status' => 'delivered',
            'delivered_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Get SMS balance (if supported by provider)
     */
    public static function getSMSBalance() {
        if (!SMS_ENABLED) {
            return ['balance' => 0, 'currency' => 'INR'];
        }
        
        // Implementation depends on SMS provider
        // This is a placeholder
        return [
            'balance' => 1000,
            'currency' => 'INR'
        ];
    }
    
    /**
     * Log SMS for audit trail
     */
    private static function logSMS($phone, $message, $status, $provider) {
        try {
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $sql = "INSERT INTO sms_logs (phone, message, status, provider, sent_at) VALUES (?, ?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$phone, $message, $status, $provider]);
            
        } catch (Exception $e) {
            error_log("SMS log error: " . $e->getMessage());
        }
    }
}
