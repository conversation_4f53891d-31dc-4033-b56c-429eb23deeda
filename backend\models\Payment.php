<?php
/**
 * Payment Model
 * 
 * Handles payment database operations
 */

class Payment {
    private $db;
    private $conn;
    
    // Payment properties
    public $id;
    public $chit_id;
    public $user_id;
    public $round_number;
    public $amount;
    public $due_date;
    public $paid_date;
    public $status;
    public $payment_type;
    public $payment_method;
    public $transaction_id;
    public $notes;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * Create a new payment
     * 
     * @return bool True on success, false on failure
     */
    public function create() {
        $sql = "INSERT INTO payments (chit_id, user_id, round_number, amount, due_date, 
                status, payment_type, payment_method, transaction_id, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            $result = $stmt->execute([
                $this->chit_id,
                $this->user_id,
                $this->round_number,
                $this->amount,
                $this->due_date,
                $this->status ?? 'pending',
                $this->payment_type ?? 'monthly_contribution',
                $this->payment_method,
                $this->transaction_id,
                $this->notes
            ]);
            
            if ($result) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Payment creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find payment by ID
     * 
     * @param int $id Payment ID
     * @return bool True if found, false otherwise
     */
    public function findById($id) {
        $sql = "SELECT * FROM payments WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find payment by ID failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update payment
     * 
     * @return bool True on success, false on failure
     */
    public function update() {
        $sql = "UPDATE payments SET amount = ?, due_date = ?, paid_date = ?, 
                status = ?, payment_method = ?, transaction_id = ?, notes = ?,
                updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            return $stmt->execute([
                $this->amount,
                $this->due_date,
                $this->paid_date,
                $this->status,
                $this->payment_method,
                $this->transaction_id,
                $this->notes,
                $this->id
            ]);
        } catch (PDOException $e) {
            error_log("Payment update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark payment as paid
     * 
     * @param string $paymentMethod Payment method
     * @param string $transactionId Transaction ID
     * @return bool True on success, false on failure
     */
    public function markAsPaid($paymentMethod = null, $transactionId = null) {
        $sql = "UPDATE payments SET status = 'paid', paid_date = CURRENT_DATE,
                payment_method = ?, transaction_id = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            return $stmt->execute([
                $paymentMethod,
                $transactionId,
                $this->id
            ]);
        } catch (PDOException $e) {
            error_log("Mark payment as paid failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get payments for a specific chit
     * 
     * @param int $chitId Chit ID
     * @return array Array of payments
     */
    public function getChitPayments($chitId) {
        $sql = "SELECT p.*, u.name as user_name, u.email, u.phone
                FROM payments p
                LEFT JOIN users u ON p.user_id = u.id
                WHERE p.chit_id = ?
                ORDER BY p.round_number DESC, p.created_at DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get chit payments failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get payments for a specific user
     * 
     * @param int $userId User ID
     * @param int $limit Limit number of results
     * @return array Array of payments
     */
    public function getUserPayments($userId, $limit = 50) {
        $sql = "SELECT p.*, c.name as chit_name, c.organizer_id
                FROM payments p
                LEFT JOIN chits c ON p.chit_id = c.id
                WHERE p.user_id = ?
                ORDER BY p.created_at DESC LIMIT ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get user payments failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get pending payments for a user
     * 
     * @param int $userId User ID
     * @return array Array of pending payments
     */
    public function getUserPendingPayments($userId) {
        $sql = "SELECT p.*, c.name as chit_name, c.organizer_id
                FROM payments p
                LEFT JOIN chits c ON p.chit_id = c.id
                WHERE p.user_id = ? AND p.status = 'pending'
                ORDER BY p.due_date ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get user pending payments failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get overdue payments for a user
     * 
     * @param int $userId User ID
     * @return array Array of overdue payments
     */
    public function getUserOverduePayments($userId) {
        $sql = "SELECT p.*, c.name as chit_name, c.organizer_id
                FROM payments p
                LEFT JOIN chits c ON p.chit_id = c.id
                WHERE p.user_id = ? AND p.status = 'pending' AND p.due_date < CURRENT_DATE
                ORDER BY p.due_date ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get user overdue payments failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get payment statistics for a chit
     * 
     * @param int $chitId Chit ID
     * @return array Payment statistics
     */
    public function getChitPaymentStats($chitId) {
        $sql = "SELECT 
                COUNT(*) as total_payments,
                SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_count,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = 'pending' AND due_date < CURRENT_DATE THEN 1 ELSE 0 END) as overdue_count,
                SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_collected,
                SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as total_pending
                FROM payments 
                WHERE chit_id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get chit payment stats failed: " . $e->getMessage());
            return [
                'total_payments' => 0,
                'paid_count' => 0,
                'pending_count' => 0,
                'overdue_count' => 0,
                'total_collected' => 0,
                'total_pending' => 0
            ];
        }
    }
    
    /**
     * Get payment statistics for a user
     * 
     * @param int $userId User ID
     * @return array Payment statistics
     */
    public function getUserPaymentStats($userId) {
        $sql = "SELECT 
                COUNT(*) as total_payments,
                SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_count,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = 'pending' AND due_date < CURRENT_DATE THEN 1 ELSE 0 END) as overdue_count,
                SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as total_pending
                FROM payments 
                WHERE user_id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get user payment stats failed: " . $e->getMessage());
            return [
                'total_payments' => 0,
                'paid_count' => 0,
                'pending_count' => 0,
                'overdue_count' => 0,
                'total_paid' => 0,
                'total_pending' => 0
            ];
        }
    }
    
    /**
     * Get payment details with related information
     * 
     * @return array Payment details
     */
    public function getDetails() {
        $sql = "SELECT p.*, u.name as user_name, u.email, u.phone,
                c.name as chit_name, c.organizer_id
                FROM payments p
                LEFT JOIN users u ON p.user_id = u.id
                LEFT JOIN chits c ON p.chit_id = c.id
                WHERE p.id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->id]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get payment details failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Delete payment
     * 
     * @return bool True on success, false on failure
     */
    public function delete() {
        $sql = "DELETE FROM payments WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Payment deletion failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Set object properties from database row
     * 
     * @param array $row Database row
     */
    private function setProperties($row) {
        $this->id = $row['id'];
        $this->chit_id = $row['chit_id'];
        $this->user_id = $row['user_id'];
        $this->round_number = $row['round_number'];
        $this->amount = $row['amount'];
        $this->due_date = $row['due_date'];
        $this->paid_date = $row['paid_date'];
        $this->status = $row['status'];
        $this->payment_type = $row['payment_type'];
        $this->payment_method = $row['payment_method'];
        $this->transaction_id = $row['transaction_id'];
        $this->notes = $row['notes'];
        $this->created_at = $row['created_at'];
        $this->updated_at = $row['updated_at'];
    }
}
?>
