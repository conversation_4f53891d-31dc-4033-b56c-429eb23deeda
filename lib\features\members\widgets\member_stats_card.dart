import 'package:flutter/material.dart';
import '../../../core/models/chit_model.dart';

class MemberStatsCard extends StatelessWidget {
  final Chit chit;

  const MemberStatsCard({
    super.key,
    required this.chit,
  });

  @override
  Widget build(BuildContext context) {
    final totalMembers = chit.numberOfMembers;
    final activeMembers = chit.members.where((m) => m.isActive).length;
    final winners = chit.members.where((m) => m.hasWon).length;
    final organizers = chit.members.where((m) => m.role == MemberRole.organizer).length;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Member Statistics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Stats Grid
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total',
                    '$totalMembers',
                    Icons.group,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Active',
                    '$activeMembers',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Winners',
                    '$winners',
                    Icons.emoji_events,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Organizers',
                    '$organizers',
                    Icons.admin_panel_settings,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Progress Indicators
            _buildProgressSection(context, totalMembers, activeMembers, winners),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        
        const SizedBox(height: 2),
        
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildProgressSection(
    BuildContext context,
    int totalMembers,
    int activeMembers,
    int winners,
  ) {
    final activePercentage = totalMembers > 0 ? activeMembers / totalMembers : 0.0;
    final completionPercentage = totalMembers > 0 ? winners / totalMembers : 0.0;

    return Column(
      children: [
        // Active Members Progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Active Members',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(activePercentage * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.green,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 4),
        
        LinearProgressIndicator(
          value: activePercentage,
          backgroundColor: Colors.green.withValues(alpha: 0.2),
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
        ),
        
        const SizedBox(height: 16),
        
        // Completion Progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Chit Completion',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(completionPercentage * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 4),
        
        LinearProgressIndicator(
          value: completionPercentage,
          backgroundColor: Colors.orange.withValues(alpha: 0.2),
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
        ),
      ],
    );
  }
}

// Compact version for dashboard
class CompactMemberStats extends StatelessWidget {
  final Chit chit;

  const CompactMemberStats({
    super.key,
    required this.chit,
  });

  @override
  Widget build(BuildContext context) {
    final totalMembers = chit.numberOfMembers;
    final activeMembers = chit.members.where((m) => m.isActive).length;
    final winners = chit.members.where((m) => m.hasWon).length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: _buildCompactStat(
                context,
                'Members',
                '$activeMembers/$totalMembers',
                Icons.group,
                Colors.blue,
              ),
            ),
            
            Container(
              width: 1,
              height: 40,
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            ),
            
            Expanded(
              child: _buildCompactStat(
                context,
                'Winners',
                '$winners',
                Icons.emoji_events,
                Colors.orange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactStat(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        
        const SizedBox(height: 4),
        
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }
}

// Member role distribution chart
class MemberRoleChart extends StatelessWidget {
  final Chit chit;

  const MemberRoleChart({
    super.key,
    required this.chit,
  });

  @override
  Widget build(BuildContext context) {
    final organizers = chit.members.where((m) => m.role == MemberRole.organizer).length;
    final members = chit.members.where((m) => m.role == MemberRole.member).length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Role Distribution',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  flex: organizers,
                  child: Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.purple,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                
                const SizedBox(width: 4),
                
                Expanded(
                  flex: members,
                  child: Container(
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                _buildLegendItem(context, 'Organizers', organizers, Colors.purple),
                const SizedBox(width: 16),
                _buildLegendItem(context, 'Members', members, Colors.blue),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(BuildContext context, String label, int count, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        
        const SizedBox(width: 6),
        
        Text(
          '$label ($count)',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
