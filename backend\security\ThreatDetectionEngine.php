<?php
/**
 * Advanced Threat Detection Engine
 * 
 * AI-powered threat detection with machine learning-like capabilities
 * - Behavioral analysis
 * - Anomaly detection
 * - Pattern recognition
 * - Predictive threat modeling
 */

class ThreatDetectionEngine {
    
    private static $behaviorBaselines = [];
    private static $anomalyThresholds = [
        'login_frequency' => 10,
        'transaction_amount' => 50000,
        'api_calls_per_minute' => 100,
        'failed_attempts' => 5,
        'unusual_hours' => [22, 6], // 10 PM to 6 AM
        'geo_distance_km' => 1000
    ];
    
    /**
     * Analyze user behavior patterns
     */
    public static function analyzeBehaviorPattern($userId, $request) {
        $score = 0;
        $factors = [];
        
        // Get user's historical behavior
        $baseline = self::getUserBehaviorBaseline($userId);
        
        // 1. Login time analysis
        $timeAnomaly = self::analyzeLoginTime($userId, $baseline);
        $score += $timeAnomaly['score'];
        $factors[] = $timeAnomaly;
        
        // 2. Request frequency analysis
        $frequencyAnomaly = self::analyzeRequestFrequency($userId, $baseline);
        $score += $frequencyAnomaly['score'];
        $factors[] = $frequencyAnomaly;
        
        // 3. Navigation pattern analysis
        $navigationAnomaly = self::analyzeNavigationPattern($userId, $request, $baseline);
        $score += $navigationAnomaly['score'];
        $factors[] = $navigationAnomaly;
        
        // 4. Device behavior analysis
        $deviceAnomaly = self::analyzeDeviceBehavior($userId, $baseline);
        $score += $deviceAnomaly['score'];
        $factors[] = $deviceAnomaly;
        
        return [
            'type' => 'behavior_analysis',
            'score' => min($score, 100), // Cap at 100
            'factors' => $factors,
            'baseline_available' => !empty($baseline)
        ];
    }
    
    /**
     * Geolocation threat analysis
     */
    public static function analyzeGeolocation($userId) {
        $score = 0;
        $factors = [];
        
        $currentIP = $_SERVER['REMOTE_ADDR'] ?? null;
        if (!$currentIP) {
            return ['type' => 'geolocation', 'score' => 0, 'factors' => ['No IP available']];
        }
        
        // Get geolocation data
        $currentLocation = self::getIPGeolocation($currentIP);
        $userLocations = self::getUserLocationHistory($userId);
        
        // 1. Check for VPN/Proxy
        if (self::isVPNOrProxy($currentIP)) {
            $score += 30;
            $factors[] = 'VPN/Proxy detected';
        }
        
        // 2. Check for suspicious countries
        if (self::isSuspiciousCountry($currentLocation['country'] ?? '')) {
            $score += 25;
            $factors[] = 'High-risk country: ' . ($currentLocation['country'] ?? 'Unknown');
        }
        
        // 3. Check for impossible travel
        $impossibleTravel = self::detectImpossibleTravel($userId, $currentLocation, $userLocations);
        if ($impossibleTravel['detected']) {
            $score += 40;
            $factors[] = 'Impossible travel detected: ' . $impossibleTravel['details'];
        }
        
        // 4. Check for new location
        if (!self::isKnownLocation($userId, $currentLocation)) {
            $score += 15;
            $factors[] = 'New location detected';
        }
        
        return [
            'type' => 'geolocation',
            'score' => min($score, 100),
            'factors' => $factors,
            'location' => $currentLocation
        ];
    }
    
    /**
     * Device fingerprinting and analysis
     */
    public static function analyzeDeviceFingerprint($userId) {
        $score = 0;
        $factors = [];
        
        $currentFingerprint = self::generateDeviceFingerprint();
        $knownDevices = self::getUserDevices($userId);
        
        // 1. Check if device is known
        if (!self::isKnownDevice($userId, $currentFingerprint)) {
            $score += 20;
            $factors[] = 'Unknown device detected';
        }
        
        // 2. Check for device spoofing indicators
        $spoofingIndicators = self::detectDeviceSpoofing($currentFingerprint);
        if (!empty($spoofingIndicators)) {
            $score += 35;
            $factors[] = 'Device spoofing indicators: ' . implode(', ', $spoofingIndicators);
        }
        
        // 3. Check for automation tools
        if (self::detectAutomationTools()) {
            $score += 40;
            $factors[] = 'Automation tools detected';
        }
        
        // 4. Check for suspicious user agent
        if (self::isSuspiciousUserAgent($_SERVER['HTTP_USER_AGENT'] ?? '')) {
            $score += 15;
            $factors[] = 'Suspicious user agent';
        }
        
        return [
            'type' => 'device_fingerprint',
            'score' => min($score, 100),
            'factors' => $factors,
            'fingerprint' => $currentFingerprint
        ];
    }
    
    /**
     * Time-based pattern analysis
     */
    public static function analyzeTimePattern($userId) {
        $score = 0;
        $factors = [];
        
        $currentHour = (int)date('H');
        $currentDay = date('w'); // 0 = Sunday, 6 = Saturday
        
        // Get user's typical activity times
        $activityPattern = self::getUserActivityPattern($userId);
        
        // 1. Check for unusual hours
        if ($currentHour >= self::$anomalyThresholds['unusual_hours'][0] || 
            $currentHour <= self::$anomalyThresholds['unusual_hours'][1]) {
            
            if (!self::isTypicalActivityTime($userId, $currentHour, $activityPattern)) {
                $score += 20;
                $factors[] = 'Activity during unusual hours';
            }
        }
        
        // 2. Check for weekend activity (if user typically doesn't work weekends)
        if (($currentDay == 0 || $currentDay == 6) && 
            !self::isTypicalWeekendActivity($userId, $activityPattern)) {
            $score += 10;
            $factors[] = 'Unusual weekend activity';
        }
        
        // 3. Check for rapid successive logins
        $recentLogins = self::getRecentLogins($userId, 300); // Last 5 minutes
        if (count($recentLogins) > 3) {
            $score += 25;
            $factors[] = 'Multiple rapid logins detected';
        }
        
        return [
            'type' => 'time_pattern',
            'score' => min($score, 100),
            'factors' => $factors,
            'current_time' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Request pattern analysis
     */
    public static function analyzeRequestPattern($userId, $request) {
        $score = 0;
        $factors = [];
        
        // 1. Check for unusual API endpoint access
        $endpoint = $request['endpoint'] ?? $_SERVER['REQUEST_URI'] ?? '';
        if (self::isUnusualEndpoint($userId, $endpoint)) {
            $score += 15;
            $factors[] = 'Unusual API endpoint access';
        }
        
        // 2. Check for parameter tampering
        $tamperingDetected = self::detectParameterTampering($request);
        if ($tamperingDetected) {
            $score += 30;
            $factors[] = 'Parameter tampering detected';
        }
        
        // 3. Check for request size anomalies
        $requestSize = strlen(json_encode($request));
        if ($requestSize > 10000) { // 10KB threshold
            $score += 10;
            $factors[] = 'Unusually large request size';
        }
        
        // 4. Check for suspicious headers
        $suspiciousHeaders = self::detectSuspiciousHeaders();
        if (!empty($suspiciousHeaders)) {
            $score += 20;
            $factors[] = 'Suspicious headers: ' . implode(', ', $suspiciousHeaders);
        }
        
        return [
            'type' => 'request_pattern',
            'score' => min($score, 100),
            'factors' => $factors,
            'request_size' => $requestSize
        ];
    }
    
    /**
     * Advanced XSS detection with context awareness
     */
    public static function detectAdvancedXSS($input) {
        $threats = [];
        $confidence = 0;
        
        // Advanced XSS patterns
        $xssPatterns = [
            // Script injections
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi' => 90,
            '/javascript\s*:/i' => 80,
            '/vbscript\s*:/i' => 80,
            '/data\s*:\s*text\/html/i' => 70,
            
            // Event handlers
            '/on\w+\s*=\s*["\']?[^"\'>\s]+/i' => 85,
            '/on(load|error|click|focus|blur|change|submit)\s*=/i' => 90,
            
            // DOM manipulation
            '/document\.(write|writeln|createElement)/i' => 75,
            '/window\.(location|open)/i' => 70,
            '/eval\s*\(/i' => 95,
            '/setTimeout\s*\(/i' => 60,
            '/setInterval\s*\(/i' => 60,
            
            // HTML injections
            '/<(iframe|object|embed|applet|meta|link)\b/i' => 80,
            '/<img[^>]+src\s*=\s*["\']?javascript:/i' => 95,
            
            // CSS injections
            '/expression\s*\(/i' => 85,
            '/@import\s+["\']?javascript:/i' => 90,
            
            // Advanced evasion techniques
            '/&#x?[0-9a-f]+;/i' => 40, // HTML entities
            '/%[0-9a-f]{2}/i' => 30, // URL encoding
            '/\\\\u[0-9a-f]{4}/i' => 50, // Unicode escapes
        ];
        
        foreach ($xssPatterns as $pattern => $threatLevel) {
            if (preg_match($pattern, $input)) {
                $threats[] = [
                    'type' => 'XSS',
                    'pattern' => $pattern,
                    'threat_level' => $threatLevel
                ];
                $confidence = max($confidence, $threatLevel);
            }
        }
        
        return [
            'safe' => empty($threats),
            'threats' => $threats,
            'confidence' => $confidence,
            'type' => 'advanced_xss'
        ];
    }
    
    /**
     * Advanced SQL injection detection
     */
    public static function detectAdvancedSQLInjection($input) {
        $threats = [];
        $confidence = 0;
        
        // Advanced SQL injection patterns
        $sqlPatterns = [
            // Union-based injections
            '/\bunion\b.+\bselect\b/i' => 95,
            '/\bunion\b.+\bfrom\b/i' => 90,
            
            // Boolean-based blind injections
            '/\b(and|or)\b\s+\d+\s*=\s*\d+/i' => 80,
            '/\b(and|or)\b\s+["\']?\w+["\']?\s*=\s*["\']?\w+["\']?/i' => 75,
            
            // Time-based blind injections
            '/\b(sleep|benchmark|waitfor)\s*\(/i' => 95,
            '/\bif\s*\(.+,\s*(sleep|benchmark)/i' => 90,
            
            // Error-based injections
            '/\b(extractvalue|updatexml|exp)\s*\(/i' => 85,
            '/\bcast\s*\(.+\bas\s+/i' => 70,
            
            // Stacked queries
            '/;\s*(insert|update|delete|drop|create|alter)\b/i' => 95,
            '/;\s*exec\b/i' => 90,
            
            // Comment-based evasion
            '/\/\*.*?\*\//s' => 60,
            '/--\s*.*$/m' => 65,
            '/#.*$/m' => 50,
            
            // Function-based injections
            '/\b(concat|group_concat|load_file|into\s+outfile)\b/i' => 80,
            '/\b(user|database|version|@@version)\s*\(\s*\)/i' => 75,
            
            // Advanced evasion
            '/\bchar\s*\(\s*\d+/i' => 70,
            '/0x[0-9a-f]+/i' => 60,
        ];
        
        foreach ($sqlPatterns as $pattern => $threatLevel) {
            if (preg_match($pattern, $input)) {
                $threats[] = [
                    'type' => 'SQL_INJECTION',
                    'pattern' => $pattern,
                    'threat_level' => $threatLevel
                ];
                $confidence = max($confidence, $threatLevel);
            }
        }
        
        return [
            'safe' => empty($threats),
            'threats' => $threats,
            'confidence' => $confidence,
            'type' => 'advanced_sql_injection'
        ];
    }
    
    /**
     * Command injection detection
     */
    public static function detectCommandInjection($input) {
        $threats = [];
        $confidence = 0;
        
        $cmdPatterns = [
            '/[;&|`$(){}[\]]/i' => 70,
            '/\b(exec|system|shell_exec|passthru|eval)\s*\(/i' => 95,
            '/\b(cat|ls|dir|type|copy|move|del|rm|mkdir|rmdir)\b/i' => 80,
            '/\b(wget|curl|nc|netcat|telnet|ssh)\b/i' => 85,
            '/\b(chmod|chown|sudo|su)\b/i' => 90,
        ];
        
        foreach ($cmdPatterns as $pattern => $threatLevel) {
            if (preg_match($pattern, $input)) {
                $threats[] = [
                    'type' => 'COMMAND_INJECTION',
                    'pattern' => $pattern,
                    'threat_level' => $threatLevel
                ];
                $confidence = max($confidence, $threatLevel);
            }
        }
        
        return [
            'safe' => empty($threats),
            'threats' => $threats,
            'confidence' => $confidence,
            'type' => 'command_injection'
        ];
    }
    
    /**
     * Path traversal detection
     */
    public static function detectPathTraversal($input) {
        $threats = [];
        $confidence = 0;
        
        $pathPatterns = [
            '/\.\.\//' => 80,
            '/\.\.\\\\/' => 80,
            '/%2e%2e%2f/i' => 85,
            '/%2e%2e%5c/i' => 85,
            '/\.\.\%2f/' => 85,
            '/\.\.\%5c/' => 85,
        ];
        
        foreach ($pathPatterns as $pattern => $threatLevel) {
            if (preg_match($pattern, $input)) {
                $threats[] = [
                    'type' => 'PATH_TRAVERSAL',
                    'pattern' => $pattern,
                    'threat_level' => $threatLevel
                ];
                $confidence = max($confidence, $threatLevel);
            }
        }
        
        return [
            'safe' => empty($threats),
            'threats' => $threats,
            'confidence' => $confidence,
            'type' => 'path_traversal'
        ];
    }
    
    // Helper methods for behavior analysis
    private static function getUserBehaviorBaseline($userId) {
        // Implementation would fetch user's historical behavior patterns
        return [];
    }
    
    private static function generateDeviceFingerprint() {
        $fingerprint = [
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            'accept_encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            'accept' => $_SERVER['HTTP_ACCEPT'] ?? '',
        ];
        
        return hash('sha256', json_encode($fingerprint));
    }
    
    private static function getIPGeolocation($ip) {
        // Implementation would use a geolocation service
        return ['country' => 'Unknown', 'city' => 'Unknown', 'lat' => 0, 'lon' => 0];
    }
    
    /**
     * Analyze login time patterns
     */
    private static function analyzeLoginTime($userId, $baseline) {
        $score = 0;
        $factors = [];

        $currentHour = (int)date('H');

        // Check for unusual hours
        if ($currentHour < 6 || $currentHour > 22) {
            $score += 20;
            $factors[] = 'Login during unusual hours';
        }

        return [
            'type' => 'login_time',
            'score' => $score,
            'factors' => $factors
        ];
    }

    /**
     * Analyze request frequency
     */
    private static function analyzeRequestFrequency($userId, $baseline) {
        $score = 0;
        $factors = [];

        // Basic frequency analysis
        $score += 5;
        $factors[] = 'Normal request frequency';

        return [
            'type' => 'request_frequency',
            'score' => $score,
            'factors' => $factors
        ];
    }

    /**
     * Analyze navigation patterns
     */
    private static function analyzeNavigationPattern($userId, $request, $baseline) {
        $score = 0;
        $factors = [];

        // Basic navigation analysis
        $endpoint = $request['endpoint'] ?? '';
        if (strpos($endpoint, 'admin') !== false) {
            $score += 10;
            $factors[] = 'Admin endpoint access';
        }

        return [
            'type' => 'navigation_pattern',
            'score' => $score,
            'factors' => $factors
        ];
    }

    /**
     * Analyze device behavior
     */
    private static function analyzeDeviceBehavior($userId, $baseline) {
        $score = 0;
        $factors = [];

        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($userAgent)) {
            $score += 15;
            $factors[] = 'Missing user agent';
        }

        return [
            'type' => 'device_behavior',
            'score' => $score,
            'factors' => $factors
        ];
    }

    /**
     * Check if VPN or Proxy
     */
    private static function isVPNOrProxy($ip) {
        // Basic check - in production, use a VPN detection service
        $suspiciousRanges = ['10.', '192.168.', '172.'];

        foreach ($suspiciousRanges as $range) {
            if (strpos($ip, $range) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if suspicious country
     */
    private static function isSuspiciousCountry($country) {
        $suspiciousCountries = ['XX', 'ZZ']; // Placeholder
        return in_array($country, $suspiciousCountries);
    }

    /**
     * Detect impossible travel
     */
    private static function detectImpossibleTravel($userId, $currentLocation, $userLocations) {
        // Basic impossible travel detection
        return [
            'detected' => false,
            'details' => 'No impossible travel detected'
        ];
    }

    /**
     * Check if known location
     */
    private static function isKnownLocation($userId, $location) {
        // Basic location check
        return true; // Assume known for now
    }

    /**
     * Check if known device
     */
    private static function isKnownDevice($userId, $fingerprint) {
        // Basic device check
        return true; // Assume known for now
    }

    /**
     * Detect device spoofing
     */
    private static function detectDeviceSpoofing($fingerprint) {
        // Basic spoofing detection
        return []; // No spoofing detected
    }

    /**
     * Detect automation tools
     */
    private static function detectAutomationTools() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        $automationIndicators = [
            'selenium', 'phantomjs', 'headless', 'bot', 'crawler'
        ];

        foreach ($automationIndicators as $indicator) {
            if (stripos($userAgent, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if suspicious user agent
     */
    private static function isSuspiciousUserAgent($userAgent) {
        if (empty($userAgent)) {
            return true;
        }

        $suspiciousPatterns = [
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/bot/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get user activity pattern
     */
    private static function getUserActivityPattern($userId) {
        // Basic activity pattern
        return [
            'typical_hours' => [9, 10, 11, 12, 13, 14, 15, 16, 17],
            'weekend_activity' => false
        ];
    }

    /**
     * Check if typical activity time
     */
    private static function isTypicalActivityTime($userId, $hour, $pattern) {
        return in_array($hour, $pattern['typical_hours'] ?? []);
    }

    /**
     * Check if typical weekend activity
     */
    private static function isTypicalWeekendActivity($userId, $pattern) {
        return $pattern['weekend_activity'] ?? false;
    }

    /**
     * Get recent logins
     */
    private static function getRecentLogins($userId, $seconds) {
        // Basic recent logins check
        return []; // No recent logins for now
    }

    /**
     * Check if unusual endpoint
     */
    private static function isUnusualEndpoint($userId, $endpoint) {
        $adminEndpoints = ['/admin', '/api/admin', '/management'];

        foreach ($adminEndpoints as $adminEndpoint) {
            if (strpos($endpoint, $adminEndpoint) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Detect parameter tampering
     */
    private static function detectParameterTampering($request) {
        // Basic parameter tampering detection
        if (isset($request['id']) && !is_numeric($request['id'])) {
            return true;
        }

        return false;
    }

    /**
     * Detect suspicious headers
     */
    private static function detectSuspiciousHeaders() {
        $suspiciousHeaders = [];

        // Check for missing common headers
        if (!isset($_SERVER['HTTP_ACCEPT'])) {
            $suspiciousHeaders[] = 'Missing Accept header';
        }

        if (!isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $suspiciousHeaders[] = 'Missing Accept-Language header';
        }

        return $suspiciousHeaders;
    }

    /**
     * Get user location history
     */
    private static function getUserLocationHistory($userId) {
        // Basic location history
        return [];
    }

    /**
     * Get user devices
     */
    private static function getUserDevices($userId) {
        // Basic device list
        return [];
    }
}

?>
