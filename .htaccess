# ================================================================
# OPTIMIZED .HTACCESS FOR LITESPEED SHARED HOSTING
# Chit Fund App - Laravel + API Configuration
# ================================================================

# ================================================================
# LITESPEED CACHE CONFIGURATION
# ================================================================
<IfModule Litespeed>
    # Enable LiteSpeed Cache
    CacheLookup on
    
    # Cache static files
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        CacheEnable on
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </FilesMatch>
    
    # Don't cache API endpoints
    <FilesMatch "^api/">
        CacheLookup off
    </FilesMatch>
</IfModule>

# ================================================================
# LARAVEL FRAMEWORK CONFIGURATION
# ================================================================
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# ================================================================
# SECURITY HEADERS
# ================================================================
<IfModule mod_headers.c>
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # CORS Headers for API
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization"
    Header always set Access-Control-Max-Age "3600"
    
    # Handle preflight OPTIONS requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# ================================================================
# COMPRESSION SETTINGS
# ================================================================
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# ================================================================
# BROWSER CACHING
# ================================================================
<IfModule mod_expires.c>
    ExpiresActive on

    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"

    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"

    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"

    # Documents
    ExpiresByType application/pdf "access plus 1 month"

    # API responses (short cache)
    ExpiresByType application/json "access plus 5 minutes"
</IfModule>

# ================================================================
# FILE PROTECTION
# ================================================================
# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

<Files "package.json">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# Protect Laravel directories
<IfModule mod_rewrite.c>
    RewriteRule ^storage/ - [F,L]
    RewriteRule ^bootstrap/ - [F,L]
    RewriteRule ^config/ - [F,L]
    RewriteRule ^database/ - [F,L]
    RewriteRule ^resources/ - [F,L]
    RewriteRule ^routes/ - [F,L]
    RewriteRule ^tests/ - [F,L]
    RewriteRule ^vendor/ - [F,L]
</IfModule>

# ================================================================
# PHP SETTINGS OVERRIDE
# ================================================================
<IfModule mod_php.c>
    # Error reporting
    php_flag display_errors On
    php_flag log_errors On
    php_value error_log "logs/php_errors.log"
    
    # Memory and execution limits
    php_value memory_limit 256M
    php_value max_execution_time 60
    php_value max_input_time 60
    
    # Upload settings
    php_value upload_max_filesize 16M
    php_value post_max_size 32M
    php_value max_file_uploads 20
    
    # Session settings
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# ================================================================
# LITESPEED SPECIFIC OPTIMIZATIONS
# ================================================================
<IfModule Litespeed>
    # Enable ESI (Edge Side Includes)
    RewriteRule .* - [E=esi_on:1]
    
    # Cache control for API
    <LocationMatch "^/api/">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </LocationMatch>
    
    # Cache static assets
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=2592000"
    </LocationMatch>
</IfModule>

# ================================================================
# ERROR PAGES
# ================================================================
ErrorDocument 400 /error/400.html
ErrorDocument 401 /error/401.html
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html
ErrorDocument 502 /error/502.html
ErrorDocument 503 /error/503.html

# ================================================================
# ADDITIONAL SECURITY
# ================================================================
# Disable server signature
ServerTokens Prod

# Prevent access to .htaccess files
<Files ~ "^\.ht">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# ================================================================
# API RATE LIMITING (If supported by hosting)
# ================================================================
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# ================================================================
# LOGGING CONFIGURATION
# ================================================================
<IfModule mod_log_config.c>
    # Custom log format for API requests
    LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" %D" combined_with_time
    
    # Log API requests separately (if supported)
    SetEnvIf Request_URI "^/api/" api_request
    CustomLog logs/api_requests.log combined_with_time env=api_request
</IfModule>

# ================================================================
# END OF CONFIGURATION
# ================================================================
