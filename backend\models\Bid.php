<?php
/**
 * Bid Model
 * 
 * Handles bid database operations
 */

class Bid {
    private $db;
    private $conn;
    
    // Bid properties
    public $id;
    public $bidding_round_id;
    public $user_id;
    public $bid_amount;
    public $bid_time;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * Create a new bid
     * 
     * @return bool True on success, false on failure
     */
    public function create() {
        $sql = "INSERT INTO bids (bidding_round_id, user_id, bid_amount, bid_time) 
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            $result = $stmt->execute([
                $this->bidding_round_id,
                $this->user_id,
                $this->bid_amount
            ]);
            
            if ($result) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Bid creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find bid by ID
     * 
     * @param int $id Bid ID
     * @return bool True if found, false otherwise
     */
    public function findById($id) {
        $sql = "SELECT * FROM bids WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find bid by ID failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update bid
     * 
     * @return bool True on success, false on failure
     */
    public function update() {
        $sql = "UPDATE bids SET bid_amount = ?, bid_time = CURRENT_TIMESTAMP, 
                updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            return $stmt->execute([
                $this->bid_amount,
                $this->id
            ]);
        } catch (PDOException $e) {
            error_log("Bid update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user's bid for a specific bidding round
     * 
     * @param int $biddingRoundId Bidding round ID
     * @param int $userId User ID
     * @return array|null Bid details or null
     */
    public function getUserBid($biddingRoundId, $userId) {
        $sql = "SELECT b.*, u.name, u.email
                FROM bids b
                LEFT JOIN users u ON b.user_id = u.id
                WHERE b.bidding_round_id = ? AND b.user_id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$biddingRoundId, $userId]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get user bid failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get all bids for a bidding round
     * 
     * @param int $biddingRoundId Bidding round ID
     * @return array Array of bids
     */
    public function getRoundBids($biddingRoundId) {
        $sql = "SELECT b.*, u.name, u.email, u.profile_image
                FROM bids b
                LEFT JOIN users u ON b.user_id = u.id
                WHERE b.bidding_round_id = ?
                ORDER BY b.bid_amount ASC, b.bid_time ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$biddingRoundId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get round bids failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get winning bid (lowest amount, earliest time)
     * 
     * @param int $biddingRoundId Bidding round ID
     * @return array|null Winning bid details or null
     */
    public function getWinningBid($biddingRoundId) {
        $sql = "SELECT b.*, u.name, u.email, u.profile_image
                FROM bids b
                LEFT JOIN users u ON b.user_id = u.id
                WHERE b.bidding_round_id = ?
                ORDER BY b.bid_amount ASC, b.bid_time ASC
                LIMIT 1";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$biddingRoundId]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get winning bid failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get bid statistics for a bidding round
     * 
     * @param int $biddingRoundId Bidding round ID
     * @return array Bid statistics
     */
    public function getBidStatistics($biddingRoundId) {
        $sql = "SELECT 
                COUNT(*) as total_bids,
                MIN(bid_amount) as lowest_bid,
                MAX(bid_amount) as highest_bid,
                AVG(bid_amount) as average_bid
                FROM bids 
                WHERE bidding_round_id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$biddingRoundId]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get bid statistics failed: " . $e->getMessage());
            return [
                'total_bids' => 0,
                'lowest_bid' => 0,
                'highest_bid' => 0,
                'average_bid' => 0
            ];
        }
    }
    
    /**
     * Get user's bidding history
     * 
     * @param int $userId User ID
     * @param int $limit Limit number of results
     * @return array Array of user's bids
     */
    public function getUserBiddingHistory($userId, $limit = 50) {
        $sql = "SELECT b.*, br.round_number, br.status as round_status,
                c.name as chit_name, br.winner_id,
                (CASE WHEN br.winner_id = b.user_id THEN 1 ELSE 0 END) as is_winner
                FROM bids b
                LEFT JOIN bidding_rounds br ON b.bidding_round_id = br.id
                LEFT JOIN chits c ON br.chit_id = c.id
                WHERE b.user_id = ?
                ORDER BY b.created_at DESC LIMIT ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get user bidding history failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get bid details with user and round information
     * 
     * @return array Bid details
     */
    public function getDetails() {
        $sql = "SELECT b.*, u.name, u.email, u.profile_image,
                br.round_number, br.status as round_status,
                c.name as chit_name
                FROM bids b
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN bidding_rounds br ON b.bidding_round_id = br.id
                LEFT JOIN chits c ON br.chit_id = c.id
                WHERE b.id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->id]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get bid details failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Delete bid
     * 
     * @return bool True on success, false on failure
     */
    public function delete() {
        $sql = "DELETE FROM bids WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Bid deletion failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get top bidders for a chit
     * 
     * @param int $chitId Chit ID
     * @param int $limit Limit number of results
     * @return array Array of top bidders
     */
    public function getTopBidders($chitId, $limit = 10) {
        $sql = "SELECT u.id, u.name, u.email, u.profile_image,
                COUNT(b.id) as total_bids,
                AVG(b.bid_amount) as average_bid,
                MIN(b.bid_amount) as lowest_bid,
                SUM(CASE WHEN br.winner_id = u.id THEN 1 ELSE 0 END) as wins
                FROM users u
                INNER JOIN bids b ON u.id = b.user_id
                INNER JOIN bidding_rounds br ON b.bidding_round_id = br.id
                WHERE br.chit_id = ?
                GROUP BY u.id, u.name, u.email, u.profile_image
                ORDER BY wins DESC, average_bid ASC
                LIMIT ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId, $limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get top bidders failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Set object properties from database row
     * 
     * @param array $row Database row
     */
    private function setProperties($row) {
        $this->id = $row['id'];
        $this->bidding_round_id = $row['bidding_round_id'];
        $this->user_id = $row['user_id'];
        $this->bid_amount = $row['bid_amount'];
        $this->bid_time = $row['bid_time'];
        $this->created_at = $row['created_at'];
        $this->updated_at = $row['updated_at'];
    }
}
?>
