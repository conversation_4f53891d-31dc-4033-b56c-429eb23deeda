# LiteSpeed Shared Hosting Setup Guide
## Optimized Configuration for Chit Fund App

### 📋 **QUICK SETUP CHECKLIST**

#### **1. Upload php.ini File**
```bash
# Upload php.ini to your public_html root directory
/home/<USER>/public_html/php.ini
```

#### **2. Create Required Directories**
```bash
# Create logs directory
mkdir -p /home/<USER>/public_html/logs
chmod 755 /home/<USER>/public_html/logs

# Create cache directory (if needed)
mkdir -p /home/<USER>/public_html/storage/cache
chmod 755 /home/<USER>/public_html/storage/cache
```

#### **3. Update File Paths**
Edit the php.ini file and update these paths with your actual hosting paths:
```ini
; Update this line with your actual username
error_log = /home/<USER>/public_html/logs/php_errors.log
```

---

### 🔧 **KEY OPTIMIZATIONS INCLUDED**

#### **Performance Settings:**
- ✅ **Memory Limit**: 256MB (shared hosting friendly)
- ✅ **Execution Time**: 60 seconds (API friendly)
- ✅ **OPcache Enabled**: 128MB cache for faster PHP execution
- ✅ **Compression**: Gzip enabled for faster responses
- ✅ **Realpath Cache**: 4MB cache for file path resolution

#### **API & Database Optimizations:**
- ✅ **Post Max Size**: 32MB for file uploads
- ✅ **Max Input Vars**: 3000 for complex forms
- ✅ **JSON Precision**: Optimized for API responses
- ✅ **MySQL Connections**: Optimized connection pooling
- ✅ **Session Security**: Secure cookie settings

#### **Logging & Debugging:**
- ✅ **Error Logging**: Comprehensive error tracking
- ✅ **API Request Logging**: Detailed request/response logging
- ✅ **Custom Log Location**: Organized log files
- ✅ **Debug Mode**: Enabled for development

#### **Security Features:**
- ✅ **Disabled Functions**: Dangerous functions disabled
- ✅ **Secure Sessions**: HTTPOnly and Secure cookies
- ✅ **Hidden PHP Version**: PHP version not exposed
- ✅ **Input Validation**: Enhanced input filtering

---

### 📁 **DIRECTORY STRUCTURE**

```
/home/<USER>/public_html/
├── php.ini                 # Main PHP configuration
├── .htaccess              # Apache/LiteSpeed rules
├── index.php              # Laravel entry point
├── logs/                  # Custom log directory
│   ├── php_errors.log     # PHP error log
│   ├── api_requests.log   # API request log
│   └── laravel.log        # Laravel application log
├── storage/               # Laravel storage
│   ├── cache/             # Application cache
│   ├── logs/              # Laravel logs
│   └── sessions/          # Session files
└── vendor/                # Composer dependencies
```

---

### 🚀 **PERFORMANCE MONITORING**

#### **Check PHP Configuration:**
```php
<?php
// Create info.php file to verify settings
phpinfo();
?>
```

#### **Monitor Error Logs:**
```bash
# Watch PHP errors in real-time
tail -f /home/<USER>/public_html/logs/php_errors.log

# Check Laravel logs
tail -f /home/<USER>/public_html/storage/logs/laravel.log
```

#### **Test API Performance:**
```bash
# Test API endpoint response time
curl -w "@curl-format.txt" -o /dev/null -s "https://chit.mobunite.com/api/chits"
```

---

### 🔍 **TROUBLESHOOTING COMMON ISSUES**

#### **1. 500 Internal Server Error**
```bash
# Check PHP error log
tail -20 /home/<USER>/public_html/logs/php_errors.log

# Check Laravel error log
tail -20 /home/<USER>/public_html/storage/logs/laravel.log
```

#### **2. Memory Limit Issues**
```ini
; Increase memory limit if needed
memory_limit = 512M
```

#### **3. Execution Timeout**
```ini
; Increase execution time for complex operations
max_execution_time = 120
```

#### **4. Upload Issues**
```ini
; Increase upload limits
upload_max_filesize = 32M
post_max_size = 64M
```

---

### 📊 **MONITORING & ANALYTICS**

#### **Key Metrics to Monitor:**
- ✅ **Response Time**: API endpoint response times
- ✅ **Memory Usage**: PHP memory consumption
- ✅ **Error Rate**: 4xx/5xx error frequency
- ✅ **Database Performance**: Query execution times
- ✅ **Cache Hit Rate**: OPcache effectiveness

#### **Log Analysis Commands:**
```bash
# Count API requests by endpoint
grep "POST /api" logs/access.log | cut -d' ' -f7 | sort | uniq -c

# Find slow queries
grep "slow" logs/mysql.log

# Monitor error patterns
grep "ERROR" logs/php_errors.log | tail -20
```

---

### 🔒 **SECURITY CONSIDERATIONS**

#### **Production Security Settings:**
```ini
; For production, change these settings:
display_errors = Off
display_startup_errors = Off
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT & ~E_NOTICE
```

#### **File Permissions:**
```bash
# Set secure permissions
chmod 644 php.ini
chmod 755 logs/
chmod 644 logs/*.log
```

---

### 📞 **HOSTING PROVIDER SPECIFIC NOTES**

#### **Common Shared Hosting Providers:**

**cPanel/WHM:**
- Upload php.ini to public_html root
- Use File Manager or FTP
- Check PHP version in cPanel

**Plesk:**
- Upload to httpdocs directory
- Use PHP settings in Plesk panel
- Enable error logging in Plesk

**DirectAdmin:**
- Upload to public_html
- Check PHP configuration in DA panel
- Monitor logs through DA interface

---

### 🎯 **OPTIMIZATION RESULTS EXPECTED**

With this configuration, you should see:
- ✅ **50-70% faster** PHP execution (OPcache)
- ✅ **30-40% smaller** response sizes (compression)
- ✅ **Better error tracking** for debugging
- ✅ **Improved API performance** for Flutter app
- ✅ **Enhanced security** for production use

---

### 📝 **NEXT STEPS**

1. **Upload php.ini** to your hosting account
2. **Create log directories** with proper permissions
3. **Update file paths** with your actual hosting paths
4. **Test API endpoints** to verify configuration
5. **Monitor logs** for any issues
6. **Optimize further** based on actual usage patterns

This configuration is specifically optimized for your Chit Fund app's requirements and should resolve the 500 errors while providing excellent performance on LiteSpeed shared hosting.
