<?php
/**
 * File Controller - Handles file uploads and management
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/env.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../utils/JWT.php';
require_once __DIR__ . '/../models/FileUpload.php';

class FileController {
    
    /**
     * Upload profile image
     */
    public function uploadProfileImage() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!isset($_FILES['profile_image'])) {
            sendError('No file uploaded', 400);
        }
        
        $file = $_FILES['profile_image'];
        
        // Validate file
        $validation = validateFileUpload($file, ['image/jpeg', 'image/jpg', 'image/png'], UPLOAD_MAX_SIZE);
        
        if (!$validation['valid']) {
            sendError('File validation failed: ' . implode(', ', $validation['errors']), 400);
        }
        
        try {
            // Create upload directory if it doesn't exist
            $uploadDir = UPLOAD_PATH . 'profiles/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileName = 'profile_' . $userId . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . $fileName;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                sendError('Failed to save file', 500);
            }
            
            // Save file record to database
            $fileUpload = new FileUpload();
            $fileUpload->user_id = $userId;
            $fileUpload->original_name = $file['name'];
            $fileUpload->file_name = $fileName;
            $fileUpload->file_path = 'uploads/profiles/' . $fileName;
            $fileUpload->file_size = $file['size'];
            $fileUpload->mime_type = $file['type'];
            $fileUpload->file_type = 'profile_image';
            
            if ($fileUpload->save()) {
                // Update user profile image
                $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
                $conn = $db->getConnection();
                
                $stmt = $conn->prepare("UPDATE users SET profile_image = ? WHERE id = ?");
                $stmt->execute([$fileUpload->file_path, $userId]);
                
                // Log activity
                logActivity($userId, 'profile_image_uploaded', [
                    'file_id' => $fileUpload->id,
                    'file_name' => $fileName
                ]);
                
                sendSuccess([
                    'image_url' => $fileUpload->file_path,
                    'file_id' => $fileUpload->id
                ], 'Profile image uploaded successfully');
            } else {
                // Clean up file if database save failed
                unlink($filePath);
                sendError('Failed to save file record', 500);
            }
            
        } catch (Exception $e) {
            error_log("Upload profile image error: " . $e->getMessage());
            sendError('Failed to upload image', 500);
        }
    }
    
    /**
     * Upload payment proof
     */
    public function uploadPaymentProof() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        if (!isset($_FILES['payment_proof']) || !isset($data['payment_id'])) {
            sendError('Missing file or payment ID', 400);
        }
        
        $file = $_FILES['payment_proof'];
        $paymentId = $data['payment_id'];
        
        // Validate file
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        $validation = validateFileUpload($file, $allowedTypes, UPLOAD_MAX_SIZE);
        
        if (!$validation['valid']) {
            sendError('File validation failed: ' . implode(', ', $validation['errors']), 400);
        }
        
        try {
            // Create upload directory
            $uploadDir = UPLOAD_PATH . 'payments/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileName = 'payment_' . $paymentId . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . $fileName;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                sendError('Failed to save file', 500);
            }
            
            // Save file record
            $fileUpload = new FileUpload();
            $fileUpload->user_id = $userId;
            $fileUpload->original_name = $file['name'];
            $fileUpload->file_name = $fileName;
            $fileUpload->file_path = 'uploads/payments/' . $fileName;
            $fileUpload->file_size = $file['size'];
            $fileUpload->mime_type = $file['type'];
            $fileUpload->file_type = 'payment_proof';
            
            if ($fileUpload->save()) {
                // Update payment record with proof
                $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
                $conn = $db->getConnection();
                
                $stmt = $conn->prepare("UPDATE payments SET proof_file_id = ? WHERE id = ? AND user_id = ?");
                $stmt->execute([$fileUpload->id, $paymentId, $userId]);
                
                // Log activity
                logActivity($userId, 'payment_proof_uploaded', [
                    'payment_id' => $paymentId,
                    'file_id' => $fileUpload->id
                ]);
                
                sendSuccess([
                    'file_url' => $fileUpload->file_path,
                    'file_id' => $fileUpload->id
                ], 'Payment proof uploaded successfully');
            } else {
                unlink($filePath);
                sendError('Failed to save file record', 500);
            }
            
        } catch (Exception $e) {
            error_log("Upload payment proof error: " . $e->getMessage());
            sendError('Failed to upload payment proof', 500);
        }
    }
    
    /**
     * Get user files
     */
    public function getUserFiles() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $fileUpload = new FileUpload();
            $files = $fileUpload->getUserFiles($userId);
            
            sendSuccess($files, 'Files retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get user files error: " . $e->getMessage());
            sendError('Failed to retrieve files', 500);
        }
    }
    
    /**
     * Delete file
     */
    public function deleteFile() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        if (!isset($data['file_id'])) {
            sendError('File ID required', 400);
        }
        
        try {
            $fileUpload = new FileUpload();
            
            if (!$fileUpload->findById($data['file_id'])) {
                sendError('File not found', 404);
            }
            
            // Check ownership
            if ($fileUpload->user_id != $userId) {
                sendError('Access denied', 403);
            }
            
            // Delete physical file
            $fullPath = UPLOAD_PATH . '../' . $fileUpload->file_path;
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
            
            // Delete database record
            if ($fileUpload->delete()) {
                // Log activity
                logActivity($userId, 'file_deleted', [
                    'file_id' => $fileUpload->id,
                    'file_name' => $fileUpload->file_name
                ]);
                
                sendSuccess(null, 'File deleted successfully');
            } else {
                sendError('Failed to delete file record', 500);
            }
            
        } catch (Exception $e) {
            error_log("Delete file error: " . $e->getMessage());
            sendError('Failed to delete file', 500);
        }
    }
}
