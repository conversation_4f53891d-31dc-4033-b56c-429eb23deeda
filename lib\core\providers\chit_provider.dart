import 'package:flutter/foundation.dart';
import '../models/chit_model.dart';
import '../models/api_response.dart';
import '../services/storage_service.dart';
import '../services/chit_service.dart';

class ChitProvider extends ChangeNotifier {
  List<Chit> _chits = [];
  Chit? _selectedChit;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Chit> get chits => _chits;
  Chit? get selectedChit => _selectedChit;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Filtered chits
  List<Chit> get activeChits => _chits.where((chit) => chit.status == ChitStatus.active).toList();
  List<Chit> get completedChits => _chits.where((chit) => chit.status == ChitStatus.completed).toList();
  List<Chit> get myChits => _chits.where((chit) => chit.isOwner).toList();
  List<Chit> get participatingChits => _chits.where((chit) => !chit.isOwner).toList();

  // Initialize chits from local storage
  Future<void> initializeChits() async {
    try {
      _setLoading(true);
      
      final chitData = StorageService.getAllChits();
      _chits = chitData.map((data) => Chit.fromJson(data)).toList();
      
      _clearError();
    } catch (e) {
      _setError('Failed to load chits: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create a new chit
  Future<bool> createChit({
    required String name,
    required String description,
    required double totalAmount,
    required int numberOfMembers,
    required ChitFrequency frequency,
    required DateTime startDate,
    double? commissionPercentage,
    bool isDraft = false,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await ChitService.createChit(
        name: name,
        description: description,
        totalAmount: totalAmount,
        numberOfMembers: numberOfMembers,
        frequency: frequency,
        startDate: startDate,
        commissionPercentage: commissionPercentage,
        isDraft: isDraft,
      );

      return result.when(
        success: (chit) {
          _chits.add(chit);
          notifyListeners();
          return true;
        },
        error: (error) {
          _setError(error);
          return false;
        },
      );
    } catch (e) {
      _setError('Failed to create chit: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Join a chit
  Future<bool> joinChit(String chitCode) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await ChitService.joinChit(chitCode);

      return result.when(
        success: (_) {
          // Refresh chits to get the updated list
          refreshChits();
          return true;
        },
        error: (error) {
          _setError(error);
          return false;
        },
      );
    } catch (e) {
      _setError('Failed to join chit: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get chit details
  Future<bool> getChitDetails(String chitId) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await ChitService.getChitDetails(chitId);

      return result.when(
        success: (chit) {
          // Update existing chit or add new one
          final index = _chits.indexWhere((c) => c.id == chitId);
          if (index != -1) {
            _chits[index] = chit;
          } else {
            _chits.add(chit);
          }
          notifyListeners();
          return true;
        },
        error: (error) {
          _setError(error);
          return false;
        },
      );
    } catch (e) {
      _setError('Failed to get chit details: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Add a new chit
  Future<void> addChit(Chit chit) async {
    try {
      _setLoading(true);
      
      // Add to local list
      _chits.add(chit);
      
      // Save to local storage
      await StorageService.saveChitData(chit.id, chit.toJson());
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add chit: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update an existing chit
  Future<void> updateChit(Chit updatedChit) async {
    try {
      _setLoading(true);
      
      final index = _chits.indexWhere((chit) => chit.id == updatedChit.id);
      if (index != -1) {
        _chits[index] = updatedChit;
        
        // Update local storage
        await StorageService.saveChitData(updatedChit.id, updatedChit.toJson());
        
        // Update selected chit if it's the same
        if (_selectedChit?.id == updatedChit.id) {
          _selectedChit = updatedChit;
        }
        
        _clearError();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update chit: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Remove a chit
  Future<void> removeChit(String chitId) async {
    try {
      _setLoading(true);
      
      _chits.removeWhere((chit) => chit.id == chitId);
      
      // Remove from local storage
      await StorageService.removeChitData(chitId);
      
      // Clear selected chit if it's the same
      if (_selectedChit?.id == chitId) {
        _selectedChit = null;
      }
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to remove chit: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Select a chit
  void selectChit(Chit chit) {
    _selectedChit = chit;
    notifyListeners();
  }

  // Clear selected chit
  void clearSelectedChit() {
    _selectedChit = null;
    notifyListeners();
  }

  // Get chit by ID
  Chit? getChitById(String chitId) {
    try {
      return _chits.firstWhere((chit) => chit.id == chitId);
    } catch (e) {
      return null;
    }
  }

  // Search chits
  List<Chit> searchChits(String query) {
    if (query.isEmpty) return _chits;
    
    final lowercaseQuery = query.toLowerCase();
    return _chits.where((chit) {
      return chit.name.toLowerCase().contains(lowercaseQuery) ||
             chit.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Filter chits by status
  List<Chit> getChitsByStatus(ChitStatus status) {
    return _chits.where((chit) => chit.status == status).toList();
  }

  // Get chits by frequency
  List<Chit> getChitsByFrequency(ChitFrequency frequency) {
    return _chits.where((chit) => chit.frequency == frequency).toList();
  }

  // Get upcoming chits (next meeting within 7 days)
  List<Chit> getUpcomingChits() {
    final now = DateTime.now();
    final nextWeek = now.add(const Duration(days: 7));
    
    return _chits.where((chit) {
      if (chit.status != ChitStatus.active) return false;
      
      final nextMeeting = chit.getNextMeetingDate();
      return nextMeeting != null && 
             nextMeeting.isAfter(now) && 
             nextMeeting.isBefore(nextWeek);
    }).toList();
  }

  // Get overdue chits
  List<Chit> getOverdueChits() {
    final now = DateTime.now();
    
    return _chits.where((chit) {
      if (chit.status != ChitStatus.active) return false;
      
      final nextMeeting = chit.getNextMeetingDate();
      return nextMeeting != null && nextMeeting.isBefore(now);
    }).toList();
  }

  // Refresh chits from server
  Future<void> refreshChits() async {
    try {
      _setLoading(true);

      final result = await ChitService.getChits();

      if (result.isSuccess) {
        _chits = result.data!;

        // Update local storage
        for (final chit in _chits) {
          await StorageService.saveChitData(chit.id, chit.toJson());
        }

        _clearError();
        notifyListeners();
      } else {
        _setError(result.error ?? 'Failed to refresh chits');
      }

    } catch (e) {
      _setError('Failed to refresh chits: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Clear all chits
  Future<void> clearAllChits() async {
    try {
      _setLoading(true);
      
      _chits.clear();
      _selectedChit = null;
      
      await StorageService.clearAllChits();
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear chits: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update chit status
  Future<Result<Chit>> updateChitStatus(String chitId, ChitStatus status) async {
    try {
      _setLoading(true);

      final result = await ChitService.updateChit(
        chitId: chitId,
        status: status,
      );

      if (result.isSuccess) {
        // Update local chit
        final chitIndex = _chits.indexWhere((chit) => chit.id == chitId);
        if (chitIndex != -1) {
          _chits[chitIndex] = result.data!;

          // Update selected chit if it's the same
          if (_selectedChit?.id == chitId) {
            _selectedChit = result.data!;
          }

          // Save to local storage
          StorageService.saveChitData(result.data!.id, result.data!.toJson());
        }

        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Unknown error');
        return result;
      }
    } catch (e) {
      final error = 'Failed to update chit status: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Delete chit
  Future<Result<void>> deleteChit(String chitId) async {
    try {
      _setLoading(true);

      // For now, we'll use the update API to mark as deleted
      // In a real app, you'd have a proper delete endpoint
      final result = await ChitService.updateChit(
        chitId: chitId,
        status: ChitStatus.cancelled,
      );

      if (result.isSuccess) {
        // Remove from local list
        _chits.removeWhere((chit) => chit.id == chitId);

        // Clear selected chit if it's the deleted one
        if (_selectedChit?.id == chitId) {
          _selectedChit = null;
        }

        // Remove from local storage
        StorageService.removeChitData(chitId);

        _clearError();
        return const Success(null);
      } else {
        _setError(result.error ?? 'Unknown error');
        return Error(result.error ?? 'Unknown error');
      }
    } catch (e) {
      final error = 'Failed to delete chit: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Get statistics
  Map<String, dynamic> getStatistics() {
    return {
      'totalChits': _chits.length,
      'activeChits': activeChits.length,
      'completedChits': completedChits.length,
      'myChits': myChits.length,
      'participatingChits': participatingChits.length,
      'upcomingChits': getUpcomingChits().length,
      'overdueChits': getOverdueChits().length,
    };
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }
}
