<?php
/**
 * Database Setup Script - Create missing tables
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔧 Chit Fund Database Setup\n";
echo "==========================\n\n";

try {
    // Include configuration
    if (file_exists(__DIR__ . '/config/env.php')) {
        require_once __DIR__ . '/config/env.php';
        echo "✅ Configuration loaded\n";
    } else {
        throw new Exception("Configuration file not found");
    }
    
    // Include database
    require_once __DIR__ . '/config/database.php';
    
    $db = new Database();
    $conn = $db->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    echo "✅ Database connected\n";
    
    // Check and create OTPs table
    echo "\n📧 Setting up OTP table...\n";
    
    $sql = "CREATE TABLE IF NOT EXISTS otps (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        otp VARCHAR(6) NOT NULL,
        type ENUM('registration', 'forgot_password') NOT NULL,
        verified BOOLEAN DEFAULT FALSE,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        verified_at TIMESTAMP NULL,
        INDEX idx_email_type (email, type),
        INDEX idx_otp (otp),
        INDEX idx_expires (expires_at)
    )";
    
    if ($conn->exec($sql) !== false) {
        echo "✅ OTPs table created/verified\n";
    } else {
        throw new Exception("Failed to create OTPs table");
    }
    
    // Test OTP table
    echo "\n🧪 Testing OTP functionality...\n";
    
    $email = '<EMAIL>';
    $otp = '123456';
    $type = 'registration';
    $expires_at = date('Y-m-d H:i:s', time() + 600);
    
    // Clean any existing test data
    $stmt = $conn->prepare("DELETE FROM otps WHERE email = ?");
    $stmt->execute([$email]);
    
    // Insert test OTP
    $stmt = $conn->prepare("INSERT INTO otps (email, otp, type, expires_at) VALUES (?, ?, ?, ?)");
    if ($stmt->execute([$email, $otp, $type, $expires_at])) {
        echo "✅ OTP insert test successful\n";
        
        // Verify we can read it back
        $stmt = $conn->prepare("SELECT * FROM otps WHERE email = ? AND otp = ?");
        $stmt->execute([$email, $otp]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo "✅ OTP read test successful\n";
        } else {
            echo "❌ OTP read test failed\n";
        }
        
        // Clean up test data
        $stmt = $conn->prepare("DELETE FROM otps WHERE email = ?");
        $stmt->execute([$email]);
        echo "✅ Test data cleaned up\n";
        
    } else {
        echo "❌ OTP insert test failed\n";
    }
    
    // Check other essential tables
    echo "\n📋 Checking other tables...\n";
    
    $tables = ['users', 'chits', 'chit_members', 'payments', 'bidding_rounds', 'bids'];
    foreach ($tables as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "✅ Table '$table' exists\n";
        } else {
            echo "⚠️  Table '$table' missing\n";
        }
    }
    
    echo "\n🎉 Database setup completed!\n";
    echo "\nYou can now test the OTP registration flow.\n";
    
} catch (Exception $e) {
    echo "\n❌ Setup failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

?>
