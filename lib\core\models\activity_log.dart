import 'package:json_annotation/json_annotation.dart';

part 'activity_log.g.dart';

@JsonSerializable()
class ActivityLog {
  final int id;
  final int? userId;
  final int? chitId;
  final String action;
  final Map<String, dynamic>? details;
  final String? ipAddress;
  final String? userAgent;
  final DateTime createdAt;

  const ActivityLog({
    required this.id,
    this.userId,
    this.chitId,
    required this.action,
    this.details,
    this.ipAddress,
    this.userAgent,
    required this.createdAt,
  });

  factory ActivityLog.fromJson(Map<String, dynamic> json) => _$ActivityLogFromJson(json);
  Map<String, dynamic> toJson() => _$ActivityLogToJson(this);

  ActivityLog copyWith({
    int? id,
    int? userId,
    int? chitId,
    String? action,
    Map<String, dynamic>? details,
    String? ipAddress,
    String? userAgent,
    DateTime? createdAt,
  }) {
    return ActivityLog(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      chitId: chitId ?? this.chitId,
      action: action ?? this.action,
      details: details ?? this.details,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Get formatted action description
  String get formattedAction {
    switch (action) {
      case 'user_login':
        return 'User logged in';
      case 'user_logout':
        return 'User logged out';
      case 'user_registered':
        return 'User registered';
      case 'chit_created':
        return 'Chit fund created';
      case 'chit_updated':
        return 'Chit fund updated';
      case 'member_joined':
        return 'Member joined chit';
      case 'member_removed':
        return 'Member removed from chit';
      case 'bidding_started':
        return 'Bidding round started';
      case 'bid_placed':
        return 'Bid placed';
      case 'bidding_completed':
        return 'Bidding round completed';
      case 'payment_made':
        return 'Payment made';
      case 'payment_received':
        return 'Payment received';
      case 'notification_sent':
        return 'Notification sent';
      case 'password_changed':
        return 'Password changed';
      case 'profile_updated':
        return 'Profile updated';
      default:
        return action.replaceAll('_', ' ').toUpperCase();
    }
  }

  // Get activity category
  ActivityCategory get category {
    if (action.startsWith('user_')) {
      return ActivityCategory.user;
    } else if (action.startsWith('chit_')) {
      return ActivityCategory.chit;
    } else if (action.startsWith('member_')) {
      return ActivityCategory.member;
    } else if (action.startsWith('bidding_') || action.startsWith('bid_')) {
      return ActivityCategory.bidding;
    } else if (action.startsWith('payment_')) {
      return ActivityCategory.payment;
    } else if (action.startsWith('notification_')) {
      return ActivityCategory.notification;
    } else {
      return ActivityCategory.system;
    }
  }

  // Get activity severity
  ActivitySeverity get severity {
    switch (action) {
      case 'user_login':
      case 'user_logout':
      case 'bid_placed':
      case 'payment_made':
        return ActivitySeverity.info;
      case 'user_registered':
      case 'chit_created':
      case 'member_joined':
      case 'bidding_started':
      case 'bidding_completed':
        return ActivitySeverity.success;
      case 'member_removed':
      case 'password_changed':
        return ActivitySeverity.warning;
      case 'login_failed':
      case 'payment_failed':
        return ActivitySeverity.error;
      default:
        return ActivitySeverity.info;
    }
  }

  // Check if activity is recent (within last hour)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 1;
  }

  // Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivityLog && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ActivityLog(id: $id, action: $action, createdAt: $createdAt)';
  }
}

enum ActivityCategory {
  user,
  chit,
  member,
  bidding,
  payment,
  notification,
  system,
}

enum ActivitySeverity {
  info,
  success,
  warning,
  error,
}

extension ActivityCategoryExtension on ActivityCategory {
  String get displayName {
    switch (this) {
      case ActivityCategory.user:
        return 'User';
      case ActivityCategory.chit:
        return 'Chit Fund';
      case ActivityCategory.member:
        return 'Member';
      case ActivityCategory.bidding:
        return 'Bidding';
      case ActivityCategory.payment:
        return 'Payment';
      case ActivityCategory.notification:
        return 'Notification';
      case ActivityCategory.system:
        return 'System';
    }
  }
}

extension ActivitySeverityExtension on ActivitySeverity {
  String get displayName {
    switch (this) {
      case ActivitySeverity.info:
        return 'Info';
      case ActivitySeverity.success:
        return 'Success';
      case ActivitySeverity.warning:
        return 'Warning';
      case ActivitySeverity.error:
        return 'Error';
    }
  }
}
