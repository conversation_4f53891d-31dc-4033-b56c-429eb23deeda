import 'package:flutter/material.dart';

class CurrentBidDisplay extends StatelessWidget {
  final double? currentBid;
  final String? currentWinner;
  final double totalAmount;

  const CurrentBidDisplay({
    super.key,
    this.currentBid,
    this.currentWinner,
    required this.totalAmount,
  });

  @override
  Widget build(BuildContext context) {
    final hasCurrentBid = currentBid != null && currentWinner != null;
    final bidPercentage = hasCurrentBid ? (currentBid! / totalAmount) : 0.0;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: hasCurrentBid
              ? [Colors.green.shade400, Colors.green.shade600]
              : [Colors.grey.shade400, Colors.grey.shade600],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (hasCurrentBid ? Colors.green : Colors.grey).withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Text(
            hasCurrentBid ? 'Current Highest Bid' : 'No Bids Yet',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (hasCurrentBid) ...[
            // Current Bid Amount
            Text(
              '₹${_formatAmount(currentBid!)}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 36,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Winner Name
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.emoji_events,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  currentWinner!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Bid Progress
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${(bidPercentage * 100).toStringAsFixed(1)}% of total',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '₹${_formatAmount(totalAmount)}',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),
                
                LinearProgressIndicator(
                  value: bidPercentage,
                  backgroundColor: Colors.white.withValues(alpha: 0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ],
            ),
          ] else ...[
            // No Bids State
            Icon(
              Icons.gavel,
              color: Colors.white.withValues(alpha: 0.7),
              size: 48,
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Be the first to place a bid!',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 16,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Minimum bid: ₹${_formatAmount(totalAmount * 0.1)}',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontSize: 14,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}

// Compact version for smaller spaces
class CompactCurrentBidDisplay extends StatelessWidget {
  final double? currentBid;
  final String? currentWinner;

  const CompactCurrentBidDisplay({
    super.key,
    this.currentBid,
    this.currentWinner,
  });

  @override
  Widget build(BuildContext context) {
    final hasCurrentBid = currentBid != null && currentWinner != null;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              hasCurrentBid ? Icons.emoji_events : Icons.gavel,
              color: hasCurrentBid ? Colors.green : Colors.grey,
              size: 24,
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    hasCurrentBid ? 'Current Leader' : 'No Bids Yet',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  
                  if (hasCurrentBid)
                    Text(
                      currentWinner!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  else
                    Text(
                      'Waiting for first bid',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                ],
              ),
            ),
            
            if (hasCurrentBid)
              Text(
                '₹${_formatAmount(currentBid!)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}

// Animated bid display with effects
class AnimatedCurrentBidDisplay extends StatefulWidget {
  final double? currentBid;
  final String? currentWinner;
  final double totalAmount;

  const AnimatedCurrentBidDisplay({
    super.key,
    this.currentBid,
    this.currentWinner,
    required this.totalAmount,
  });

  @override
  State<AnimatedCurrentBidDisplay> createState() => _AnimatedCurrentBidDisplayState();
}

class _AnimatedCurrentBidDisplayState extends State<AnimatedCurrentBidDisplay>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    if (widget.currentBid != null) {
      _pulseController.repeat(reverse: true);
      _scaleController.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedCurrentBidDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.currentBid != oldWidget.currentBid && widget.currentBid != null) {
      _scaleController.reset();
      _scaleController.forward();
      
      if (!_pulseController.isAnimating) {
        _pulseController.repeat(reverse: true);
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final hasCurrentBid = widget.currentBid != null && widget.currentWinner != null;

    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _scaleAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: hasCurrentBid ? _pulseAnimation.value : 1.0,
          child: Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: hasCurrentBid
                    ? [Colors.green.shade400, Colors.green.shade600]
                    : [Colors.grey.shade400, Colors.grey.shade600],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: (hasCurrentBid ? Colors.green : Colors.grey).withValues(alpha: 0.3),
                  blurRadius: hasCurrentBid ? 15 : 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Transform.scale(
              scale: hasCurrentBid ? _scaleAnimation.value : 1.0,
              child: CurrentBidDisplay(
                currentBid: widget.currentBid,
                currentWinner: widget.currentWinner,
                totalAmount: widget.totalAmount,
              ),
            ),
          ),
        );
      },
    );
  }
}
