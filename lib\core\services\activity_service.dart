import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/activity_log.dart';
import 'api_service.dart';
import 'storage_service.dart';

class ActivityService {
  static late Dio _dio;

  static void initialize() {
    ApiService.initialize();
    _dio = Dio(BaseOptions(
      baseUrl: ApiService.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token to requests
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) {
          handler.next(error);
        },
      ),
    );
  }

  /// Get recent activities
  static Future<Result<List<ActivityLog>>> getRecentActivities({
    int limit = 10,
    int offset = 0,
  }) async {
    try {
      final response = await _dio.get('/activity', queryParameters: {
        'limit': limit,
        'offset': offset,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<ActivityLog>>.fromJson(
          response.data,
          (json) => (json as List)
              .map((item) => ActivityLog.fromJson(item as Map<String, dynamic>))
              .toList(),
        );

        if (apiResponse.isSuccess) {
          return Success(apiResponse.data!);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch activities');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get user activities
  static Future<Result<List<ActivityLog>>> getUserActivities({
    String? userId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
        'offset': offset,
      };

      if (userId != null) {
        queryParams['user_id'] = userId;
      }

      // Get current user ID from storage
      final currentUserId = userId ?? await _getCurrentUserId();
      final response = await _dio.get('/users/$currentUserId/activity', queryParameters: queryParams);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<ActivityLog>>.fromJson(
          response.data,
          (json) => (json as List)
              .map((item) => ActivityLog.fromJson(item as Map<String, dynamic>))
              .toList(),
        );

        if (apiResponse.isSuccess) {
          return Success(apiResponse.data!);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch user activities');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get chit activities
  static Future<Result<List<ActivityLog>>> getChitActivities({
    required String chitId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _dio.get('/activity/chit/$chitId', queryParameters: {
        'limit': limit,
        'offset': offset,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<ActivityLog>>.fromJson(
          response.data,
          (json) => (json as List)
              .map((item) => ActivityLog.fromJson(item as Map<String, dynamic>))
              .toList(),
        );

        if (apiResponse.isSuccess) {
          return Success(apiResponse.data!);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch chit activities');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Log activity (for client-side logging)
  static Future<Result<void>> logActivity({
    required String action,
    Map<String, dynamic>? details,
    String? chitId,
  }) async {
    try {
      final data = {
        'action': action,
        'details': details,
        'chit_id': chitId,
      };

      final response = await _dio.post('/activity/log', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to log activity');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle Dio errors
  static String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Unknown error';
        return 'Server error ($statusCode): $message';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.unknown:
        return 'Network error. Please check your internet connection.';
      default:
        return 'An unexpected error occurred';
    }
  }

  /// Get current user ID from storage
  static Future<String> _getCurrentUserId() async {
    final userId = StorageService.getUserId();
    if (userId != null) {
      return userId;
    }
    throw Exception('User not found in storage');
  }
}
