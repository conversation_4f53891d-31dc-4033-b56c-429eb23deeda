#!/bin/bash

# ================================================================
# LITESPEED SHARED HOSTING DEPLOYMENT SCRIPT
# Chit Fund App - Automated Setup
# ================================================================

echo "🚀 LiteSpeed Shared Hosting Deployment Script"
echo "=============================================="

# Configuration variables (UPDATE THESE)
HOSTING_USERNAME="your_username"
HOSTING_PATH="/home/<USER>/public_html"
DOMAIN="chit.mobunite.com"

echo ""
echo "📋 Configuration:"
echo "   Username: $HOSTING_USERNAME"
echo "   Path: $HOSTING_PATH"
echo "   Domain: $DOMAIN"
echo ""

# ================================================================
# STEP 1: CREATE REQUIRED DIRECTORIES
# ================================================================
echo "📁 Creating required directories..."

mkdir -p "$HOSTING_PATH/logs"
mkdir -p "$HOSTING_PATH/storage/cache"
mkdir -p "$HOSTING_PATH/storage/logs"
mkdir -p "$HOSTING_PATH/storage/sessions"
mkdir -p "$HOSTING_PATH/bootstrap/cache"

echo "✅ Directories created"

# ================================================================
# STEP 2: SET PROPER PERMISSIONS
# ================================================================
echo "🔒 Setting file permissions..."

# Set directory permissions
chmod 755 "$HOSTING_PATH"
chmod 755 "$HOSTING_PATH/logs"
chmod 755 "$HOSTING_PATH/storage"
chmod 755 "$HOSTING_PATH/storage/cache"
chmod 755 "$HOSTING_PATH/storage/logs"
chmod 755 "$HOSTING_PATH/storage/sessions"
chmod 755 "$HOSTING_PATH/bootstrap/cache"

# Set file permissions
chmod 644 "$HOSTING_PATH/php.ini"
chmod 644 "$HOSTING_PATH/.htaccess"
chmod 644 "$HOSTING_PATH/.env"

# Make storage writable
chmod -R 775 "$HOSTING_PATH/storage"
chmod -R 775 "$HOSTING_PATH/bootstrap/cache"

echo "✅ Permissions set"

# ================================================================
# STEP 3: UPDATE PHP.INI WITH CORRECT PATHS
# ================================================================
echo "⚙️ Updating php.ini with correct paths..."

# Update error log path in php.ini
sed -i "s|/home/<USER>/home/<USER>" "$HOSTING_PATH/php.ini"

echo "✅ php.ini updated"

# ================================================================
# STEP 4: CREATE LOG FILES
# ================================================================
echo "📝 Creating log files..."

touch "$HOSTING_PATH/logs/php_errors.log"
touch "$HOSTING_PATH/logs/api_requests.log"
touch "$HOSTING_PATH/storage/logs/laravel.log"

chmod 644 "$HOSTING_PATH/logs/php_errors.log"
chmod 644 "$HOSTING_PATH/logs/api_requests.log"
chmod 644 "$HOSTING_PATH/storage/logs/laravel.log"

echo "✅ Log files created"

# ================================================================
# STEP 5: OPTIMIZE LARAVEL FOR PRODUCTION
# ================================================================
echo "🔧 Optimizing Laravel for production..."

# Navigate to Laravel directory
cd "$HOSTING_PATH"

# Clear and cache configurations
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
php artisan view:clear
php artisan view:cache

# Optimize autoloader
composer dump-autoload --optimize

echo "✅ Laravel optimized"

# ================================================================
# STEP 6: CREATE ENVIRONMENT FILE
# ================================================================
echo "🌍 Setting up environment file..."

# Create .env file if it doesn't exist
if [ ! -f "$HOSTING_PATH/.env" ]; then
    cp "$HOSTING_PATH/.env.example" "$HOSTING_PATH/.env"
    
    # Update .env with production settings
    sed -i "s|APP_ENV=local|APP_ENV=production|g" "$HOSTING_PATH/.env"
    sed -i "s|APP_DEBUG=true|APP_DEBUG=false|g" "$HOSTING_PATH/.env"
    sed -i "s|APP_URL=http://localhost|APP_URL=https://$DOMAIN|g" "$HOSTING_PATH/.env"
    
    echo "✅ Environment file created"
else
    echo "ℹ️ Environment file already exists"
fi

# ================================================================
# STEP 7: TEST CONFIGURATION
# ================================================================
echo "🧪 Testing configuration..."

# Test PHP configuration
echo "<?php phpinfo(); ?>" > "$HOSTING_PATH/test_php.php"

# Test Laravel
php "$HOSTING_PATH/artisan" --version

echo "✅ Configuration tested"

# ================================================================
# STEP 8: SECURITY CLEANUP
# ================================================================
echo "🔒 Security cleanup..."

# Remove test files
rm -f "$HOSTING_PATH/test_php.php"

# Secure sensitive files
chmod 600 "$HOSTING_PATH/.env"

echo "✅ Security cleanup completed"

# ================================================================
# STEP 9: FINAL VERIFICATION
# ================================================================
echo ""
echo "🔍 Final Verification:"
echo "====================="

echo "📁 Directory Structure:"
ls -la "$HOSTING_PATH" | head -10

echo ""
echo "📝 Log Files:"
ls -la "$HOSTING_PATH/logs/"

echo ""
echo "⚙️ PHP Configuration:"
php -v

echo ""
echo "🌍 Laravel Status:"
cd "$HOSTING_PATH" && php artisan --version

# ================================================================
# COMPLETION MESSAGE
# ================================================================
echo ""
echo "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo "===================================="
echo ""
echo "📋 Next Steps:"
echo "1. Test your API endpoints: https://$DOMAIN/api/chits"
echo "2. Monitor logs: tail -f $HOSTING_PATH/logs/php_errors.log"
echo "3. Check Laravel logs: tail -f $HOSTING_PATH/storage/logs/laravel.log"
echo "4. Test Flutter app connectivity"
echo ""
echo "🔧 Configuration Files:"
echo "   php.ini: $HOSTING_PATH/php.ini"
echo "   .htaccess: $HOSTING_PATH/.htaccess"
echo "   .env: $HOSTING_PATH/.env"
echo ""
echo "📊 Monitoring:"
echo "   PHP Errors: $HOSTING_PATH/logs/php_errors.log"
echo "   API Requests: $HOSTING_PATH/logs/api_requests.log"
echo "   Laravel Logs: $HOSTING_PATH/storage/logs/laravel.log"
echo ""
echo "🚀 Your Chit Fund app is now optimized for LiteSpeed!"

# ================================================================
# OPTIONAL: CREATE MONITORING SCRIPT
# ================================================================
cat > "$HOSTING_PATH/monitor.sh" << 'EOF'
#!/bin/bash
echo "📊 Chit Fund App Monitoring Dashboard"
echo "===================================="
echo ""
echo "🔍 Recent PHP Errors:"
tail -5 logs/php_errors.log
echo ""
echo "📡 Recent API Requests:"
tail -5 logs/api_requests.log
echo ""
echo "🐛 Recent Laravel Logs:"
tail -5 storage/logs/laravel.log
echo ""
echo "💾 Disk Usage:"
du -sh storage/
echo ""
echo "🔄 Cache Status:"
ls -la bootstrap/cache/
EOF

chmod +x "$HOSTING_PATH/monitor.sh"

echo "📊 Monitoring script created: $HOSTING_PATH/monitor.sh"
echo "   Run: ./monitor.sh to check app status"

echo ""
echo "✅ All done! Your LiteSpeed hosting is optimized and ready!"
