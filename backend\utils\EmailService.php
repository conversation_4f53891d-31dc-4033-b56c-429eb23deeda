<?php
/**
 * Email Service for sending various types of emails
 */

require_once __DIR__ . '/../config/env.php';

class EmailService {
    
    /**
     * Send OTP email
     */
    public static function sendOTP($email, $otp, $name = '') {
        $subject = 'Your OTP for Chit Fund App';
        $message = self::getOTPEmailTemplate($otp, $name);
        
        return self::sendEmail($email, $subject, $message);
    }
    
    /**
     * Send welcome email
     */
    public static function sendWelcomeEmail($email, $name) {
        $subject = 'Welcome to Chit Fund App';
        $message = self::getWelcomeEmailTemplate($name);
        
        return self::sendEmail($email, $subject, $message);
    }
    
    /**
     * Send payment reminder email
     */
    public static function sendPaymentReminder($email, $name, $chitName, $amount, $dueDate) {
        $subject = 'Payment Reminder - ' . $chitName;
        $message = self::getPaymentReminderTemplate($name, $chitName, $amount, $dueDate);
        
        return self::sendEmail($email, $subject, $message);
    }
    
    /**
     * Send bidding notification email
     */
    public static function sendBiddingNotification($email, $name, $chitName, $roundNumber) {
        $subject = 'Bidding Round Started - ' . $chitName;
        $message = self::getBiddingNotificationTemplate($name, $chitName, $roundNumber);
        
        return self::sendEmail($email, $subject, $message);
    }
    
    /**
     * Send password reset email
     */
    public static function sendPasswordReset($email, $name, $resetToken) {
        $subject = 'Password Reset Request';
        $message = self::getPasswordResetTemplate($name, $resetToken);
        
        return self::sendEmail($email, $subject, $message);
    }
    
    /**
     * Core email sending function
     */
    private static function sendEmail($to, $subject, $message) {
        try {
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=UTF-8',
                'From: ' . SMTP_FROM_EMAIL,
                'Reply-To: ' . SMTP_FROM_EMAIL,
                'X-Mailer: PHP/' . phpversion()
            ];
            
            $success = mail($to, $subject, $message, implode("\r\n", $headers));
            
            if ($success) {
                error_log("Email sent successfully to: $to");
                return true;
            } else {
                error_log("Failed to send email to: $to");
                return false;
            }
            
        } catch (Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * OTP Email Template
     */
    private static function getOTPEmailTemplate($otp, $name) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4CAF50; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .otp { font-size: 24px; font-weight: bold; color: #4CAF50; text-align: center; padding: 20px; background: white; border: 2px dashed #4CAF50; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Chit Fund App</h1>
                </div>
                <div class='content'>
                    <h2>Hello " . htmlspecialchars($name) . ",</h2>
                    <p>Your One-Time Password (OTP) for verification is:</p>
                    <div class='otp'>$otp</div>
                    <p>This OTP is valid for 10 minutes. Please do not share this code with anyone.</p>
                    <p>If you didn't request this OTP, please ignore this email.</p>
                </div>
                <div class='footer'>
                    <p>Thank you for using Chit Fund App!</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Welcome Email Template
     */
    private static function getWelcomeEmailTemplate($name) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4CAF50; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { text-align: center; padding: 20px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Welcome to Chit Fund App!</h1>
                </div>
                <div class='content'>
                    <h2>Hello " . htmlspecialchars($name) . ",</h2>
                    <p>Welcome to our Chit Fund management platform! We're excited to have you on board.</p>
                    <p>With our app, you can:</p>
                    <ul>
                        <li>Create and manage chit funds</li>
                        <li>Track payments and bidding</li>
                        <li>Manage members and communications</li>
                        <li>Generate reports and analytics</li>
                    </ul>
                    <p>Get started by logging into your account and exploring the features.</p>
                </div>
                <div class='footer'>
                    <p>Thank you for choosing Chit Fund App!</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Payment Reminder Email Template
     */
    private static function getPaymentReminderTemplate($name, $chitName, $amount, $dueDate) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #FF9800; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .amount { font-size: 20px; font-weight: bold; color: #FF9800; }
                .footer { text-align: center; padding: 20px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Payment Reminder</h1>
                </div>
                <div class='content'>
                    <h2>Hello " . htmlspecialchars($name) . ",</h2>
                    <p>This is a friendly reminder that your payment for <strong>" . htmlspecialchars($chitName) . "</strong> is due.</p>
                    <p>Payment Details:</p>
                    <ul>
                        <li>Amount: <span class='amount'>₹" . number_format($amount, 2) . "</span></li>
                        <li>Due Date: <strong>" . date('d M Y', strtotime($dueDate)) . "</strong></li>
                    </ul>
                    <p>Please make your payment on time to avoid any penalties.</p>
                </div>
                <div class='footer'>
                    <p>Thank you for your participation!</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Bidding Notification Email Template
     */
    private static function getBiddingNotificationTemplate($name, $chitName, $roundNumber) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #2196F3; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { text-align: center; padding: 20px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Bidding Round Started!</h1>
                </div>
                <div class='content'>
                    <h2>Hello " . htmlspecialchars($name) . ",</h2>
                    <p>A new bidding round has started for <strong>" . htmlspecialchars($chitName) . "</strong>.</p>
                    <p>Round Details:</p>
                    <ul>
                        <li>Round Number: <strong>$roundNumber</strong></li>
                        <li>Chit Fund: <strong>" . htmlspecialchars($chitName) . "</strong></li>
                    </ul>
                    <p>Please log in to the app to participate in the bidding.</p>
                </div>
                <div class='footer'>
                    <p>Good luck with your bidding!</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * Password Reset Email Template
     */
    private static function getPasswordResetTemplate($name, $resetToken) {
        $resetUrl = "https://chit.mobunite.com/reset-password?token=" . $resetToken;
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #f44336; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .button { display: inline-block; padding: 12px 24px; background: #f44336; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Password Reset Request</h1>
                </div>
                <div class='content'>
                    <h2>Hello " . htmlspecialchars($name) . ",</h2>
                    <p>You have requested to reset your password. Click the button below to reset your password:</p>
                    <a href='$resetUrl' class='button'>Reset Password</a>
                    <p>If you didn't request this password reset, please ignore this email.</p>
                    <p>This link will expire in 1 hour for security reasons.</p>
                </div>
                <div class='footer'>
                    <p>Chit Fund App Security Team</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
?>
