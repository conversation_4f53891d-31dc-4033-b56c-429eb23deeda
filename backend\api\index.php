<?php
/**
 * LiteSpeed Optimized API Entry Point
 * 
 * This file handles all API requests and routes them to appropriate controllers.
 * Optimized for LiteSpeed shared hosting environment with OTP support.
 */

// Start output buffering for better performance
ob_start();

// LiteSpeed optimizations
if (function_exists('fastcgi_finish_request')) {
    register_shutdown_function('fastcgi_finish_request');
}

// Include environment configuration
if (file_exists(__DIR__ . '/../config/env.php')) {
    require_once __DIR__ . '/../config/env.php';
} else {
    die(json_encode(['success' => false, 'message' => 'Configuration file env.php not found']));
}

// Include database configuration (automatically detects LiteSpeed optimizations)
require_once __DIR__ . '/../config/database.php';

// Include security middleware
require_once __DIR__ . '/../middleware/SecurityMiddleware.php';

// Apply comprehensive security checks
SecurityMiddleware::apply('api');

// Performance monitoring start
$start_time = microtime(true);
$start_memory = memory_get_usage();

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove /api prefix if present
$path = preg_replace('#^/api#', '', $path);

// Remove leading slash
$path = ltrim($path, '/');

// Split path into segments
$segments = explode('/', $path);

// Get the main route
$route = $segments[0] ?? '';

// LiteSpeed Cache Control based on route
$cache_control = 'no-cache';
$cache_tags = ['chitfund-api'];

// Set cache headers for cacheable endpoints
$cacheable_routes = ['health', 'reports'];
if (in_array($route, $cacheable_routes) && $method === 'GET') {
    $cache_control = 'public, max-age=1800'; // 30 minutes
    $cache_tags[] = "route-{$route}";
}

// Apply LiteSpeed cache headers
if (defined('LITESPEED_CACHE') && LITESPEED_CACHE) {
    header("X-LiteSpeed-Cache-Control: {$cache_control}");
    header('X-LiteSpeed-Tag: ' . implode(',', $cache_tags));
    header('X-LiteSpeed-Vary: User-Agent,Accept-Encoding');
}

// Rate limiting check
if (defined('ENABLE_RATE_LIMITING') && ENABLE_RATE_LIMITING) {
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $rate_limit_key = "rate_limit_{$client_ip}";
    
    // Simple file-based rate limiting for shared hosting
    $rate_limit_file = __DIR__ . "/../cache/{$rate_limit_key}.txt";
    $current_time = time();
    $rate_limit = defined('API_RATE_LIMIT') ? API_RATE_LIMIT : 60;
    
    if (file_exists($rate_limit_file)) {
        $data = json_decode(file_get_contents($rate_limit_file), true);
        if ($data && $current_time - $data['timestamp'] < 60) {
            if ($data['count'] >= $rate_limit) {
                http_response_code(429);
                echo json_encode([
                    'success' => false,
                    'message' => 'Rate limit exceeded',
                    'retry_after' => 60 - ($current_time - $data['timestamp'])
                ]);
                exit();
            }
            $data['count']++;
        } else {
            $data = ['timestamp' => $current_time, 'count' => 1];
        }
    } else {
        $data = ['timestamp' => $current_time, 'count' => 1];
    }
    
    file_put_contents($rate_limit_file, json_encode($data));
}

// Maintenance mode check
if (defined('MAINTENANCE_MODE') && MAINTENANCE_MODE) {
    $allowed_ips = defined('MAINTENANCE_ALLOWED_IPS') ? MAINTENANCE_ALLOWED_IPS : [];
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
    
    if (!in_array($client_ip, $allowed_ips)) {
        http_response_code(503);
        echo json_encode([
            'success' => false,
            'message' => defined('MAINTENANCE_MESSAGE') ? MAINTENANCE_MESSAGE : 'System is under maintenance'
        ]);
        exit();
    }
}

// Handle different routes
try {
    switch ($route) {
        case 'auth':
            require_once __DIR__ . '/auth/index.php';
            break;
            
        case 'users':
            require_once __DIR__ . '/users/index.php';
            break;
            
        case 'chits':
            require_once __DIR__ . '/chits/index.php';
            break;
            
        case 'members':
            require_once __DIR__ . '/members/index.php';
            break;
            
        case 'payments':
            require_once __DIR__ . '/payments/index.php';
            break;
            
        case 'bidding':
            require_once __DIR__ . '/bidding/index.php';
            break;
            
        case 'reports':
            require_once __DIR__ . '/reports/index.php';
            break;
            
        case 'notifications':
            require_once __DIR__ . '/notifications/index.php';
            break;

        case 'files':
            require_once __DIR__ . '/files/index.php';
            break;

        case 'backup':
            require_once __DIR__ . '/backup/index.php';
            break;

        case 'sms':
            require_once __DIR__ . '/sms/index.php';
            break;

        case 'push':
            require_once __DIR__ . '/push/index.php';
            break;

        case 'health':
            // Health check endpoint with system info
            $end_time = microtime(true);
            $execution_time = round(($end_time - $start_time) * 1000, 2);
            $memory_usage = round((memory_get_usage() - $start_memory) / 1024 / 1024, 2);
            
            $health_data = [
                'status' => 'healthy',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => defined('APP_VERSION') ? APP_VERSION : '1.0.0',
                'environment' => defined('APP_ENV') ? APP_ENV : 'production',
                'features' => [
                    'otp_verification' => true,
                    'litespeed_optimized' => true,
                    'rate_limiting' => defined('ENABLE_RATE_LIMITING') ? ENABLE_RATE_LIMITING : false,
                    'caching' => defined('CACHE_ENABLED') ? CACHE_ENABLED : false
                ],
                'performance' => [
                    'execution_time_ms' => $execution_time,
                    'memory_usage_mb' => $memory_usage,
                    'peak_memory_mb' => round(memory_get_peak_usage() / 1024 / 1024, 2)
                ]
            ];
            
            // Test database connection
            try {
                if (class_exists('LiteSpeedDatabase')) {
                    $db = new LiteSpeedDatabase();
                    $conn = $db->getConnection();
                    $health_data['database'] = $conn ? 'connected' : 'disconnected';
                    if ($conn) {
                        $health_data['database_stats'] = $db->getStats();
                    }
                } else {
                    $database = new Database();
                    $pdo = $database->getConnection();
                    $health_data['database'] = $pdo ? 'connected' : 'disconnected';
                }
            } catch (Exception $e) {
                $health_data['database'] = 'error: ' . $e->getMessage();
            }
            
            // Cache status
            $health_data['cache'] = [
                'enabled' => defined('CACHE_ENABLED') ? CACHE_ENABLED : false,
                'litespeed' => defined('LITESPEED_CACHE') ? LITESPEED_CACHE : false
            ];
            
            echo json_encode(['success' => true, 'data' => $health_data]);
            break;
            
        case '':
            // API root - show available endpoints
            echo json_encode([
                'success' => true,
                'data' => [
                    'message' => 'Chit Fund API - LiteSpeed Optimized with OTP',
                    'version' => defined('APP_VERSION') ? APP_VERSION : '1.0.0',
                    'environment' => defined('APP_ENV') ? APP_ENV : 'production',
                    'endpoints' => [
                        'auth' => [
                            'login' => 'POST /api/auth/login',
                            'register' => 'POST /api/auth/register',
                            'send_otp' => 'POST /api/auth/send-otp',
                            'verify_otp' => 'POST /api/auth/verify-otp',
                            'reset_password' => 'POST /api/auth/reset-password',
                            'logout' => 'POST /api/auth/logout'
                        ],
                        'chits' => 'GET|POST|PUT|DELETE /api/chits',
                        'members' => 'GET|POST|PUT|DELETE /api/members',
                        'payments' => 'GET|POST|PUT /api/payments',
                        'bidding' => 'GET|POST /api/bidding',
                        'reports' => 'GET /api/reports',
                        'notifications' => 'GET|POST|PUT /api/notifications',
                        'health' => 'GET /api/health'
                    ],
                    'features' => [
                        'OTP verification for registration and password reset',
                        'LiteSpeed cache optimization',
                        'Rate limiting protection',
                        'JWT authentication',
                        'Real-time notifications',
                        'Comprehensive reporting'
                    ]
                ]
            ]);
            break;
            
        default:
            // Log 404 errors for monitoring
            error_log("API 404: {$method} {$path} from {$_SERVER['REMOTE_ADDR']}");
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Endpoint not found',
                'available_routes' => ['auth', 'chits', 'members', 'payments', 'bidding', 'reports', 'notifications', 'health']
            ]);
            break;
    }
} catch (Exception $e) {
    // Enhanced error logging
    $error_context = [
        'method' => $method,
        'path' => $path,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    error_log("API Error: " . $e->getMessage() . " Context: " . json_encode($error_context));
    
    // Clear LiteSpeed cache on error
    if (defined('LITESPEED_CACHE') && LITESPEED_CACHE) {
        header('X-LiteSpeed-Cache-Control: no-cache');
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => defined('APP_DEBUG') && APP_DEBUG ? $e->getMessage() : null
    ]);
} finally {
    // Performance logging for slow requests
    $end_time = microtime(true);
    $execution_time = $end_time - $start_time;
    
    if (defined('PERFORMANCE_LOG_SLOW_REQUESTS') && PERFORMANCE_LOG_SLOW_REQUESTS) {
        $threshold = defined('SLOW_REQUEST_THRESHOLD') ? SLOW_REQUEST_THRESHOLD : 3;
        if ($execution_time > $threshold) {
            error_log("Slow API request: {$method} {$path} took {$execution_time}s");
        }
    }
    
    // Cleanup rate limit files older than 1 hour
    if (rand(1, 100) === 1) { // 1% chance to run cleanup
        $cache_dir = __DIR__ . '/../cache/';
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . 'rate_limit_*.txt');
            foreach ($files as $file) {
                if (filemtime($file) < time() - 3600) {
                    unlink($file);
                }
            }
        }
    }
}

// Flush output buffer
ob_end_flush();

?>
