<?php
/**
 * Members API Routes - LiteSpeed Optimized
 */

require_once __DIR__ . '/../../controllers/MemberController.php';

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];

// Remove /api/members prefix and get the endpoint
$endpoint = str_replace('/api/members', '', parse_url($path, PHP_URL_PATH));
$endpoint = trim($endpoint, '/');

// Split path into segments
$segments = explode('/', $endpoint);
$memberId = $segments[0] ?? null;
$action = $segments[1] ?? null;

// Initialize controller
$memberController = new MemberController();

// LiteSpeed cache control for members endpoints
if ($method === 'GET') {
    header('X-LiteSpeed-Cache-Control: public, max-age=600'); // 10 minutes
    header('X-LiteSpeed-Tag: members');
} else {
    header('X-LiteSpeed-Cache-Control: no-cache');
}

try {
    switch ($method) {
        case 'GET':
            if (empty($memberId)) {
                // GET /api/members - List all members
                $memberController->getAllMembers();
            } elseif (empty($action)) {
                // GET /api/members/{id} - Get specific member
                $memberController->getMember($memberId);
            } elseif ($action === 'chits') {
                // GET /api/members/{id}/chits - Get member's chits
                $memberController->getMemberChits($memberId);
            } elseif ($action === 'payments') {
                // GET /api/members/{id}/payments - Get member's payments
                $memberController->getMemberPayments($memberId);
            } elseif ($action === 'summary') {
                // GET /api/members/{id}/summary - Get member summary
                $memberController->getMemberSummary($memberId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'POST':
            if (empty($memberId)) {
                // POST /api/members - Create new member
                $memberController->createMember();
            } elseif ($action === 'invite') {
                // POST /api/members/{id}/invite - Send invitation
                $memberController->sendInvitation($memberId);
            } elseif ($action === 'activate') {
                // POST /api/members/{id}/activate - Activate member
                $memberController->activateMember($memberId);
            } elseif ($action === 'deactivate') {
                // POST /api/members/{id}/deactivate - Deactivate member
                $memberController->deactivateMember($memberId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'PUT':
            if (!empty($memberId) && empty($action)) {
                // PUT /api/members/{id} - Update member
                $memberController->updateMember($memberId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'DELETE':
            if (!empty($memberId) && empty($action)) {
                // DELETE /api/members/{id} - Delete member
                $memberController->deleteMember($memberId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        default:
            sendError('Method not allowed', 405);
            break;
    }
} catch (Exception $e) {
    error_log("Members API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

?>
