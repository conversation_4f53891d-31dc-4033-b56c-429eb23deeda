<?php
/**
 * Push Notification API Routes
 */

require_once __DIR__ . '/../../utils/PushNotificationService.php';
require_once __DIR__ . '/../../utils/helpers.php';
require_once __DIR__ . '/../../utils/JWT.php';

// Get remaining path segments
$segments = array_slice(explode('/', trim($_SERVER['REQUEST_URI'], '/')), 2);
$action = $segments[0] ?? '';

// Verify authentication for all push notification operations
$userId = JWT::getUserIdFromToken();
if (!$userId) {
    sendError('Authentication required', 401);
}

switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        switch ($action) {
            case 'register-token':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['device_token', 'platform'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                try {
                    $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
                    $conn = $db->getConnection();
                    
                    // Insert or update device token
                    $sql = "INSERT INTO device_tokens (user_id, device_token, platform, app_version) 
                            VALUES (?, ?, ?, ?) 
                            ON DUPLICATE KEY UPDATE 
                            platform = VALUES(platform), 
                            app_version = VALUES(app_version), 
                            is_active = TRUE, 
                            updated_at = CURRENT_TIMESTAMP";
                    
                    $stmt = $conn->prepare($sql);
                    $stmt->execute([
                        $userId,
                        $data['device_token'],
                        $data['platform'],
                        $data['app_version'] ?? null
                    ]);
                    
                    // Log activity
                    logActivity($userId, 'device_token_registered', [
                        'platform' => $data['platform']
                    ]);
                    
                    sendSuccess(null, 'Device token registered successfully');
                    
                } catch (Exception $e) {
                    error_log("Register device token error: " . $e->getMessage());
                    sendError('Failed to register device token', 500);
                }
                break;
                
            case 'send-to-device':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['device_token', 'title', 'body'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                $notificationData = $data['data'] ?? [];
                $result = PushNotificationService::sendToDevice(
                    $data['device_token'],
                    $data['title'],
                    $data['body'],
                    $notificationData
                );
                
                if ($result) {
                    // Log activity
                    logActivity($userId, 'push_notification_sent', [
                        'title' => $data['title'],
                        'type' => 'device'
                    ]);
                    
                    sendSuccess(null, 'Push notification sent successfully');
                } else {
                    sendError('Failed to send push notification', 500);
                }
                break;
                
            case 'send-to-topic':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['topic', 'title', 'body'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                $notificationData = $data['data'] ?? [];
                $result = PushNotificationService::sendToTopic(
                    $data['topic'],
                    $data['title'],
                    $data['body'],
                    $notificationData
                );
                
                if ($result) {
                    // Log activity
                    logActivity($userId, 'push_notification_sent', [
                        'title' => $data['title'],
                        'type' => 'topic',
                        'topic' => $data['topic']
                    ]);
                    
                    sendSuccess(null, 'Topic push notification sent successfully');
                } else {
                    sendError('Failed to send topic push notification', 500);
                }
                break;
                
            case 'send-payment-reminder':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['device_token', 'chit_name', 'amount', 'due_date'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                $result = PushNotificationService::sendPaymentReminder(
                    $data['device_token'],
                    $data['chit_name'],
                    $data['amount'],
                    $data['due_date']
                );
                
                if ($result) {
                    // Log activity
                    logActivity($userId, 'payment_reminder_push_sent', [
                        'chit_name' => $data['chit_name']
                    ]);
                    
                    sendSuccess(null, 'Payment reminder push notification sent successfully');
                } else {
                    sendError('Failed to send payment reminder push notification', 500);
                }
                break;
                
            case 'send-bidding-notification':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['device_token', 'chit_name', 'round_number'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                $result = PushNotificationService::sendBiddingNotification(
                    $data['device_token'],
                    $data['chit_name'],
                    $data['round_number']
                );
                
                if ($result) {
                    // Log activity
                    logActivity($userId, 'bidding_notification_push_sent', [
                        'chit_name' => $data['chit_name']
                    ]);
                    
                    sendSuccess(null, 'Bidding notification push notification sent successfully');
                } else {
                    sendError('Failed to send bidding notification push notification', 500);
                }
                break;
                
            case 'subscribe-topic':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['device_token', 'topic'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                $result = PushNotificationService::subscribeToTopic(
                    $data['device_token'],
                    $data['topic']
                );
                
                if ($result) {
                    sendSuccess(null, 'Subscribed to topic successfully');
                } else {
                    sendError('Failed to subscribe to topic', 500);
                }
                break;
                
            case 'unsubscribe-topic':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['device_token', 'topic'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                $result = PushNotificationService::unsubscribeFromTopic(
                    $data['device_token'],
                    $data['topic']
                );
                
                if ($result) {
                    sendSuccess(null, 'Unsubscribed from topic successfully');
                } else {
                    sendError('Failed to unsubscribe from topic', 500);
                }
                break;
                
            default:
                sendError('Invalid push notification action', 400);
                break;
        }
        break;
        
    case 'GET':
        switch ($action) {
            case 'stats':
                $stats = PushNotificationService::getNotificationStats();
                sendSuccess($stats, 'Push notification statistics retrieved successfully');
                break;
                
            case 'user-tokens':
                try {
                    $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
                    $conn = $db->getConnection();
                    
                    $stmt = $conn->prepare("SELECT device_token, platform, app_version, is_active, created_at, updated_at FROM device_tokens WHERE user_id = ? AND is_active = TRUE");
                    $stmt->execute([$userId]);
                    $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    sendSuccess($tokens, 'User device tokens retrieved successfully');
                    
                } catch (Exception $e) {
                    error_log("Get user tokens error: " . $e->getMessage());
                    sendError('Failed to retrieve user tokens', 500);
                }
                break;
                
            default:
                sendError('Invalid push notification endpoint', 400);
                break;
        }
        break;
        
    case 'DELETE':
        switch ($action) {
            case 'remove-token':
                $data = getRequestData();
                
                if (!isset($data['device_token'])) {
                    sendError('Device token required', 400);
                }
                
                try {
                    $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
                    $conn = $db->getConnection();
                    
                    $stmt = $conn->prepare("UPDATE device_tokens SET is_active = FALSE WHERE user_id = ? AND device_token = ?");
                    $stmt->execute([$userId, $data['device_token']]);
                    
                    sendSuccess(null, 'Device token removed successfully');
                    
                } catch (Exception $e) {
                    error_log("Remove device token error: " . $e->getMessage());
                    sendError('Failed to remove device token', 500);
                }
                break;
                
            default:
                sendError('Invalid push notification delete action', 400);
                break;
        }
        break;
        
    default:
        sendError('Method not allowed', 405);
        break;
}
?>
