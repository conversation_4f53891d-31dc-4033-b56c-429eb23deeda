; ================================================================
; OPTIMIZED PHP.INI FOR LITESPEED SHARED HOSTING
; Chit Fund App - Production Ready Configuration
; ================================================================

; ================================================================
; CORE SETTINGS
; ================================================================
engine = On
short_open_tag = Off
precision = 14
output_buffering = 4096
zlib.output_compression = On
zlib.output_compression_level = 6
implicit_flush = Off
unserialize_callback_func =
serialize_precision = -1
disable_functions = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source
disable_classes =
zend.enable_gc = On
zend.exception_ignore_args = On
zend.exception_string_param_max_len = 0

; ================================================================
; PERFORMANCE & MEMORY SETTINGS (Optimized for Shared Hosting)
; ================================================================
memory_limit = 256M
max_execution_time = 60
max_input_time = 60
max_input_vars = 3000
max_input_nesting_level = 64
post_max_size = 32M
upload_max_filesize = 16M
max_file_uploads = 20
default_socket_timeout = 60

; ================================================================
; ERROR REPORTING & LOGGING (Enhanced for API Debugging)
; ================================================================
; Development Settings (Change for Production)
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
display_errors = On
display_startup_errors = On
log_errors = On
log_errors_max_len = 1024
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On

; Custom Error Log Location (Update with your actual path)
error_log = /home/<USER>/public_html/logs/php_errors.log
; Alternative locations for different hosting providers:
; error_log = /home/<USER>/logs/php_errors.log
; error_log = /var/log/php_errors.log

; ================================================================
; SESSION SETTINGS (Optimized for API Usage)
; ================================================================
session.save_handler = files
session.save_path = "/tmp"
session.use_strict_mode = 1
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 86400
session.cookie_path = /
session.cookie_domain =
session.cookie_httponly = 1
session.cookie_secure = 1
session.cookie_samesite = "Lax"
session.serialize_handler = php
session.gc_probability = 1
session.gc_divisor = 1000
session.gc_maxlifetime = 86400
session.referer_check =
session.cache_limiter = nocache
session.cache_expire = 180
session.use_trans_sid = 0
session.sid_length = 26
session.trans_sid_tags = "a=href,area=href,frame=src,form="
session.sid_bits_per_character = 5

; ================================================================
; SECURITY SETTINGS
; ================================================================
expose_php = Off
allow_url_fopen = On
allow_url_include = Off
auto_globals_jit = On
default_charset = "UTF-8"
internal_encoding = "UTF-8"
input_encoding = "UTF-8"
output_encoding = "UTF-8"

; ================================================================
; FILE UPLOAD SETTINGS
; ================================================================
file_uploads = On
upload_tmp_dir = /tmp
auto_detect_line_endings = Off

; ================================================================
; DATABASE SETTINGS (MySQL/MariaDB Optimized)
; ================================================================
; MySQLi Settings
mysqli.max_persistent = 10
mysqli.allow_persistent = On
mysqli.max_links = 20
mysqli.default_port = 3306
mysqli.default_socket =
mysqli.default_host =
mysqli.default_user =
mysqli.default_pw =
mysqli.reconnect = Off

; PDO Settings
pdo_mysql.default_socket =

; ================================================================
; OPCACHE SETTINGS (Critical for Performance)
; ================================================================
opcache.enable = 1
opcache.enable_cli = 0
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
opcache.enable_file_override = 0
opcache.optimization_level = 0x7FFFBFFF
opcache.inherited_hack = 1
opcache.dups_fix = 0
opcache.blacklist_filename = ""
opcache.max_file_size = 0
opcache.consistency_checks = 0
opcache.force_restart_timeout = 180
opcache.error_log = ""
opcache.log_verbosity_level = 1
opcache.preferred_memory_model = ""
opcache.protect_memory = 0
opcache.save_comments = 1
opcache.validate_timestamps = 1

; ================================================================
; REALPATH CACHE (Performance Boost)
; ================================================================
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; ================================================================
; MAIL SETTINGS
; ================================================================
SMTP = localhost
smtp_port = 25
mail.add_x_header = Off

; ================================================================
; DATE/TIME SETTINGS
; ================================================================
date.timezone = "Asia/Kolkata"

; ================================================================
; LITESPEED SPECIFIC OPTIMIZATIONS
; ================================================================
; Enable LiteSpeed Cache
litespeed.esi = On

; ================================================================
; CURL SETTINGS (For API Calls)
; ================================================================
curl.cainfo = "/path/to/cacert.pem"

; ================================================================
; JSON SETTINGS (Important for API)
; ================================================================
json.serialize_precision = -1

; ================================================================
; MBSTRING SETTINGS (For UTF-8 Support)
; ================================================================
mbstring.language = English
mbstring.internal_encoding = "UTF-8"
mbstring.http_input = "auto"
mbstring.http_output = "UTF-8"
mbstring.encoding_translation = Off
mbstring.detect_order = "auto"
mbstring.substitute_character = "none"
mbstring.func_overload = 0
mbstring.strict_detection = On
mbstring.http_output_conv_mimetype = "^(text/|application/xhtml\+xml)"

; ================================================================
; CUSTOM LOGGING FOR API DEBUGGING
; ================================================================
; Enable detailed logging for debugging
log_errors = On
error_log = /home/<USER>/public_html/logs/php_errors.log

; Custom ini settings for better debugging
auto_prepend_file = ""
auto_append_file = ""

; ================================================================
; RESOURCE LIMITS (Shared Hosting Friendly)
; ================================================================
; Process limits
max_execution_time = 60
max_input_time = 60
memory_limit = 256M

; Connection limits
default_socket_timeout = 60
mysql.connect_timeout = 60
mysql.timeout = 60

; ================================================================
; COMPRESSION SETTINGS
; ================================================================
zlib.output_compression = On
zlib.output_compression_level = 6
zlib.output_handler = ""

; ================================================================
; ADDITIONAL PERFORMANCE SETTINGS
; ================================================================
; Disable unnecessary modules for better performance
; (Uncomment if these modules are not needed)
; extension=bcmath
; extension=calendar
; extension=ctype
; extension=curl
; extension=dom
; extension=exif
; extension=fileinfo
; extension=filter
; extension=ftp
; extension=gd
; extension=gettext
; extension=hash
; extension=iconv
; extension=json
; extension=libxml
; extension=mbstring
; extension=mysqli
; extension=mysqlnd
; extension=openssl
; extension=pcre
; extension=pdo
; extension=pdo_mysql
; extension=phar
; extension=posix
; extension=readline
; extension=reflection
; extension=session
; extension=simplexml
; extension=sockets
; extension=spl
; extension=standard
; extension=tokenizer
; extension=xml
; extension=xmlreader
; extension=xmlwriter
; extension=zip
; extension=zlib

; ================================================================
; CUSTOM SETTINGS FOR CHIT FUND APP
; ================================================================
; Increase limits for handling multiple chit members
max_input_vars = 3000
post_max_size = 32M

; Optimize for JSON API responses
json.serialize_precision = -1

; Enable better error tracking
track_errors = On
html_errors = On

; ================================================================
; END OF CONFIGURATION
; ================================================================
