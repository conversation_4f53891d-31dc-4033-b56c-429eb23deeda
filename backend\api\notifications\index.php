<?php
/**
 * Notifications API Routes - LiteSpeed Optimized
 */

require_once __DIR__ . '/../../controllers/NotificationController.php';

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];

// Remove /api/notifications prefix and get the endpoint
$endpoint = str_replace('/api/notifications', '', parse_url($path, PHP_URL_PATH));
$endpoint = trim($endpoint, '/');

// Split path into segments
$segments = explode('/', $endpoint);
$notificationId = $segments[0] ?? null;
$action = $segments[1] ?? null;

// Initialize controller
$notificationController = new NotificationController();

// LiteSpeed cache control for notifications endpoints
if ($method === 'GET') {
    header('X-LiteSpeed-Cache-Control: public, max-age=60'); // 1 minute for real-time
    header('X-LiteSpeed-Tag: notifications');
} else {
    header('X-LiteSpeed-Cache-Control: no-cache');
}

try {
    switch ($method) {
        case 'GET':
            if (empty($notificationId)) {
                // GET /api/notifications - List all notifications
                $notificationController->getAllNotifications();
            } elseif ($notificationId === 'unread') {
                // GET /api/notifications/unread - Get unread notifications
                $notificationController->getUnreadNotifications();
            } elseif ($notificationId === 'count') {
                // GET /api/notifications/count - Get notification count
                $notificationController->getNotificationCount();
            } elseif (empty($action)) {
                // GET /api/notifications/{id} - Get specific notification
                $notificationController->getNotification($notificationId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'POST':
            if (empty($notificationId)) {
                // POST /api/notifications - Create new notification
                $notificationController->createNotification();
            } elseif ($notificationId === 'send') {
                // POST /api/notifications/send - Send notification
                $notificationController->sendNotification();
            } elseif ($notificationId === 'broadcast') {
                // POST /api/notifications/broadcast - Broadcast notification
                $notificationController->broadcastNotification();
            } elseif ($action === 'read') {
                // POST /api/notifications/{id}/read - Mark as read
                $notificationController->markAsRead($notificationId);
            } elseif ($action === 'unread') {
                // POST /api/notifications/{id}/unread - Mark as unread
                $notificationController->markAsUnread($notificationId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'PUT':
            if (!empty($notificationId) && empty($action)) {
                // PUT /api/notifications/{id} - Update notification
                $notificationController->updateNotification($notificationId);
            } elseif ($notificationId === 'read-all') {
                // PUT /api/notifications/read-all - Mark all as read
                $notificationController->markAllAsRead();
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'DELETE':
            if (!empty($notificationId) && empty($action)) {
                // DELETE /api/notifications/{id} - Delete notification
                $notificationController->deleteNotification($notificationId);
            } elseif ($notificationId === 'clear-all') {
                // DELETE /api/notifications/clear-all - Clear all notifications
                $notificationController->clearAllNotifications();
            } elseif ($notificationId === 'clear-read') {
                // DELETE /api/notifications/clear-read - Clear read notifications
                $notificationController->clearReadNotifications();
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        default:
            sendError('Method not allowed', 405);
            break;
    }
} catch (Exception $e) {
    error_log("Notifications API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

?>
