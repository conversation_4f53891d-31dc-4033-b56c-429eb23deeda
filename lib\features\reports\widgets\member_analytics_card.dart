import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/models/chit_model.dart';

class MemberAnalyticsCard extends StatelessWidget {
  final DateTimeRange? dateRange;
  final String? chitId;

  const MemberAnalyticsCard({
    super.key,
    this.dateRange,
    this.chitId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ChitProvider>(
      builder: (context, chitProvider, child) {
        final analyticsData = _calculateAnalyticsData(chitProvider);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Member Analytics',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Key metrics
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        'Total Members',
                        '${analyticsData['totalMembers']}',
                        Icons.people,
                        Colors.blue,
                        '${analyticsData['memberGrowth'].toStringAsFixed(1)}%',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        'Active Members',
                        '${analyticsData['activeMembers']}',
                        Icons.person,
                        Colors.green,
                        '${analyticsData['activeRate'].toStringAsFixed(1)}%',
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        'Retention Rate',
                        '${analyticsData['retentionRate'].toStringAsFixed(1)}%',
                        Icons.trending_up,
                        Colors.purple,
                        analyticsData['retentionTrend'] >= 0 ? '+' : '',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildMetricCard(
                        context,
                        'Avg. Contribution',
                        '₹${_formatAmount(analyticsData['avgContribution'])}',
                        Icons.account_balance_wallet,
                        Colors.orange,
                        '${analyticsData['contributionGrowth'].toStringAsFixed(1)}%',
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Member distribution
                _buildMemberDistribution(context, analyticsData),
                
                const SizedBox(height: 20),
                
                // Top performers
                _buildTopPerformers(context, analyticsData),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    String trend,
  ) {
    final isPositiveTrend = trend.startsWith('+') || (!trend.startsWith('-') && trend.contains('%'));
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              if (trend.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isPositiveTrend ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    trend,
                    style: TextStyle(
                      color: isPositiveTrend ? Colors.green : Colors.red,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemberDistribution(BuildContext context, Map<String, dynamic> data) {
    final distribution = data['distribution'] as Map<String, int>;
    final total = distribution.values.fold(0, (sum, count) => sum + count);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Member Distribution',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 12),
        
        ...distribution.entries.map((entry) {
          final percentage = total > 0 ? (entry.value / total) : 0.0;
          final color = _getDistributionColor(entry.key);
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      entry.key,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${entry.value} (${(percentage * 100).toStringAsFixed(1)}%)',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),
                
                LinearProgressIndicator(
                  value: percentage,
                  backgroundColor: color.withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildTopPerformers(BuildContext context, Map<String, dynamic> data) {
    final topPerformers = data['topPerformers'] as List<Map<String, dynamic>>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Top Contributors',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // View all contributors
              },
              child: const Text('View All'),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        ...topPerformers.take(5).map((performer) {
          final rank = topPerformers.indexOf(performer) + 1;
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                // Rank badge
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: _getRankColor(rank),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      '$rank',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Member avatar
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  child: Text(
                    performer['name'][0].toUpperCase(),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Member info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        performer['name'],
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${performer['chitsCount']} chits',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Contribution amount
                Text(
                  '₹${_formatAmount(performer['totalContribution'])}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Map<String, dynamic> _calculateAnalyticsData(ChitProvider chitProvider) {
    final chits = chitProvider.chits;
    
    // Filter chits based on criteria
    final relevantChits = chitId != null
        ? chits.where((c) => c.id == chitId)
        : chits;

    // Calculate member metrics
    final allMembers = relevantChits.expand((c) => c.members).toList();
    final totalMembers = allMembers.length;
    final activeMembers = allMembers.where((m) => m.isActive).length;
    final activeRate = totalMembers > 0 ? (activeMembers / totalMembers) * 100 : 0.0;

    // Calculate distribution by role
    final distribution = <String, int>{
      'Organizers': allMembers.where((m) => m.role == MemberRole.organizer).length,
      'Members': allMembers.where((m) => m.role == MemberRole.member).length,
      'Winners': allMembers.where((m) => m.hasWon).length,
    };

    // Calculate top performers (simplified)
    final memberContributions = <String, Map<String, dynamic>>{};
    
    for (final chit in relevantChits) {
      for (final member in chit.members) {
        if (!memberContributions.containsKey(member.id)) {
          memberContributions[member.id] = {
            'name': member.name,
            'totalContribution': 0.0,
            'chitsCount': 0,
          };
        }
        
        memberContributions[member.id]!['totalContribution'] += chit.totalAmount / chit.numberOfMembers;
        memberContributions[member.id]!['chitsCount'] += 1;
      }
    }

    final topPerformers = memberContributions.values.toList()
      ..sort((a, b) => (b['totalContribution'] as double).compareTo(a['totalContribution'] as double));

    final avgContribution = topPerformers.isNotEmpty
        ? topPerformers.map((p) => p['totalContribution'] as double).reduce((a, b) => a + b) / topPerformers.length
        : 0.0;

    return {
      'totalMembers': totalMembers,
      'activeMembers': activeMembers,
      'activeRate': activeRate,
      'retentionRate': 89.5, // Placeholder
      'retentionTrend': 2.3, // Placeholder
      'avgContribution': avgContribution,
      'contributionGrowth': 15.2, // Placeholder
      'memberGrowth': 8.7, // Placeholder
      'distribution': distribution,
      'topPerformers': topPerformers,
    };
  }

  Color _getDistributionColor(String category) {
    switch (category) {
      case 'Organizers':
        return Colors.purple;
      case 'Members':
        return Colors.blue;
      case 'Winners':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber; // Gold
      case 2:
        return Colors.grey; // Silver
      case 3:
        return Colors.brown; // Bronze
      default:
        return Colors.blue;
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
