import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/providers/payment_provider.dart';
import '../../../core/models/chit_model.dart';
import '../../../core/models/payment_model.dart';

class FinancialSummaryCard extends StatelessWidget {
  final DateTimeRange? dateRange;
  final String? chitId;

  const FinancialSummaryCard({
    super.key,
    this.dateRange,
    this.chitId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<ChitProvider, PaymentProvider>(
      builder: (context, chitProvider, paymentProvider, child) {
        final financialData = _calculateFinancialData(chitProvider, paymentProvider);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Financial Summary',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getPerformanceColor(financialData['performance']).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getPerformanceText(financialData['performance']),
                        style: TextStyle(
                          color: _getPerformanceColor(financialData['performance']),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Main financial metrics
                Row(
                  children: [
                    Expanded(
                      child: _buildMainMetric(
                        context,
                        'Total Revenue',
                        financialData['totalRevenue'],
                        financialData['revenueChange'],
                        Icons.trending_up,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildMainMetric(
                        context,
                        'Outstanding',
                        financialData['outstanding'],
                        financialData['outstandingChange'],
                        Icons.schedule,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Secondary metrics
                Row(
                  children: [
                    Expanded(
                      child: _buildSecondaryMetric(
                        context,
                        'Collection Rate',
                        '${financialData['collectionRate'].toStringAsFixed(1)}%',
                        Icons.check_circle,
                        Colors.blue,
                      ),
                    ),
                    Expanded(
                      child: _buildSecondaryMetric(
                        context,
                        'Active Chits',
                        '${financialData['activeChits']}',
                        Icons.account_balance_wallet,
                        Colors.purple,
                      ),
                    ),
                    Expanded(
                      child: _buildSecondaryMetric(
                        context,
                        'Total Members',
                        '${financialData['totalMembers']}',
                        Icons.people,
                        Colors.teal,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Progress indicators
                _buildProgressSection(context, financialData),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMainMetric(
    BuildContext context,
    String label,
    double value,
    double change,
    IconData icon,
    Color color,
  ) {
    final isPositive = change >= 0;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Text(
            '₹${_formatAmount(value)}',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Row(
            children: [
              Icon(
                isPositive ? Icons.trending_up : Icons.trending_down,
                size: 16,
                color: isPositive ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 4),
              Text(
                '${isPositive ? '+' : ''}${change.toStringAsFixed(1)}%',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isPositive ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSecondaryMetric(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildProgressSection(BuildContext context, Map<String, dynamic> data) {
    final collectionRate = data['collectionRate'] / 100;
    final completionRate = data['completionRate'] / 100;
    
    return Column(
      children: [
        // Collection Progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Collection Progress',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(collectionRate * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: _getProgressColor(collectionRate),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        LinearProgressIndicator(
          value: collectionRate,
          backgroundColor: Colors.grey.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor(collectionRate)),
        ),
        
        const SizedBox(height: 16),
        
        // Completion Progress
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Overall Completion',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(completionRate * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: _getProgressColor(completionRate),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        LinearProgressIndicator(
          value: completionRate,
          backgroundColor: Colors.grey.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(_getProgressColor(completionRate)),
        ),
      ],
    );
  }

  Map<String, dynamic> _calculateFinancialData(ChitProvider chitProvider, PaymentProvider paymentProvider) {
    final chits = chitProvider.chits;
    final payments = paymentProvider.payments;
    
    // Filter by date range if specified
    final filteredPayments = dateRange != null
        ? payments.where((p) => 
            p.createdAt.isAfter(dateRange!.start) && 
            p.createdAt.isBefore(dateRange!.end.add(const Duration(days: 1))))
        : payments;
    
    // Filter by chit if specified
    final relevantPayments = chitId != null
        ? filteredPayments.where((p) => p.chitId == chitId)
        : filteredPayments;
    
    final relevantChits = chitId != null
        ? chits.where((c) => c.id == chitId)
        : chits;
    
    // Calculate metrics
    final totalRevenue = relevantPayments
        .where((p) => p.paymentStatus == PaymentStatus.paid)
        .fold(0.0, (sum, p) => sum + (p.amount ?? 0.0));

    final outstanding = relevantPayments
        .where((p) => p.paymentStatus == PaymentStatus.pending)
        .fold(0.0, (sum, p) => sum + (p.amount ?? 0.0));
    
    final totalPayments = relevantPayments.length;
    final paidPayments = relevantPayments.where((p) => p.paymentStatus == PaymentStatus.paid).length;
    final collectionRate = totalPayments > 0 ? (paidPayments / totalPayments) * 100 : 0.0;
    
    final activeChits = relevantChits.where((c) => c.status == ChitStatus.active).length;
    final completedChits = relevantChits.where((c) => c.status == ChitStatus.completed).length;
    final totalChits = relevantChits.length;
    final completionRate = totalChits > 0 ? (completedChits / totalChits) * 100 : 0.0;
    
    final totalMembers = relevantChits.fold(0, (sum, c) => sum + c.numberOfMembers);
    
    // Calculate performance score
    final performance = (collectionRate + completionRate) / 2;
    
    return {
      'totalRevenue': totalRevenue,
      'outstanding': outstanding,
      'collectionRate': collectionRate,
      'completionRate': completionRate,
      'activeChits': activeChits,
      'totalMembers': totalMembers,
      'performance': performance,
      'revenueChange': 12.5, // Placeholder - would calculate from historical data
      'outstandingChange': -8.3, // Placeholder - would calculate from historical data
    };
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }

  Color _getProgressColor(double progress) {
    if (progress >= 0.8) return Colors.green;
    if (progress >= 0.6) return Colors.orange;
    return Colors.red;
  }

  Color _getPerformanceColor(double performance) {
    if (performance >= 80) return Colors.green;
    if (performance >= 60) return Colors.orange;
    return Colors.red;
  }

  String _getPerformanceText(double performance) {
    if (performance >= 80) return 'Excellent';
    if (performance >= 60) return 'Good';
    return 'Needs Attention';
  }
}
