<?php
/**
 * LiteSpeed Performance Optimization Script
 * 
 * Run this script to optimize your Chit Fund API for LiteSpeed shared hosting
 */

echo "🚀 LiteSpeed Optimization Script for Chit Fund API\n";
echo "================================================\n\n";

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die("This script must be run from command line\n");
}

// Configuration
$base_dir = dirname(__DIR__);
$config_dir = $base_dir . '/config';
$cache_dir = $base_dir . '/cache';
$logs_dir = $base_dir . '/logs';
$uploads_dir = $base_dir . '/uploads';

echo "📁 Setting up directory structure...\n";

// Create necessary directories
$directories = [
    $cache_dir => 0755,
    $logs_dir => 0755,
    $uploads_dir => 0755,
    $base_dir . '/sessions' => 0755,
    $base_dir . '/backups' => 0755
];

foreach ($directories as $dir => $permission) {
    if (!is_dir($dir)) {
        if (mkdir($dir, $permission, true)) {
            echo "✅ Created directory: $dir\n";
        } else {
            echo "❌ Failed to create directory: $dir\n";
        }
    } else {
        echo "✅ Directory exists: $dir\n";
    }
}

echo "\n🔒 Setting up security files...\n";

// Create .htaccess files for security
$security_files = [
    $config_dir . '/.htaccess' => "Order deny,allow\nDeny from all",
    $logs_dir . '/.htaccess' => "Order deny,allow\nDeny from all",
    $cache_dir . '/.htaccess' => "Order deny,allow\nDeny from all",
    $uploads_dir . '/.htaccess' => "<FilesMatch \"\\.php$\">\nOrder allow,deny\nDeny from all\n</FilesMatch>",
];

foreach ($security_files as $file => $content) {
    if (file_put_contents($file, $content)) {
        echo "✅ Created security file: $file\n";
    } else {
        echo "❌ Failed to create security file: $file\n";
    }
}

echo "\n⚙️ Optimizing PHP configuration...\n";

// Create optimized php.ini for shared hosting
$php_ini_content = "; LiteSpeed Optimized PHP Configuration
; Place this in your public_html directory or ask hosting provider to apply

; Memory and execution limits
memory_limit = 128M
max_execution_time = 30
max_input_time = 30

; File upload limits
post_max_size = 8M
upload_max_filesize = 2M
max_file_uploads = 5

; Session configuration
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1
session.gc_maxlifetime = 7200

; Error handling
display_errors = Off
log_errors = On
error_log = logs/php_errors.log

; Performance optimizations
realpath_cache_size = 4096K
realpath_cache_ttl = 600
output_buffering = 4096

; OPcache settings (if available)
opcache.enable = 1
opcache.memory_consumption = 128
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1

; Compression
zlib.output_compression = On
zlib.output_compression_level = 6
";

$php_ini_file = $base_dir . '/php.ini.example';
if (file_put_contents($php_ini_file, $php_ini_content)) {
    echo "✅ Created PHP configuration example: $php_ini_file\n";
} else {
    echo "❌ Failed to create PHP configuration\n";
}

echo "\n🗄️ Optimizing database configuration...\n";

// Create database optimization script
$db_optimize_content = "<?php
/**
 * Database Optimization Script
 * Run this periodically to optimize database performance
 */

require_once __DIR__ . '/../config/config.php';

try {
    \$pdo = new PDO(
        \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=\" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo \"🗄️ Optimizing database tables...\\n\";
    
    // Get all tables
    \$tables = \$pdo->query(\"SHOW TABLES\")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach (\$tables as \$table) {
        // Optimize table
        \$pdo->exec(\"OPTIMIZE TABLE `\$table`\");
        echo \"✅ Optimized table: \$table\\n\";
        
        // Analyze table
        \$pdo->exec(\"ANALYZE TABLE `\$table`\");
        echo \"📊 Analyzed table: \$table\\n\";
    }
    
    echo \"\\n✅ Database optimization completed!\\n\";
    
} catch (Exception \$e) {
    echo \"❌ Database optimization failed: \" . \$e->getMessage() . \"\\n\";
}
?>";

$db_optimize_file = $base_dir . '/scripts/optimize_database.php';
if (file_put_contents($db_optimize_file, $db_optimize_content)) {
    echo "✅ Created database optimization script: $db_optimize_file\n";
} else {
    echo "❌ Failed to create database optimization script\n";
}

echo "\n🧹 Creating cache cleanup script...\n";

// Create cache cleanup script
$cache_cleanup_content = "<?php
/**
 * Cache Cleanup Script
 * Run this via cron job to clean up old cache files
 */

\$cache_dir = __DIR__ . '/../cache/';
\$max_age = 3600; // 1 hour

if (is_dir(\$cache_dir)) {
    \$files = glob(\$cache_dir . '*');
    \$cleaned = 0;
    
    foreach (\$files as \$file) {
        if (is_file(\$file) && (time() - filemtime(\$file)) > \$max_age) {
            if (unlink(\$file)) {
                \$cleaned++;
            }
        }
    }
    
    echo \"🧹 Cleaned up \$cleaned cache files\\n\";
} else {
    echo \"❌ Cache directory not found\\n\";
}
?>";

$cache_cleanup_file = $base_dir . '/scripts/cleanup_cache.php';
if (file_put_contents($cache_cleanup_file, $cache_cleanup_content)) {
    echo "✅ Created cache cleanup script: $cache_cleanup_file\n";
} else {
    echo "❌ Failed to create cache cleanup script\n";
}

echo "\n📝 Creating log rotation script...\n";

// Create log rotation script
$log_rotation_content = "<?php
/**
 * Log Rotation Script
 * Run this daily to rotate and compress log files
 */

\$logs_dir = __DIR__ . '/../logs/';
\$max_files = 7; // Keep 7 days of logs
\$max_size = 10 * 1024 * 1024; // 10MB

if (is_dir(\$logs_dir)) {
    \$log_files = glob(\$logs_dir . '*.log');
    
    foreach (\$log_files as \$log_file) {
        // Check if file is too large
        if (filesize(\$log_file) > \$max_size) {
            \$backup_file = \$log_file . '.' . date('Y-m-d-H-i-s');
            if (rename(\$log_file, \$backup_file)) {
                echo \"📦 Rotated log file: \" . basename(\$log_file) . \"\\n\";
                
                // Compress the backup
                if (function_exists('gzopen')) {
                    \$gz_file = \$backup_file . '.gz';
                    \$fp_in = fopen(\$backup_file, 'rb');
                    \$fp_out = gzopen(\$gz_file, 'wb9');
                    
                    while (!\feof(\$fp_in)) {
                        gzwrite(\$fp_out, fread(\$fp_in, 8192));
                    }
                    
                    fclose(\$fp_in);
                    gzclose(\$fp_out);
                    unlink(\$backup_file);
                    
                    echo \"🗜️ Compressed log file: \" . basename(\$gz_file) . \"\\n\";
                }
            }
        }
    }
    
    // Clean up old backup files
    \$backup_files = glob(\$logs_dir . '*.gz');
    if (count(\$backup_files) > \$max_files) {
        usort(\$backup_files, function(\$a, \$b) {
            return filemtime(\$a) - filemtime(\$b);
        });
        
        \$files_to_delete = array_slice(\$backup_files, 0, count(\$backup_files) - \$max_files);
        foreach (\$files_to_delete as \$file) {
            if (unlink(\$file)) {
                echo \"🗑️ Deleted old log file: \" . basename(\$file) . \"\\n\";
            }
        }
    }
    
    echo \"✅ Log rotation completed!\\n\";
} else {
    echo \"❌ Logs directory not found\\n\";
}
?>";

$log_rotation_file = $base_dir . '/scripts/rotate_logs.php';
if (file_put_contents($log_rotation_file, $log_rotation_content)) {
    echo "✅ Created log rotation script: $log_rotation_file\n";
} else {
    echo "❌ Failed to create log rotation script\n";
}

echo "\n⏰ Creating cron job examples...\n";

// Create cron job examples
$cron_content = "# LiteSpeed Chit Fund API Cron Jobs
# Add these to your cPanel cron jobs or ask hosting provider

# Database optimization (weekly)
0 2 * * 0 /usr/bin/php /path/to/your/api/scripts/optimize_database.php

# Cache cleanup (hourly)
0 * * * * /usr/bin/php /path/to/your/api/scripts/cleanup_cache.php

# Log rotation (daily)
0 1 * * * /usr/bin/php /path/to/your/api/scripts/rotate_logs.php

# Health check (every 5 minutes)
*/5 * * * * curl -s https://yourdomain.com/api/health > /dev/null

# Backup database (daily)
0 3 * * * mysqldump -u username -p'password' database_name > /path/to/backups/backup_\$(date +\\%Y\\%m\\%d).sql
";

$cron_file = $base_dir . '/cron_examples.txt';
if (file_put_contents($cron_file, $cron_content)) {
    echo "✅ Created cron job examples: $cron_file\n";
} else {
    echo "❌ Failed to create cron job examples\n";
}

echo "\n🔧 Creating maintenance script...\n";

// Create maintenance script
$maintenance_content = "<?php
/**
 * Maintenance Mode Toggle Script
 */

\$config_file = __DIR__ . '/../config/env.php';

if (!\$argc || \$argc < 2) {
    echo \"Usage: php maintenance.php [on|off]\\n\";
    exit(1);
}

\$mode = \$argv[1];

if (!\in_array(\$mode, ['on', 'off'])) {
    echo \"Invalid mode. Use 'on' or 'off'\\n\";
    exit(1);
}

if (!file_exists(\$config_file)) {
    echo \"Configuration file not found: \$config_file\\n\";
    exit(1);
}

\$config = file_get_contents(\$config_file);

if (\$mode === 'on') {
    \$config = preg_replace(
        \"/define\\('MAINTENANCE_MODE', false\\);/\",
        \"define('MAINTENANCE_MODE', true);\",
        \$config
    );
    echo \"🔧 Maintenance mode enabled\\n\";
} else {
    \$config = preg_replace(
        \"/define\\('MAINTENANCE_MODE', true\\);/\",
        \"define('MAINTENANCE_MODE', false);\",
        \$config
    );
    echo \"✅ Maintenance mode disabled\\n\";
}

if (file_put_contents(\$config_file, \$config)) {
    echo \"Configuration updated successfully\\n\";
} else {
    echo \"❌ Failed to update configuration\\n\";
    exit(1);
}
?>";

$maintenance_file = $base_dir . '/scripts/maintenance.php';
if (file_put_contents($maintenance_file, $maintenance_content)) {
    echo "✅ Created maintenance script: $maintenance_file\n";
} else {
    echo "❌ Failed to create maintenance script\n";
}

echo "\n📊 Creating performance monitoring script...\n";

// Create performance monitoring script
$monitor_content = "<?php
/**
 * Performance Monitoring Script
 */

require_once __DIR__ . '/../config/config.php';

echo \"📊 Performance Monitoring Report\\n\";
echo \"==============================\\n\\n\";

// Check PHP version and extensions
echo \"🐘 PHP Information:\\n\";
echo \"Version: \" . PHP_VERSION . \"\\n\";
echo \"Memory Limit: \" . ini_get('memory_limit') . \"\\n\";
echo \"Max Execution Time: \" . ini_get('max_execution_time') . \"s\\n\";

// Check OPcache
if (function_exists('opcache_get_status')) {
    \$opcache = opcache_get_status();
    echo \"OPcache: \" . (\$opcache['opcache_enabled'] ? 'Enabled' : 'Disabled') . \"\\n\";
} else {
    echo \"OPcache: Not available\\n\";
}

echo \"\\n🗄️ Database Information:\\n\";

try {
    \$pdo = new PDO(
        \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME,
        DB_USER,
        DB_PASS
    );
    
    // Database version
    \$version = \$pdo->query('SELECT VERSION()')->fetchColumn();
    echo \"MySQL Version: \$version\\n\";
    
    // Database size
    \$size_query = \"SELECT 
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = '\" . DB_NAME . \"'\";
    \$size = \$pdo->query(\$size_query)->fetchColumn();
    echo \"Database Size: {$size} MB\\n\";
    
    // Table count
    \$table_count = \$pdo->query(\"SHOW TABLES\")->rowCount();
    echo \"Tables: \$table_count\\n\";
    
} catch (Exception \$e) {
    echo \"Database: Connection failed\\n\";
}

echo \"\\n📁 File System Information:\\n\";

// Directory sizes
\$directories = [
    'Cache' => __DIR__ . '/../cache/',
    'Logs' => __DIR__ . '/../logs/',
    'Uploads' => __DIR__ . '/../uploads/'
];

foreach (\$directories as \$name => \$dir) {
    if (is_dir(\$dir)) {
        \$size = 0;
        \$files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(\$dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach (\$files as \$file) {
            \$size += \$file->getSize();
        }
        
        echo \"\$name: \" . round(\$size / 1024 / 1024, 2) . \" MB\\n\";
    } else {
        echo \"\$name: Directory not found\\n\";
    }
}

echo \"\\n✅ Monitoring completed!\\n\";
?>";

$monitor_file = $base_dir . '/scripts/monitor.php';
if (file_put_contents($monitor_file, $monitor_content)) {
    echo "✅ Created performance monitoring script: $monitor_file\n";
} else {
    echo "❌ Failed to create performance monitoring script\n";
}

echo "\n🎉 LiteSpeed Optimization Complete!\n";
echo "==================================\n\n";

echo "📋 Next Steps:\n";
echo "1. Copy config/env.litespeed.php to config/env.php and update with your settings\n";
echo "2. Upload the optimized .htaccess file to your public_html directory\n";
echo "3. Set up the cron jobs from cron_examples.txt\n";
echo "4. Test your API with: curl https://yourdomain.com/api/health\n";
echo "5. Monitor performance with: php scripts/monitor.php\n\n";

echo "🔧 Maintenance Commands:\n";
echo "- Enable maintenance: php scripts/maintenance.php on\n";
echo "- Disable maintenance: php scripts/maintenance.php off\n";
echo "- Clean cache: php scripts/cleanup_cache.php\n";
echo "- Optimize database: php scripts/optimize_database.php\n";
echo "- Rotate logs: php scripts/rotate_logs.php\n";
echo "- Monitor performance: php scripts/monitor.php\n\n";

echo "🚀 Your Chit Fund API is now optimized for LiteSpeed shared hosting!\n";

?>
