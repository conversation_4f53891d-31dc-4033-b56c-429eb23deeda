// API Response models for handling server responses

class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final Map<String, dynamic>? details;
  final int? errorCode;

  const ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.details,
    this.errorCode,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] != null ? fromJsonT(json['data']) : null,
      details: json['details'] as Map<String, dynamic>?,
      errorCode: json['error_code'] as int?,
    );
  }

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) {
    return {
      'success': success,
      'message': message,
      'data': data != null ? toJsonT(data as T) : null,
      'details': details,
      'error_code': errorCode,
    };
  }

  // Factory constructors for common response types
  factory ApiResponse.success({
    required String message,
    T? data,
  }) {
    return ApiResponse<T>(
      success: true,
      message: message,
      data: data,
    );
  }

  factory ApiResponse.error({
    required String message,
    Map<String, dynamic>? details,
    int? errorCode,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      details: details,
      errorCode: errorCode,
    );
  }

  // Check if response is successful
  bool get isSuccess => success;
  bool get isError => !success;

  // Get error message or null if successful
  String? get errorMessage => isError ? message : null;

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, hasData: ${data != null})';
  }
}

// Specialized response types for common use cases
class LoginResponse {
  final String token;
  final String refreshToken;
  final Map<String, dynamic> user;

  const LoginResponse({
    required this.token,
    required this.refreshToken,
    required this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      token: json['token'] as String? ?? '',
      refreshToken: json['refresh_token'] as String? ?? '',
      user: json['user'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'refresh_token': refreshToken,
      'user': user,
    };
  }
}

class PaginatedResponse<T> {
  final List<T> data;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginatedResponse({
    required this.data,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) {
    return PaginatedResponse<T>(
      data: (json['data'] as List).map((item) => fromJsonT(item)).toList(),
      currentPage: json['current_page'] as int,
      totalPages: json['total_pages'] as int,
      totalItems: json['total_items'] as int,
      itemsPerPage: json['items_per_page'] as int,
      hasNextPage: json['has_next_page'] as bool,
      hasPreviousPage: json['has_previous_page'] as bool,
    );
  }

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) {
    return {
      'data': data.map((item) => toJsonT(item)).toList(),
      'current_page': currentPage,
      'total_pages': totalPages,
      'total_items': totalItems,
      'items_per_page': itemsPerPage,
      'has_next_page': hasNextPage,
      'has_previous_page': hasPreviousPage,
    };
  }

  bool get isEmpty => data.isEmpty;
  bool get isNotEmpty => data.isNotEmpty;
  int get length => data.length;
}

// Error details for validation errors
class ValidationError {
  final String field;
  final String message;
  final dynamic value;

  const ValidationError({
    required this.field,
    required this.message,
    this.value,
  });

  factory ValidationError.fromJson(Map<String, dynamic> json) {
    return ValidationError(
      field: json['field'] as String,
      message: json['message'] as String,
      value: json['value'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'field': field,
      'message': message,
      'value': value,
    };
  }

  @override
  String toString() {
    return 'ValidationError(field: $field, message: $message)';
  }
}

// Network error types
enum NetworkErrorType {
  connectionTimeout,
  sendTimeout,
  receiveTimeout,
  badResponse,
  cancel,
  connectionError,
  unknown,
}

class NetworkError {
  final NetworkErrorType type;
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? details;

  const NetworkError({
    required this.type,
    required this.message,
    this.statusCode,
    this.details,
  });

  factory NetworkError.connectionTimeout() {
    return const NetworkError(
      type: NetworkErrorType.connectionTimeout,
      message: 'Connection timeout. Please check your internet connection.',
    );
  }

  factory NetworkError.sendTimeout() {
    return const NetworkError(
      type: NetworkErrorType.sendTimeout,
      message: 'Request timeout. Please try again.',
    );
  }

  factory NetworkError.receiveTimeout() {
    return const NetworkError(
      type: NetworkErrorType.receiveTimeout,
      message: 'Response timeout. Please try again.',
    );
  }

  factory NetworkError.badResponse(int statusCode, String message) {
    return NetworkError(
      type: NetworkErrorType.badResponse,
      message: message,
      statusCode: statusCode,
    );
  }

  factory NetworkError.connectionError() {
    return const NetworkError(
      type: NetworkErrorType.connectionError,
      message: 'No internet connection. Please check your network.',
    );
  }

  factory NetworkError.unknown(String message) {
    return NetworkError(
      type: NetworkErrorType.unknown,
      message: message,
    );
  }

  @override
  String toString() {
    return 'NetworkError(type: $type, message: $message, statusCode: $statusCode)';
  }
}

// Result type for handling success/error states
abstract class Result<T> {
  const Result();

  bool get isSuccess => this is Success<T>;
  bool get isError => this is Error<T>;

  T? get data => isSuccess ? (this as Success<T>).data : null;
  String? get error => isError ? (this as Error<T>).message : null;

  // Transform the result
  Result<R> map<R>(R Function(T data) transform) {
    if (isSuccess) {
      try {
        return Success(transform((this as Success<T>).data));
      } catch (e) {
        return Error(e.toString());
      }
    }
    return Error((this as Error<T>).message);
  }

  // Handle the result
  R when<R>({
    required R Function(T data) success,
    required R Function(String error) error,
  }) {
    if (isSuccess) {
      return success((this as Success<T>).data);
    } else {
      return error((this as Error<T>).message);
    }
  }
}

class Success<T> extends Result<T> {
  @override
  final T data;

  const Success(this.data);

  @override
  String toString() => 'Success(data: $data)';
}

class Error<T> extends Result<T> {
  final String message;
  final dynamic details;

  const Error(this.message, [this.details]);

  @override
  String toString() => 'Error(message: $message)';
}
