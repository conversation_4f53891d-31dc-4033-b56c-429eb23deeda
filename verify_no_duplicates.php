<?php
/**
 * Verify No Duplicate Methods Script
 * 
 * Checks all PHP controller files for duplicate methods
 */

echo "🔍 DUPLICATE METHODS VERIFICATION\n";
echo "=================================\n\n";

$controllers = [
    'backend/controllers/ChitController.php',
    'backend/controllers/UserController.php',
    'backend/controllers/AuthController.php',
    'backend/controllers/PaymentController.php',
    'backend/controllers/MemberController.php',
    'backend/controllers/BiddingController.php',
    'backend/controllers/ReportController.php',
    'backend/controllers/NotificationController.php'
];

$totalDuplicates = 0;
$allMethods = [];

foreach ($controllers as $controller) {
    if (!file_exists($controller)) {
        echo "⚠️ File not found: $controller\n";
        continue;
    }
    
    echo "📁 Checking: " . basename($controller) . "\n";
    
    $content = file_get_contents($controller);
    
    // Find all method declarations
    preg_match_all('/public function (\w+)\s*\(/', $content, $matches);
    $methods = $matches[1];
    
    // Count method occurrences
    $methodCounts = array_count_values($methods);
    $duplicates = array_filter($methodCounts, function($count) { return $count > 1; });
    
    if (empty($duplicates)) {
        echo "   ✅ No duplicates found (" . count($methods) . " methods)\n";
    } else {
        echo "   ❌ Duplicates found:\n";
        foreach ($duplicates as $method => $count) {
            echo "      - $method(): $count occurrences\n";
            $totalDuplicates++;
        }
    }
    
    // Store all methods for global check
    $allMethods[basename($controller)] = $methods;
    
    echo "\n";
}

// ================================================================
// SUMMARY
// ================================================================
echo "📊 VERIFICATION SUMMARY:\n";
echo "========================\n\n";

if ($totalDuplicates === 0) {
    echo "🎉 SUCCESS: No duplicate methods found!\n";
    echo "✅ All controllers are clean and ready for production.\n\n";
} else {
    echo "❌ ISSUES FOUND: $totalDuplicates duplicate methods detected.\n";
    echo "🔧 Please fix the duplicates listed above.\n\n";
}

// ================================================================
// DETAILED METHOD LISTING
// ================================================================
echo "📋 DETAILED METHOD LISTING:\n";
echo "===========================\n\n";

foreach ($allMethods as $controller => $methods) {
    echo "📁 $controller:\n";
    $uniqueMethods = array_unique($methods);
    sort($uniqueMethods);
    
    foreach ($uniqueMethods as $method) {
        $count = array_count_values($methods)[$method];
        $status = $count > 1 ? "❌ ($count times)" : "✅";
        echo "   - $method() $status\n";
    }
    echo "\n";
}

// ================================================================
// SPECIFIC CHITCONTROLLER CHECK
// ================================================================
if (file_exists('backend/controllers/ChitController.php')) {
    echo "🎯 CHITCONTROLLER SPECIFIC CHECK:\n";
    echo "=================================\n\n";
    
    $content = file_get_contents('backend/controllers/ChitController.php');
    
    // Check for specific problematic methods
    $problematicMethods = ['addMember', 'removeMember', 'getMembers', 'getChitMembers'];
    
    foreach ($problematicMethods as $method) {
        $count = preg_match_all("/public function $method\s*\(/", $content);
        if ($count > 1) {
            echo "❌ $method(): $count occurrences (DUPLICATE)\n";
        } elseif ($count === 1) {
            echo "✅ $method(): $count occurrence (GOOD)\n";
        } else {
            echo "⚠️ $method(): $count occurrences (MISSING)\n";
        }
    }
    
    echo "\n";
    
    // Check for authentication patterns
    echo "🔐 AUTHENTICATION PATTERNS:\n";
    $jwtCount = preg_match_all('/JWT::getUserIdFromToken/', $content);
    $authUserCount = preg_match_all('/authenticateUser\(\)/', $content);
    
    echo "   - JWT::getUserIdFromToken: $jwtCount occurrences\n";
    echo "   - authenticateUser(): $authUserCount occurrences\n";
    
    if ($jwtCount > 0 && $authUserCount > 0) {
        echo "   ⚠️ Mixed authentication patterns detected\n";
        echo "   💡 Consider standardizing on authenticateUser()\n";
    } else {
        echo "   ✅ Consistent authentication pattern\n";
    }
}

// ================================================================
// RECOMMENDATIONS
// ================================================================
echo "\n💡 RECOMMENDATIONS:\n";
echo "===================\n\n";

if ($totalDuplicates > 0) {
    echo "🔧 IMMEDIATE ACTIONS NEEDED:\n";
    echo "1. Remove duplicate methods as identified above\n";
    echo "2. Keep the enhanced versions (usually the later ones)\n";
    echo "3. Test API endpoints after removal\n";
    echo "4. Run this script again to verify fixes\n\n";
} else {
    echo "🎉 ALL GOOD! Your controllers are clean.\n\n";
    echo "✅ NEXT STEPS:\n";
    echo "1. Test your API endpoints\n";
    echo "2. Deploy to production\n";
    echo "3. Monitor for any runtime issues\n\n";
}

echo "📋 QUICK FIX GUIDE:\n";
echo "For ChitController.php duplicates, see: DUPLICATE_METHODS_FIX_GUIDE.md\n\n";

echo "🚀 Verification complete!\n";

?>
