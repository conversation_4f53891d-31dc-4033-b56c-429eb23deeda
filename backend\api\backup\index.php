<?php
/**
 * Backup API Routes
 */

require_once __DIR__ . '/../../utils/BackupService.php';
require_once __DIR__ . '/../../utils/helpers.php';
require_once __DIR__ . '/../../utils/JWT.php';

// Get remaining path segments
$segments = array_slice(explode('/', trim($_SERVER['REQUEST_URI'], '/')), 2);
$action = $segments[0] ?? '';

// Verify authentication for all backup operations
$userId = JWT::getUserIdFromToken();
if (!$userId) {
    sendError('Authentication required', 401);
}

// Check if user has admin privileges for backup operations
try {
    $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user || $user['role'] !== 'admin') {
        sendError('Admin privileges required', 403);
    }
} catch (Exception $e) {
    sendError('Authentication error', 500);
}

switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        switch ($action) {
            case 'create-database':
                $result = BackupService::createDatabaseBackup($userId);
                if ($result['success']) {
                    sendSuccess($result, $result['message']);
                } else {
                    sendError($result['message'], 500);
                }
                break;
                
            case 'create-files':
                $result = BackupService::createFilesBackup($userId);
                if ($result['success']) {
                    sendSuccess($result, $result['message']);
                } else {
                    sendError($result['message'], 500);
                }
                break;
                
            case 'create-complete':
                $result = BackupService::createCompleteBackup($userId);
                if ($result['success']) {
                    sendSuccess($result, $result['message']);
                } else {
                    sendError($result['message'], 500);
                }
                break;
                
            default:
                sendError('Invalid backup action', 400);
                break;
        }
        break;
        
    case 'GET':
        switch ($action) {
            case 'list':
                $backups = BackupService::listBackups();
                sendSuccess($backups, 'Backups retrieved successfully');
                break;
                
            case 'download':
                $data = getRequestData();
                if (!isset($data['filename'])) {
                    sendError('Filename required', 400);
                }
                
                $result = BackupService::downloadBackup($data['filename']);
                if ($result['success']) {
                    // Set headers for file download
                    header('Content-Type: ' . $result['mime_type']);
                    header('Content-Disposition: attachment; filename="' . basename($data['filename']) . '"');
                    header('Content-Length: ' . $result['file_size']);
                    
                    // Output file content
                    readfile($result['file_path']);
                    exit();
                } else {
                    sendError($result['message'], 404);
                }
                break;
                
            case 'stats':
                $stats = BackupService::getBackupStats();
                sendSuccess($stats, 'Backup statistics retrieved successfully');
                break;
                
            default:
                $backups = BackupService::listBackups();
                sendSuccess($backups, 'Backups retrieved successfully');
                break;
        }
        break;
        
    case 'DELETE':
        $data = getRequestData();
        if (!isset($data['filename'])) {
            sendError('Filename required', 400);
        }
        
        $result = BackupService::deleteBackup($data['filename'], $userId);
        if ($result['success']) {
            sendSuccess(null, $result['message']);
        } else {
            sendError($result['message'], 404);
        }
        break;
        
    default:
        sendError('Method not allowed', 405);
        break;
}
?>
