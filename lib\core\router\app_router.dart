import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../providers/auth_provider.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/otp_verification_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../features/chit/screens/chit_list_screen.dart';
import '../../features/chit/screens/chit_detail_screen.dart';
import '../../features/chit/screens/create_chit_screen.dart';
import '../../features/chit/screens/join_chit_screen.dart';
import '../../features/payment/screens/make_payment_screen.dart';
import '../../features/payments/screens/payment_list_screen.dart';
import '../../features/reports/screens/reports_screen.dart';
import '../../features/notifications/screens/notifications_screen.dart';
import '../../features/activity/screens/activity_log_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../features/settings/screens/notification_settings_screen.dart';
import '../../features/settings/screens/security_settings_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/edit_profile_screen.dart';
import '../../features/profile/screens/change_password_screen.dart';
import '../../shared/screens/splash_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/splash',
    redirect: (context, state) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final isAuthenticated = authProvider.isAuthenticated;
      final isInitialized = authProvider.status != AuthStatus.initial;

      // If not initialized, stay on splash
      if (!isInitialized) {
        return '/splash';
      }

      // If on splash and initialized, redirect based on auth status
      if (state.uri.toString() == '/splash') {
        return isAuthenticated ? '/dashboard' : '/login';
      }

      // If not authenticated and trying to access protected routes
      if (!isAuthenticated && _isProtectedRoute(state.uri.toString())) {
        return '/login';
      }

      // If authenticated and trying to access auth routes
      if (isAuthenticated && _isAuthRoute(state.uri.toString())) {
        return '/dashboard';
      }

      return null; // No redirect needed
    },
    routes: [
      // Splash Screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/otp-verification',
        name: 'otp-verification',
        builder: (context, state) {
          final extra = state.extra as Map<String, String>?;
          return OtpVerificationScreen(
            email: extra?['email'] ?? '',
            name: extra?['name'] ?? '',
            phone: extra?['phone'] ?? '',
            password: extra?['password'] ?? '',
          );
        },
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      // Main App Routes
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),

      // Chit Routes
      GoRoute(
        path: '/chits',
        name: 'chits',
        builder: (context, state) => const ChitListScreen(),
        routes: [
          GoRoute(
            path: '/create',
            name: 'create-chit',
            builder: (context, state) => const CreateChitScreen(),
          ),
          GoRoute(
            path: '/join',
            name: 'join-chit',
            builder: (context, state) => const JoinChitScreen(),
          ),
          GoRoute(
            path: '/:chitId',
            name: 'chit-detail',
            builder: (context, state) {
              final chitId = state.pathParameters['chitId']!;
              return ChitDetailScreen(chitId: chitId);
            },
          ),
        ],
      ),

      // Payment Routes
      GoRoute(
        path: '/payments',
        name: 'payments',
        builder: (context, state) => const PaymentListScreen(),
        routes: [
          GoRoute(
            path: '/make',
            name: 'make-payment',
            builder: (context, state) => const MakePaymentScreen(),
          ),
        ],
      ),

      // Reports Route
      GoRoute(
        path: '/reports',
        name: 'reports',
        builder: (context, state) => const ReportsScreen(),
      ),

      // Notifications Route
      GoRoute(
        path: '/notifications',
        name: 'notifications',
        builder: (context, state) => const NotificationsScreen(),
      ),

      // Activity Route
      GoRoute(
        path: '/activity',
        name: 'activity',
        builder: (context, state) => const ActivityLogScreen(),
      ),

      // Settings Route
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
        routes: [
          GoRoute(
            path: '/notifications',
            name: 'notification-settings',
            builder: (context, state) => const NotificationSettingsScreen(),
          ),
          GoRoute(
            path: '/security',
            name: 'security-settings',
            builder: (context, state) => const SecuritySettingsScreen(),
          ),
        ],
      ),

      // Profile Routes
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
        routes: [
          GoRoute(
            path: '/edit',
            name: 'edit-profile',
            builder: (context, state) => const EditProfileScreen(),
          ),
          GoRoute(
            path: '/change-password',
            name: 'change-password',
            builder: (context, state) => const ChangePasswordScreen(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );

  // Helper methods
  static bool _isProtectedRoute(String location) {
    const protectedRoutes = [
      '/dashboard',
      '/chits',
      '/payments',
      '/reports',
      '/notifications',
      '/activity',
      '/settings',
      '/profile',
    ];

    return protectedRoutes.any((route) => location.startsWith(route));
  }

  static bool _isAuthRoute(String location) {
    const authRoutes = [
      '/login',
      '/register',
      '/otp-verification',
      '/forgot-password',
    ];

    return authRoutes.contains(location);
  }
}

// Navigation helper class
class AppNavigation {
  static void goToLogin(BuildContext context) {
    context.go('/login');
  }

  static void goToRegister(BuildContext context) {
    context.go('/register');
  }

  static void goToOtpVerification(
    BuildContext context, {
    required String email,
    required String name,
    required String phone,
    required String password,
  }) {
    context.go('/otp-verification', extra: {
      'email': email,
      'name': name,
      'phone': phone,
      'password': password,
    });
  }

  static void goToForgotPassword(BuildContext context) {
    context.go('/forgot-password');
  }

  static void goToDashboard(BuildContext context) {
    context.go('/dashboard');
  }

  static void goToChits(BuildContext context) {
    context.go('/chits');
  }

  static void goToCreateChit(BuildContext context) {
    context.go('/chits/create');
  }

  static void goToChitDetail(BuildContext context, String chitId) {
    context.go('/chits/$chitId');
  }

  static void goToProfile(BuildContext context) {
    context.go('/profile');
  }

  // Dashboard navigation
  static void goToNotifications(BuildContext context) {
    context.go('/notifications');
  }

  static void goToSettings(BuildContext context) {
    context.go('/settings');
  }

  // Chit management navigation
  static void goToChitList(BuildContext context) {
    context.go('/chits');
  }

  static void goToChitDetails(BuildContext context, String chitId) {
    context.go('/chits/$chitId');
  }

  static void goToJoinChit(BuildContext context) {
    context.go('/chits/join');
  }

  // Payment navigation
  static void goToPayments(BuildContext context) {
    context.go('/payments');
  }

  static void goToMakePayment(BuildContext context) {
    context.go('/payments/make');
  }

  // Reports navigation
  static void goToReports(BuildContext context) {
    context.go('/reports');
  }

  // Activity navigation
  static void goToActivityLog(BuildContext context) {
    context.go('/activity');
  }

  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    } else {
      context.go('/dashboard');
    }
  }
}
