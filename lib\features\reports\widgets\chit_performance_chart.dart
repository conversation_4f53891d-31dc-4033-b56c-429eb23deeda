import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/models/chit_model.dart';

class ChitPerformanceChart extends StatelessWidget {
  final DateTimeRange? dateRange;
  final String? chitId;

  const ChitPerformanceChart({
    super.key,
    this.dateRange,
    this.chitId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ChitProvider>(
      builder: (context, chitProvider, child) {
        final performanceData = _calculatePerformanceData(chitProvider);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Chit Performance',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        // Handle chart type selection
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'bar',
                          child: Text('Bar Chart'),
                        ),
                        const PopupMenuItem(
                          value: 'line',
                          child: Text('Line Chart'),
                        ),
                        const PopupMenuItem(
                          value: 'pie',
                          child: Text('Pie Chart'),
                        ),
                      ],
                      child: const Icon(Icons.more_vert),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Chart placeholder with performance metrics
                Container(
                  height: 250,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Chart header
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildChartLegend(context, 'Collection Rate', Colors.blue),
                            _buildChartLegend(context, 'Completion Rate', Colors.green),
                            _buildChartLegend(context, 'Member Satisfaction', Colors.orange),
                          ],
                        ),
                      ),
                      
                      // Chart area
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: _buildPerformanceBars(context, performanceData),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Performance summary
                _buildPerformanceSummary(context, performanceData),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildChartLegend(BuildContext context, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildPerformanceBars(BuildContext context, Map<String, dynamic> data) {
    final chits = data['chits'] as List<Map<String, dynamic>>;
    
    if (chits.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No performance data available',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: chits.take(5).map((chitData) {
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                // Chit name
                SizedBox(
                  width: 80,
                  child: Text(
                    chitData['name'],
                    style: Theme.of(context).textTheme.bodySmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // Performance bars
                Expanded(
                  child: Row(
                    children: [
                      // Collection rate bar
                      Expanded(
                        child: Container(
                          height: 20,
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: chitData['collectionRate'] / 100,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(width: 4),
                      
                      // Completion rate bar
                      Expanded(
                        child: Container(
                          height: 20,
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: chitData['completionRate'] / 100,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(width: 4),
                      
                      // Satisfaction bar
                      Expanded(
                        child: Container(
                          height: 20,
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: chitData['satisfaction'] / 100,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // Overall score
                Text(
                  '${chitData['overallScore'].toStringAsFixed(1)}%',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getScoreColor(chitData['overallScore']),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPerformanceSummary(BuildContext context, Map<String, dynamic> data) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryItem(
            context,
            'Best Performer',
            data['bestPerformer'] ?? 'N/A',
            Icons.emoji_events,
            const Color(0xFFFFD700), // Gold color
          ),
        ),
        Expanded(
          child: _buildSummaryItem(
            context,
            'Avg. Collection',
            '${data['avgCollection'].toStringAsFixed(1)}%',
            Icons.trending_up,
            Colors.blue,
          ),
        ),
        Expanded(
          child: _buildSummaryItem(
            context,
            'Completion Rate',
            '${data['avgCompletion'].toStringAsFixed(1)}%',
            Icons.check_circle,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Map<String, dynamic> _calculatePerformanceData(ChitProvider chitProvider) {
    final chits = chitProvider.chits;
    
    // Filter chits based on criteria
    final relevantChits = chitId != null
        ? chits.where((c) => c.id == chitId)
        : chits;

    final chitPerformanceList = relevantChits.map((chit) {
      // Calculate collection rate (placeholder logic)
      final collectionRate = 85.0 + (chit.id.hashCode % 20); // Simulated data
      
      // Calculate completion rate
      final completionRate = chit.status == ChitStatus.completed
          ? 100.0
          : (chit.biddingRounds.length / chit.numberOfMembers) * 100;
      
      // Calculate satisfaction (placeholder)
      final satisfaction = 80.0 + (chit.id.hashCode % 15); // Simulated data
      
      // Overall score
      final overallScore = (collectionRate + completionRate + satisfaction) / 3;
      
      return {
        'id': chit.id,
        'name': chit.name,
        'collectionRate': collectionRate.clamp(0.0, 100.0),
        'completionRate': completionRate.clamp(0.0, 100.0),
        'satisfaction': satisfaction.clamp(0.0, 100.0),
        'overallScore': overallScore.clamp(0.0, 100.0),
      };
    }).toList();

    // Sort by overall score
    chitPerformanceList.sort((a, b) => (b['overallScore'] as double).compareTo(a['overallScore'] as double));

    // Calculate averages
    final avgCollection = chitPerformanceList.isNotEmpty
        ? chitPerformanceList.map((c) => c['collectionRate'] as double).reduce((a, b) => a + b) / chitPerformanceList.length
        : 0.0;
    
    final avgCompletion = chitPerformanceList.isNotEmpty
        ? chitPerformanceList.map((c) => c['completionRate'] as double).reduce((a, b) => a + b) / chitPerformanceList.length
        : 0.0;

    final bestPerformer = chitPerformanceList.isNotEmpty
        ? chitPerformanceList.first['name']
        : null;

    return {
      'chits': chitPerformanceList,
      'avgCollection': avgCollection,
      'avgCompletion': avgCompletion,
      'bestPerformer': bestPerformer,
    };
  }

  Color _getScoreColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }
}


