import 'package:flutter/material.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/notification_service.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _pushNotifications = true;
  bool _emailNotifications = true;
  bool _paymentReminders = true;
  bool _biddingAlerts = true;
  bool _chitUpdates = true;
  bool _memberUpdates = true;
  bool _systemNotifications = true;
  bool _marketingEmails = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    setState(() {
      _pushNotifications = StorageService.areNotificationsEnabled();
      _emailNotifications = StorageService.getEmailNotifications();
      _paymentReminders = StorageService.getPaymentReminders();
      _biddingAlerts = StorageService.getBiddingAlerts();
      _chitUpdates = StorageService.getChitUpdates();
      _memberUpdates = StorageService.getMemberUpdates();
      _systemNotifications = StorageService.getSystemNotifications();
      _marketingEmails = StorageService.getMarketingEmails();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        children: [
          _buildSection(
            'Push Notifications',
            [
              _buildSwitchTile(
                'Enable Push Notifications',
                'Receive notifications on your device',
                _pushNotifications,
                (value) async {
                  setState(() {
                    _pushNotifications = value;
                  });
                  await StorageService.setNotificationsEnabled(value);
                  if (value) {
                    await NotificationService.requestPermissions();
                  }
                },
              ),
              _buildSwitchTile(
                'Payment Reminders',
                'Get notified about upcoming payments',
                _paymentReminders,
                (value) async {
                  setState(() {
                    _paymentReminders = value;
                  });
                  await StorageService.setPaymentReminders(value);
                },
                enabled: _pushNotifications,
              ),
              _buildSwitchTile(
                'Bidding Alerts',
                'Notifications for bidding rounds',
                _biddingAlerts,
                (value) async {
                  setState(() {
                    _biddingAlerts = value;
                  });
                  await StorageService.setBiddingAlerts(value);
                },
                enabled: _pushNotifications,
              ),
              _buildSwitchTile(
                'Chit Updates',
                'Updates about your chit funds',
                _chitUpdates,
                (value) async {
                  setState(() {
                    _chitUpdates = value;
                  });
                  await StorageService.setChitUpdates(value);
                },
                enabled: _pushNotifications,
              ),
              _buildSwitchTile(
                'Member Updates',
                'New members and member activities',
                _memberUpdates,
                (value) async {
                  setState(() {
                    _memberUpdates = value;
                  });
                  await StorageService.setMemberUpdates(value);
                },
                enabled: _pushNotifications,
              ),
              _buildSwitchTile(
                'System Notifications',
                'Important system updates and alerts',
                _systemNotifications,
                (value) async {
                  setState(() {
                    _systemNotifications = value;
                  });
                  await StorageService.setSystemNotifications(value);
                },
                enabled: _pushNotifications,
              ),
            ],
          ),
          _buildSection(
            'Email Notifications',
            [
              _buildSwitchTile(
                'Enable Email Notifications',
                'Receive notifications via email',
                _emailNotifications,
                (value) async {
                  setState(() {
                    _emailNotifications = value;
                  });
                  await StorageService.setEmailNotifications(value);
                },
              ),
              _buildSwitchTile(
                'Marketing Emails',
                'Promotional offers and updates',
                _marketingEmails,
                (value) async {
                  setState(() {
                    _marketingEmails = value;
                  });
                  await StorageService.setMarketingEmails(value);
                },
                enabled: _emailNotifications,
              ),
            ],
          ),
          _buildSection(
            'Notification Schedule',
            [
              ListTile(
                leading: const Icon(Icons.schedule),
                title: const Text('Quiet Hours'),
                subtitle: const Text('Set times when notifications are muted'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showQuietHoursDialog();
                },
              ),
              ListTile(
                leading: const Icon(Icons.vibration),
                title: const Text('Notification Sound'),
                subtitle: const Text('Choose notification sound'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showSoundDialog();
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: () {
                _testNotification();
              },
              child: const Text('Test Notification'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged, {
    bool enabled = true,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: TextStyle(
          color: enabled ? null : Theme.of(context).disabledColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: enabled ? null : Theme.of(context).disabledColor,
        ),
      ),
      value: enabled ? value : false,
      onChanged: enabled ? onChanged : null,
    );
  }

  void _showQuietHoursDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quiet Hours'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Set the time range when you don\'t want to receive notifications.'),
            SizedBox(height: 16),
            // TODO: Implement time picker for quiet hours
            Text('Feature coming soon'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSoundDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Sound'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Choose your notification sound preference.'),
            SizedBox(height: 16),
            // TODO: Implement sound selection
            Text('Feature coming soon'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _testNotification() async {
    await NotificationService.showLocalNotification(
      id: 999,
      title: 'Test Notification',
      body: 'This is a test notification from Chit Fund Manager',
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Test notification sent!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
