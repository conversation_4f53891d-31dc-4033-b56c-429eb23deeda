<?php
/**
 * Deployment Validation Script
 * 
 * Comprehensive validation for production deployment
 */

echo "🚀 DEPLOYMENT VALIDATION\n";
echo "========================\n\n";

$errors = [];
$warnings = [];
$success = [];

// 1. Check PHP syntax for all critical files
echo "1. 📝 CHECKING PHP SYNTAX\n";
echo "-------------------------\n";

$criticalFiles = [
    'backend/utils/helpers.php',
    'backend/config/database.php',
    'backend/api/index.php',
    'backend/controllers/AuthController.php',
    'backend/controllers/UserController.php',
    'backend/controllers/ChitController.php',
    'backend/security/AdvancedSecurityManager.php',
    'backend/security/ThreatDetectionEngine.php',
    'backend/security/DataProtectionManager.php',
    'backend/security/SecurityMonitor.php',
    'backend/middleware/SecurityMiddleware.php'
];

foreach ($criticalFiles as $file) {
    if (!file_exists($file)) {
        $errors[] = "Missing file: $file";
        echo "❌ $file - NOT FOUND\n";
        continue;
    }
    
    // Check for syntax errors
    $output = [];
    $returnCode = 0;
    exec("php -l \"$file\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        $success[] = "Syntax OK: $file";
        echo "✅ $file - SYNTAX OK\n";
    } else {
        $errors[] = "Syntax error in $file: " . implode(' ', $output);
        echo "❌ $file - SYNTAX ERROR\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
    }
}

echo "\n";

// 2. Check for duplicate function declarations
echo "2. 🔍 CHECKING FOR DUPLICATE FUNCTIONS\n";
echo "--------------------------------------\n";

$helperContent = file_get_contents('backend/utils/helpers.php');
preg_match_all('/function\s+(\w+)\s*\(/', $helperContent, $matches);
$functions = $matches[1];
$functionCounts = array_count_values($functions);

$duplicates = array_filter($functionCounts, function($count) { return $count > 1; });

if (empty($duplicates)) {
    $success[] = "No duplicate functions found";
    echo "✅ No duplicate function declarations\n";
} else {
    foreach ($duplicates as $function => $count) {
        $errors[] = "Duplicate function: $function (declared $count times)";
        echo "❌ Duplicate function: $function (declared $count times)\n";
    }
}

echo "\n";

// 3. Check database configuration
echo "3. 🗄️ CHECKING DATABASE CONFIGURATION\n";
echo "------------------------------------\n";

try {
    require_once 'backend/config/database.php';
    
    // Check if constants are defined
    $requiredConstants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS'];
    foreach ($requiredConstants as $constant) {
        if (!defined($constant)) {
            $errors[] = "Missing database constant: $constant";
            echo "❌ Missing constant: $constant\n";
        } else {
            echo "✅ Constant defined: $constant\n";
        }
    }
    
    // Test database connection
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        $success[] = "Database connection successful";
        echo "✅ Database connection successful\n";
        
        // Test a simple query
        $stmt = $conn->query('SELECT 1 as test');
        if ($stmt) {
            echo "✅ Database query test successful\n";
        } else {
            $warnings[] = "Database query test failed";
            echo "⚠️ Database query test failed\n";
        }
    } else {
        $errors[] = "Database connection failed";
        echo "❌ Database connection failed\n";
    }
    
} catch (Exception $e) {
    $errors[] = "Database configuration error: " . $e->getMessage();
    echo "❌ Database configuration error: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. Check for missing dependencies
echo "4. 📦 CHECKING DEPENDENCIES\n";
echo "---------------------------\n";

$requiredClasses = ['Database', 'JWT', 'SecurityMiddleware'];
foreach ($requiredClasses as $class) {
    if (class_exists($class)) {
        echo "✅ Class available: $class\n";
    } else {
        $warnings[] = "Class not found: $class";
        echo "⚠️ Class not found: $class\n";
    }
}

$requiredFunctions = ['hashPassword', 'verifyPassword', 'authenticateUser', 'sendResponse'];
foreach ($requiredFunctions as $function) {
    if (function_exists($function)) {
        echo "✅ Function available: $function\n";
    } else {
        $errors[] = "Function not found: $function";
        echo "❌ Function not found: $function\n";
    }
}

echo "\n";

// 5. Check file permissions (if on Unix-like system)
echo "5. 🔐 CHECKING FILE PERMISSIONS\n";
echo "-------------------------------\n";

$writableDirectories = ['backend/cache', 'backend/logs', 'backend/uploads'];
foreach ($writableDirectories as $dir) {
    if (!is_dir($dir)) {
        $warnings[] = "Directory not found: $dir";
        echo "⚠️ Directory not found: $dir\n";
        continue;
    }
    
    if (is_writable($dir)) {
        echo "✅ Writable: $dir\n";
    } else {
        $warnings[] = "Directory not writable: $dir";
        echo "⚠️ Directory not writable: $dir\n";
    }
}

echo "\n";

// 6. Check security configuration
echo "6. 🛡️ CHECKING SECURITY CONFIGURATION\n";
echo "------------------------------------\n";

// Check if security classes are available
$securityFiles = [
    'backend/security/AdvancedSecurityManager.php',
    'backend/security/ThreatDetectionEngine.php',
    'backend/security/DataProtectionManager.php',
    'backend/security/SecurityMonitor.php'
];

foreach ($securityFiles as $file) {
    if (file_exists($file)) {
        echo "✅ Security file exists: " . basename($file) . "\n";
    } else {
        $errors[] = "Missing security file: $file";
        echo "❌ Missing security file: " . basename($file) . "\n";
    }
}

echo "\n";

// 7. Summary
echo "📊 VALIDATION SUMMARY\n";
echo "====================\n";

echo "✅ Successes: " . count($success) . "\n";
echo "⚠️ Warnings: " . count($warnings) . "\n";
echo "❌ Errors: " . count($errors) . "\n\n";

if (!empty($errors)) {
    echo "🚨 CRITICAL ERRORS:\n";
    foreach ($errors as $error) {
        echo "   ❌ $error\n";
    }
    echo "\n";
}

if (!empty($warnings)) {
    echo "⚠️ WARNINGS:\n";
    foreach ($warnings as $warning) {
        echo "   ⚠️ $warning\n";
    }
    echo "\n";
}

// Final verdict
if (empty($errors)) {
    if (empty($warnings)) {
        echo "🎉 DEPLOYMENT READY! All checks passed.\n";
        echo "🚀 Your backend is ready for production!\n";
    } else {
        echo "✅ DEPLOYMENT READY with warnings.\n";
        echo "🔧 Consider addressing the warnings above.\n";
    }
} else {
    echo "❌ DEPLOYMENT NOT READY!\n";
    echo "🛠️ Please fix the critical errors above before deploying.\n";
}

echo "\n🔗 Test your deployment at: http://chit.mobunite.com/api\n";

?>
