import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/payment_provider.dart';
import '../../../core/models/payment_model.dart';
import '../../../core/router/app_router.dart';
import '../widgets/payment_card.dart';
import '../widgets/payment_stats_card.dart';
import '../widgets/payment_filter_chips.dart';

class PaymentListScreen extends StatefulWidget {
  final String? chitId;

  const PaymentListScreen({
    super.key,
    this.chitId,
  });

  @override
  State<PaymentListScreen> createState() => _PaymentListScreenState();
}

class _PaymentListScreenState extends State<PaymentListScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  PaymentStatus? _selectedStatus;
  PaymentType? _selectedType;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPayments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPayments() async {
    final paymentProvider = Provider.of<PaymentProvider>(context, listen: false);
    await paymentProvider.refreshPayments();
  }

  Future<void> _handleRefresh() async {
    await _loadPayments();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: Text(widget.chitId != null ? 'Chit Payments' : 'All Payments'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.home),
          onPressed: () => AppNavigation.goToDashboard(context),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Pending'),
            Tab(text: 'Paid'),
            Tab(text: 'Overdue'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'export':
                  _exportPayments();
                  break;
                case 'send_reminders':
                  _sendReminders();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'send_reminders',
                child: Row(
                  children: [
                    Icon(Icons.notifications),
                    SizedBox(width: 8),
                    Text('Send Reminders'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Payment Statistics
          PaymentStatsCard(),
          
          // Active Filters
          if (_searchQuery.isNotEmpty || _selectedStatus != null || _selectedType != null)
            _buildActiveFilters(),
          
          // Payment List
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPaymentList(null), // All payments
                _buildPaymentList(PaymentStatus.pending),
                _buildPaymentList(PaymentStatus.paid),
                _buildPaymentList(PaymentStatus.overdue),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showMakePaymentDialog();
        },
        icon: const Icon(Icons.payment),
        label: const Text('Make Payment'),
      ),
    );
  }

  Widget _buildActiveFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Active Filters',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              if (_searchQuery.isNotEmpty)
                Chip(
                  label: Text('Search: $_searchQuery'),
                  onDeleted: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                ),
              if (_selectedStatus != null)
                Chip(
                  label: Text('Status: ${_selectedStatus!.displayName}'),
                  onDeleted: () {
                    setState(() {
                      _selectedStatus = null;
                    });
                  },
                ),
              if (_selectedType != null)
                Chip(
                  label: Text('Type: ${_selectedType!.displayName}'),
                  onDeleted: () {
                    setState(() {
                      _selectedType = null;
                    });
                  },
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentList(PaymentStatus? statusFilter) {
    return Consumer<PaymentProvider>(
      builder: (context, paymentProvider, child) {
        if (paymentProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        List<Payment> payments = paymentProvider.payments;

        // Filter by chit if specified
        if (widget.chitId != null) {
          payments = payments.where((payment) => payment.chitId == widget.chitId).toList();
        }

        // Apply status filter
        if (statusFilter != null) {
          payments = payments.where((payment) => payment.status == statusFilter).toList();
        }

        // Apply search filter
        if (_searchQuery.isNotEmpty) {
          payments = payments.where((payment) =>
              payment.memberName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              payment.paymentType.displayName.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
        }

        // Apply additional filters
        if (_selectedStatus != null) {
          payments = payments.where((payment) => payment.status == _selectedStatus).toList();
        }

        if (_selectedType != null) {
          payments = payments.where((payment) => payment.paymentType == _selectedType).toList();
        }

        if (payments.isEmpty) {
          return _buildEmptyState(statusFilter);
        }

        return RefreshIndicator(
          onRefresh: _handleRefresh,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: payments.length,
            itemBuilder: (context, index) {
              final payment = payments[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: PaymentCard(
                  payment: payment,
                  onTap: () {
                    _showPaymentDetails(payment);
                  },
                  onMarkPaid: payment.status == PaymentStatus.pending
                      ? () => _markAsPaid(payment)
                      : null,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(PaymentStatus? statusFilter) {
    String title;
    String subtitle;
    IconData icon;

    switch (statusFilter) {
      case PaymentStatus.pending:
        title = 'No Pending Payments';
        subtitle = 'All payments are up to date';
        icon = Icons.check_circle_outline;
        break;
      case PaymentStatus.paid:
        title = 'No Paid Payments';
        subtitle = 'No payments have been made yet';
        icon = Icons.payment_outlined;
        break;
      case PaymentStatus.overdue:
        title = 'No Overdue Payments';
        subtitle = 'Great! All payments are on time';
        icon = Icons.schedule_outlined;
        break;
      default:
        title = 'No Payments Found';
        subtitle = 'No payment records available';
        icon = Icons.receipt_outlined;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Payments'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Enter member name or payment type',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _searchQuery = '';
              });
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Payments'),
        content: PaymentFilterChips(
          selectedStatus: _selectedStatus,
          selectedType: _selectedType,
          onStatusChanged: (status) {
            setState(() {
              _selectedStatus = status;
            });
          },
          onTypeChanged: (type) {
            setState(() {
              _selectedType = type;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _selectedStatus = null;
                _selectedType = null;
              });
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _showMakePaymentDialog() {
    showDialog(
      context: context,
      builder: (context) => _MakePaymentDialog(),
    ).then((result) {
      if (result == true) {
        _loadPayments(); // Refresh data
      }
    });
  }

  void _showPaymentDetails(Payment payment) {
    // Show payment details in a dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Payment Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Member: ${payment.memberName}'),
            const SizedBox(height: 8),
            Text('Amount: ₹${(payment.amount ?? 0.0).toStringAsFixed(2)}'),
            const SizedBox(height: 8),
            Text('Type: ${payment.paymentType.name}'),
            const SizedBox(height: 8),
            Text('Status: ${payment.paymentStatus.name}'),
            const SizedBox(height: 8),
            Text('Method: ${payment.paymentMethod?.name ?? 'Not specified'}'),
            const SizedBox(height: 8),
            Text('Due Date: ${payment.dueDate.toString().split(' ')[0]}'),
            if (payment.paidDate != null) ...[
              const SizedBox(height: 8),
              Text('Paid Date: ${payment.paidDate!.toString().split(' ')[0]}'),
            ],
            if (payment.transactionReference != null) ...[
              const SizedBox(height: 8),
              Text('Transaction Ref: ${payment.transactionReference}'),
            ],
            if (payment.notes != null && payment.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text('Notes: ${payment.notes}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _markAsPaid(Payment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Mark as Paid'),
        content: Text('Mark payment of ₹${payment.formattedAmount} from ${payment.memberName} as paid?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performMarkAsPaid(payment);
            },
            child: const Text('Mark Paid'),
          ),
        ],
      ),
    );
  }

  void _exportPayments() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Payments'),
        content: const Text('Choose export format:'),
        actions: [
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performExport('csv');
            },
            child: const Text('CSV'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performExport('pdf');
            },
            child: const Text('PDF'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _performExport(String format) async {
    try {
      // Show loading
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Exporting payments as ${format.toUpperCase()}...'),
          backgroundColor: Colors.blue,
        ),
      );

      // Get current payments
      final paymentProvider = Provider.of<PaymentProvider>(context, listen: false);
      final payments = paymentProvider.payments;

      if (payments.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No payments to export'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Simulate export process
      await Future.delayed(const Duration(seconds: 2));

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${payments.length} payments exported as ${format.toUpperCase()} successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _sendReminders() {
    // Send payment reminders functionality
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Reminders'),
        content: const Text('Send payment reminders to all members with pending payments?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Payment reminders sent successfully!')),
              );
            },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }

  Future<void> _performMarkAsPaid(Payment payment) async {
    final paymentProvider = Provider.of<PaymentProvider>(context, listen: false);

    final result = await paymentProvider.markPaymentAsPaid(
      paymentId: payment.id,
      paidDate: DateTime.now(),
      paymentMethod: PaymentMethod.cash, // Default method, could be made configurable
    );

    if (result && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Payment marked as paid successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      _loadPayments(); // Refresh data
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to mark payment as paid'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

// Make Payment Dialog
class _MakePaymentDialog extends StatefulWidget {
  @override
  State<_MakePaymentDialog> createState() => _MakePaymentDialogState();
}

class _MakePaymentDialogState extends State<_MakePaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  PaymentMethod _selectedMethod = PaymentMethod.cash;
  PaymentType _selectedType = PaymentType.contribution;
  bool _isLoading = false;

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Record Payment'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
                // Chit Selection
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Select Chit',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: '1', child: Text('Sample Chit 1')),
                    DropdownMenuItem(value: '2', child: Text('Sample Chit 2')),
                  ],
                  onChanged: (value) {
                    // Handle chit selection
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select a chit';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(
                    labelText: 'Amount',
                    border: OutlineInputBorder(),
                    prefixText: '₹ ',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter amount';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<PaymentMethod>(
                  value: _selectedMethod,
                  decoration: const InputDecoration(
                    labelText: 'Payment Method',
                    border: OutlineInputBorder(),
                  ),
                  items: PaymentMethod.values.map((method) {
                    return DropdownMenuItem(
                      value: method,
                      child: Text(method.name.toUpperCase()),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedMethod = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<PaymentType>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'Payment Type',
                    border: OutlineInputBorder(),
                  ),
                  items: PaymentType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type.name.toUpperCase()),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _recordPayment,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Record Payment'),
        ),
      ],
    );
  }

  Future<void> _recordPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final paymentProvider = Provider.of<PaymentProvider>(context, listen: false);

      // For demo purposes, using dummy IDs
      // In a real app, you'd select the chit and member
      final result = await paymentProvider.recordPayment(
        chitId: 'demo-chit-id',
        memberId: 'demo-member-id',
        amount: double.parse(_amountController.text),
        method: _selectedMethod,
        type: _selectedType,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      if (result.isSuccess && mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Payment recorded successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.error ?? 'Failed to record payment'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recording payment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
