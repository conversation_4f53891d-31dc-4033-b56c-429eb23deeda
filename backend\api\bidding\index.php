<?php
/**
 * Bidding API Routes - LiteSpeed Optimized
 */

require_once __DIR__ . '/../../controllers/BiddingController.php';

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];

// Remove /api/bidding prefix and get the endpoint
$endpoint = str_replace('/api/bidding', '', parse_url($path, PHP_URL_PATH));
$endpoint = trim($endpoint, '/');

// Split path into segments
$segments = explode('/', $endpoint);
$roundId = $segments[0] ?? null;
$action = $segments[1] ?? null;

// Initialize controller
$biddingController = new BiddingController();

// LiteSpeed cache control for bidding endpoints
if ($method === 'GET') {
    header('X-LiteSpeed-Cache-Control: public, max-age=60'); // 1 minute for real-time data
    header('X-LiteSpeed-Tag: bidding');
} else {
    header('X-LiteSpeed-Cache-Control: no-cache');
}

try {
    switch ($method) {
        case 'GET':
            if (empty($roundId)) {
                // GET /api/bidding - List all bidding rounds
                $biddingController->getAllRounds();
            } elseif (empty($action)) {
                // GET /api/bidding/{id} - Get specific bidding round
                $biddingController->getRound($roundId);
            } elseif ($action === 'bids') {
                // GET /api/bidding/{id}/bids - Get all bids for round
                $biddingController->getRoundBids($roundId);
            } elseif ($action === 'status') {
                // GET /api/bidding/{id}/status - Get round status
                $biddingController->getRoundStatus($roundId);
            } elseif ($action === 'winner') {
                // GET /api/bidding/{id}/winner - Get round winner
                $biddingController->getRoundWinner($roundId);
            } elseif ($action === 'live') {
                // GET /api/bidding/{id}/live - Get live bidding data
                $biddingController->getLiveBiddingData($roundId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'POST':
            if (empty($roundId)) {
                // POST /api/bidding - Start new bidding round
                $biddingController->startRound();
            } elseif ($action === 'bid') {
                // POST /api/bidding/{id}/bid - Place a bid
                $biddingController->placeBid($roundId);
            } elseif ($action === 'end') {
                // POST /api/bidding/{id}/end - End bidding round
                $biddingController->endRound($roundId);
            } elseif ($action === 'cancel') {
                // POST /api/bidding/{id}/cancel - Cancel bidding round
                $biddingController->cancelRound($roundId);
            } elseif ($action === 'extend') {
                // POST /api/bidding/{id}/extend - Extend bidding time
                $biddingController->extendRound($roundId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'PUT':
            if (!empty($roundId) && empty($action)) {
                // PUT /api/bidding/{id} - Update bidding round
                $biddingController->updateRound($roundId);
            } elseif (!empty($roundId) && $action === 'bid' && !empty($segments[2])) {
                // PUT /api/bidding/{id}/bid/{bidId} - Update bid
                $biddingController->updateBid($roundId, $segments[2]);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'DELETE':
            if (!empty($roundId) && $action === 'bid' && !empty($segments[2])) {
                // DELETE /api/bidding/{id}/bid/{bidId} - Cancel bid
                $biddingController->cancelBid($roundId, $segments[2]);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        default:
            sendError('Method not allowed', 405);
            break;
    }
} catch (Exception $e) {
    error_log("Bidding API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

?>
