<?php
/**
 * FileUpload Model
 */

require_once __DIR__ . '/../config/database.php';

class FileUpload {
    public $id;
    public $user_id;
    public $chit_id;
    public $original_name;
    public $file_name;
    public $file_path;
    public $file_size;
    public $mime_type;
    public $file_type;
    public $created_at;
    
    private $db;
    private $conn;
    
    public function __construct() {
        $this->db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * Save file upload record
     */
    public function save() {
        try {
            if ($this->id) {
                return $this->update();
            } else {
                return $this->create();
            }
        } catch (Exception $e) {
            error_log("FileUpload save error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create new file upload record
     */
    private function create() {
        $sql = "INSERT INTO file_uploads (user_id, chit_id, original_name, file_name, file_path, file_size, mime_type, file_type) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($sql);
        $result = $stmt->execute([
            $this->user_id,
            $this->chit_id,
            $this->original_name,
            $this->file_name,
            $this->file_path,
            $this->file_size,
            $this->mime_type,
            $this->file_type
        ]);
        
        if ($result) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        
        return false;
    }
    
    /**
     * Update file upload record
     */
    private function update() {
        $sql = "UPDATE file_uploads SET 
                user_id = ?, chit_id = ?, original_name = ?, file_name = ?, 
                file_path = ?, file_size = ?, mime_type = ?, file_type = ?
                WHERE id = ?";
        
        $stmt = $this->conn->prepare($sql);
        return $stmt->execute([
            $this->user_id,
            $this->chit_id,
            $this->original_name,
            $this->file_name,
            $this->file_path,
            $this->file_size,
            $this->mime_type,
            $this->file_type,
            $this->id
        ]);
    }
    
    /**
     * Find file upload by ID
     */
    public function findById($id) {
        $sql = "SELECT * FROM file_uploads WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$id]);
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($row) {
            $this->mapFromArray($row);
            return true;
        }
        
        return false;
    }
    
    /**
     * Get files for a user
     */
    public function getUserFiles($userId, $fileType = null) {
        $sql = "SELECT * FROM file_uploads WHERE user_id = ?";
        $params = [$userId];
        
        if ($fileType) {
            $sql .= " AND file_type = ?";
            $params[] = $fileType;
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get files for a chit
     */
    public function getChitFiles($chitId, $fileType = null) {
        $sql = "SELECT * FROM file_uploads WHERE chit_id = ?";
        $params = [$chitId];
        
        if ($fileType) {
            $sql .= " AND file_type = ?";
            $params[] = $fileType;
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Delete file upload record
     */
    public function delete() {
        if (!$this->id) {
            return false;
        }
        
        $sql = "DELETE FROM file_uploads WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        return $stmt->execute([$this->id]);
    }
    
    /**
     * Get file statistics
     */
    public function getFileStats($userId = null) {
        $sql = "SELECT 
                    file_type,
                    COUNT(*) as count,
                    SUM(file_size) as total_size,
                    AVG(file_size) as avg_size
                FROM file_uploads";
        
        $params = [];
        
        if ($userId) {
            $sql .= " WHERE user_id = ?";
            $params[] = $userId;
        }
        
        $sql .= " GROUP BY file_type";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Clean up old files
     */
    public function cleanupOldFiles($days = 30) {
        $sql = "SELECT * FROM file_uploads WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$days]);
        
        $oldFiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $deletedCount = 0;
        
        foreach ($oldFiles as $file) {
            // Delete physical file
            $fullPath = UPLOAD_PATH . '../' . $file['file_path'];
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
            
            // Delete database record
            $deleteSql = "DELETE FROM file_uploads WHERE id = ?";
            $deleteStmt = $this->conn->prepare($deleteSql);
            if ($deleteStmt->execute([$file['id']])) {
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
    
    /**
     * Map array data to object properties
     */
    private function mapFromArray($data) {
        $this->id = $data['id'] ?? null;
        $this->user_id = $data['user_id'] ?? null;
        $this->chit_id = $data['chit_id'] ?? null;
        $this->original_name = $data['original_name'] ?? null;
        $this->file_name = $data['file_name'] ?? null;
        $this->file_path = $data['file_path'] ?? null;
        $this->file_size = $data['file_size'] ?? null;
        $this->mime_type = $data['mime_type'] ?? null;
        $this->file_type = $data['file_type'] ?? null;
        $this->created_at = $data['created_at'] ?? null;
    }
    
    /**
     * Convert to array
     */
    public function toArray() {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'chit_id' => $this->chit_id,
            'original_name' => $this->original_name,
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'file_size' => $this->file_size,
            'mime_type' => $this->mime_type,
            'file_type' => $this->file_type,
            'created_at' => $this->created_at
        ];
    }
}
