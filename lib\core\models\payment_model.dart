import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'payment_model.g.dart';

@JsonSerializable()
class Payment {
  final String id;
  final String chitId;
  final String biddingRoundId;
  final String memberId;
  final String memberName;
  final double? amount;
  final PaymentType paymentType;
  final PaymentMethod? paymentMethod;
  final PaymentStatus paymentStatus;
  final DateTime dueDate;
  final DateTime? paidDate;
  final String? transactionReference;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Payment({
    required this.id,
    required this.chitId,
    required this.biddingRoundId,
    required this.memberId,
    required this.memberName,
    required this.amount,
    required this.paymentType,
    this.paymentMethod,
    this.paymentStatus = PaymentStatus.pending,
    required this.dueDate,
    this.paidDate,
    this.transactionReference,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Payment.fromJson(Map<String, dynamic> json) => _$PaymentFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentToJson(this);

  Payment copyWith({
    String? id,
    String? chitId,
    String? biddingRoundId,
    String? memberId,
    String? memberName,
    double? amount,
    PaymentType? paymentType,
    PaymentMethod? paymentMethod,
    PaymentStatus? paymentStatus,
    DateTime? dueDate,
    DateTime? paidDate,
    String? transactionReference,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Payment(
      id: id ?? this.id,
      chitId: chitId ?? this.chitId,
      biddingRoundId: biddingRoundId ?? this.biddingRoundId,
      memberId: memberId ?? this.memberId,
      memberName: memberName ?? this.memberName,
      amount: amount ?? this.amount,
      paymentType: paymentType ?? this.paymentType,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      dueDate: dueDate ?? this.dueDate,
      paidDate: paidDate ?? this.paidDate,
      transactionReference: transactionReference ?? this.transactionReference,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Mark payment as paid
  Payment markAsPaid({
    DateTime? paidDate,
    String? transactionReference,
    PaymentMethod? paymentMethod,
  }) {
    return copyWith(
      paymentStatus: PaymentStatus.paid,
      paidDate: paidDate ?? DateTime.now(),
      transactionReference: transactionReference ?? this.transactionReference,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      updatedAt: DateTime.now(),
    );
  }

  // Mark payment as overdue
  Payment markAsOverdue() {
    return copyWith(
      paymentStatus: PaymentStatus.overdue,
      updatedAt: DateTime.now(),
    );
  }

  // Check if payment is overdue
  bool get isOverdue {
    return paymentStatus == PaymentStatus.pending && 
           dueDate.isBefore(DateTime.now());
  }

  // Check if payment is paid
  bool get isPaid => paymentStatus == PaymentStatus.paid;

  // Check if payment is pending
  bool get isPending => paymentStatus == PaymentStatus.pending;

  // Convenience getter for status (to match widget expectations)
  PaymentStatus get status => paymentStatus;

  // Get days until due (negative if overdue)
  int get daysUntilDue {
    final now = DateTime.now();
    final difference = dueDate.difference(now);
    return difference.inDays;
  }

  // Get formatted amount
  String get formattedAmount {
    return '₹${(amount ?? 0.0).toStringAsFixed(2)}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Payment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Payment(id: $id, amount: $amount, type: $paymentType, status: $paymentStatus)';
  }
}

enum PaymentType {
  @JsonValue('contribution')
  contribution,
  @JsonValue('winning_amount')
  winningAmount,
  @JsonValue('commission')
  commission,
}

enum PaymentMethod {
  @JsonValue('cash')
  cash,
  @JsonValue('bank_transfer')
  bankTransfer,
  @JsonValue('upi')
  upi,
  @JsonValue('cheque')
  cheque,
  @JsonValue('other')
  other,
}

enum PaymentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('paid')
  paid,
  @JsonValue('overdue')
  overdue,
  @JsonValue('cancelled')
  cancelled,
}



// Extensions for better display
extension PaymentTypeExtension on PaymentType {
  String get displayName {
    switch (this) {
      case PaymentType.contribution:
        return 'Contribution';
      case PaymentType.winningAmount:
        return 'Winning Amount';
      case PaymentType.commission:
        return 'Commission';
    }
  }

  String get description {
    switch (this) {
      case PaymentType.contribution:
        return 'Monthly contribution to the chit fund';
      case PaymentType.winningAmount:
        return 'Amount received by the bidding winner';
      case PaymentType.commission:
        return 'Commission paid to the chit organizer';
    }
  }
}

extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.upi:
        return 'UPI';
      case PaymentMethod.cheque:
        return 'Cheque';
      case PaymentMethod.other:
        return 'Other';
    }
  }
}

extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.paid:
        return 'Paid';
      case PaymentStatus.overdue:
        return 'Overdue';
      case PaymentStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color get color {
    switch (this) {
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.overdue:
        return Colors.red;
      case PaymentStatus.cancelled:
        return Colors.grey;
    }
  }
}
