<?php
/**
 * Authentication API Routes - LiteSpeed Optimized with OTP
 */

require_once __DIR__ . '/../../controllers/AuthController.php';

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];

// Remove /api/auth prefix and get the endpoint
$endpoint = str_replace('/api/auth', '', parse_url($path, PHP_URL_PATH));
$endpoint = trim($endpoint, '/');

// Initialize controller
$authController = new AuthController();

// LiteSpeed cache control for auth endpoints
header('X-LiteSpeed-Cache-Control: no-cache');
header('Cache-Control: no-cache, no-store, must-revalidate');

try {
    switch ($endpoint) {
        case 'login':
            if ($method === 'POST') {
                $authController->login();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'register':
            if ($method === 'POST') {
                $authController->register();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'send-otp':
            if ($method === 'POST') {
                $authController->sendOTP();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'verify-otp':
            if ($method === 'POST') {
                $authController->verifyOTP();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'logout':
            if ($method === 'POST') {
                $authController->logout();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'refresh':
            if ($method === 'POST') {
                $authController->refreshToken();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'reset-password':
            if ($method === 'POST') {
                $authController->resetPassword();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'verify':
            if ($method === 'GET') {
                $authController->verifyToken();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'profile':
            if ($method === 'GET') {
                $authController->getProfile();
            } elseif ($method === 'PUT') {
                $authController->updateProfile();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        case 'change-password':
            if ($method === 'POST') {
                $authController->changePassword();
            } else {
                sendError('Method not allowed', 405);
            }
            break;
            
        default:
            sendError('Endpoint not found', 404);
            break;
    }
} catch (Exception $e) {
    error_log("Auth API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

?>
