import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/providers/bidding_provider.dart';
import '../../../core/models/chit_model.dart';
import '../../../core/router/app_router.dart';
import '../widgets/bid_card.dart';
import '../widgets/bidding_timer.dart';
import '../widgets/current_bid_display.dart';

class BiddingScreen extends StatefulWidget {
  final String chitId;
  final String roundId;

  const BiddingScreen({
    super.key,
    required this.chitId,
    required this.roundId,
  });

  @override
  State<BiddingScreen> createState() => _BiddingScreenState();
}

class _BiddingScreenState extends State<BiddingScreen> {
  final _bidController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  
  Chit? _chit;
  BiddingRound? _currentRound;
  bool _isLoading = true;
  bool _isSubmittingBid = false;
  double? _currentHighestBid;
  String? _currentWinner;

  @override
  void initState() {
    super.initState();
    _loadBiddingData();
    _startRealTimeUpdates();
  }

  @override
  void dispose() {
    _bidController.dispose();
    super.dispose();
  }

  Future<void> _loadBiddingData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final chitProvider = Provider.of<ChitProvider>(context, listen: false);
      final biddingProvider = Provider.of<BiddingProvider>(context, listen: false);

      await chitProvider.getChitDetails(widget.chitId);
      final chitList = chitProvider.chits.where((chit) => chit.id == widget.chitId).toList();
      _chit = chitList.isNotEmpty ? chitList.first : null;

      // Load current bidding round
      await biddingProvider.loadCurrentBiddingRound(widget.chitId);
      _currentRound = biddingProvider.currentBiddingRound;

      // Update UI state based on loaded data
      if (_currentRound != null) {
        final bids = biddingProvider.currentBids;
        if (bids.isNotEmpty) {
          _currentHighestBid = bids.map((bid) => bid.amount).reduce((a, b) => a > b ? a : b);
          _currentWinner = bids.firstWhere((bid) => bid.amount == _currentHighestBid).memberName;
        }
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading bidding data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }



  void _startRealTimeUpdates() {
    // Implement periodic updates for real-time bidding data
    // In a production app, you would use WebSocket or Server-Sent Events
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && _currentRound?.status == BiddingStatus.inProgress) {
        _loadBiddingData();
        _startRealTimeUpdates();
      }
    });
  }

  Future<void> _submitBid() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmittingBid = true;
    });

    try {
      if (_currentRound == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No active bidding round found'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final biddingProvider = Provider.of<BiddingProvider>(context, listen: false);
      final bidAmount = double.parse(_bidController.text);

      final result = await biddingProvider.placeBid(
        biddingRoundId: _currentRound!.id,
        bidAmount: bidAmount,
      );

      if (result.isSuccess && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bid submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        _bidController.clear();
        await _loadBiddingData();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.error ?? 'Failed to submit bid'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit bid: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmittingBid = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Bidding'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_chit == null || _currentRound == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Bidding'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text('Bidding round not found'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: Text('${_chit!.name} - Round ${_currentRound!.roundNumber}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBiddingData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'rules':
                  _showBiddingRules();
                  break;
                case 'history':
                  _showBiddingHistory();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'rules',
                child: Row(
                  children: [
                    Icon(Icons.rule),
                    SizedBox(width: 8),
                    Text('Bidding Rules'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'history',
                child: Row(
                  children: [
                    Icon(Icons.history),
                    SizedBox(width: 8),
                    Text('Bidding History'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Bidding Timer
          if (_currentRound!.endTime != null)
            BiddingTimer(
              endTime: _currentRound!.endTime!,
              onTimeUp: () {
                _showTimeUpDialog();
              },
            ),
          
          // Current Bid Display
          CurrentBidDisplay(
            currentBid: _currentHighestBid,
            currentWinner: _currentWinner,
            totalAmount: _chit!.totalAmount,
          ),
          
          // Bid Input Section
          _buildBidInputSection(),
          
          // Bids List
          Expanded(
            child: _buildBidsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBidInputSection() {
    final isActive = _currentRound!.status == BiddingStatus.inProgress;
    final minBid = _currentHighestBid != null 
        ? _currentHighestBid! + 100 
        : _chit!.totalAmount * 0.1; // Minimum 10% of total amount

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Place Your Bid',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _bidController,
                    enabled: isActive && !_isSubmittingBid,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    decoration: InputDecoration(
                      labelText: 'Bid Amount',
                      hintText: 'Minimum: ₹${minBid.toStringAsFixed(0)}',
                      prefixText: '₹',
                      border: const OutlineInputBorder(),
                      filled: true,
                      fillColor: Theme.of(context).colorScheme.surface,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter bid amount';
                      }
                      
                      final amount = double.tryParse(value);
                      if (amount == null) {
                        return 'Please enter a valid amount';
                      }
                      
                      if (amount < minBid) {
                        return 'Minimum bid: ₹${minBid.toStringAsFixed(0)}';
                      }
                      
                      if (amount > _chit!.totalAmount) {
                        return 'Bid cannot exceed total amount';
                      }
                      
                      return null;
                    },
                  ),
                ),
                
                const SizedBox(width: 12),
                
                ElevatedButton(
                  onPressed: isActive && !_isSubmittingBid ? _submitBid : null,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  ),
                  child: _isSubmittingBid
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Submit Bid'),
                ),
              ],
            ),
            
            if (!isActive) ...[
              const SizedBox(height: 8),
              Text(
                _currentRound!.status == BiddingStatus.completed
                    ? 'Bidding has ended'
                    : 'Bidding has not started yet',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.orange,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBidsList() {
    final bids = _currentRound!.bids.toList()
      ..sort((a, b) => b.amount.compareTo(a.amount)); // Sort by amount descending

    if (bids.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.gavel,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No bids yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Be the first to place a bid!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bids.length,
      itemBuilder: (context, index) {
        final bid = bids[index];
        final isWinning = index == 0;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: BidCard(
            bid: bid,
            isWinning: isWinning,
            rank: index + 1,
          ),
        );
      },
    );
  }

  void _showBiddingRules() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bidding Rules'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('1. Bidding starts at 10% of the total chit amount'),
              SizedBox(height: 8),
              Text('2. Each bid must be at least ₹100 higher than the previous bid'),
              SizedBox(height: 8),
              Text('3. Bidding ends when the timer reaches zero'),
              SizedBox(height: 8),
              Text('4. The highest bidder wins the round'),
              SizedBox(height: 8),
              Text('5. Winners cannot participate in future bidding rounds'),
              SizedBox(height: 8),
              Text('6. All bids are final and cannot be withdrawn'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showBiddingHistory() {
    Navigator.of(context).pushNamed(
      '/bidding/history',
      arguments: widget.chitId,
    );
  }

  void _showTimeUpDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Time\'s Up!'),
        content: Text(
          _currentWinner != null
              ? 'Congratulations to $_currentWinner for winning this round with a bid of ₹${_currentHighestBid?.toStringAsFixed(0)}!'
              : 'No bids were placed in this round.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              AppNavigation.goBack(context);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
