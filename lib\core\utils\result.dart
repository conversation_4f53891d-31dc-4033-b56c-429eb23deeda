/// Result class for handling success and error states
abstract class Result<T> {
  const Result();
  
  bool get isSuccess => this is Success<T>;
  bool get isError => this is Error<T>;
  
  T? get data => isSuccess ? (this as Success<T>).data : null;
  String get error => isError ? (this as Error<T>).message : '';
}

/// Success result containing data
class Success<T> extends Result<T> {
  @override
  final T data;
  
  const Success(this.data);
  
  @override
  String toString() => 'Success(data: $data)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Success<T> && other.data == data;
  }
  
  @override
  int get hashCode => data.hashCode;
}

/// Error result containing error message
class Error<T> extends Result<T> {
  final String message;
  
  const Error(this.message);
  
  @override
  String toString() => 'Error(message: $message)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Error<T> && other.message == message;
  }
  
  @override
  int get hashCode => message.hashCode;
}

/// Extension methods for Result
extension ResultExtensions<T> on Result<T> {
  /// Map the data if success, otherwise return the error
  Result<R> map<R>(R Function(T data) mapper) {
    if (isSuccess) {
      try {
        return Success(mapper(data as T));
      } catch (e) {
        return Error(e.toString());
      }
    } else {
      return Error<R>(this.error);
    }
  }
  
  /// FlatMap for chaining results
  Result<R> flatMap<R>(Result<R> Function(T data) mapper) {
    if (isSuccess) {
      try {
        return mapper(data as T);
      } catch (e) {
        return Error(e.toString());
      }
    } else {
      return Error<R>(this.error);
    }
  }
  
  /// Execute function if success
  Result<T> onSuccess(void Function(T data) action) {
    if (isSuccess) {
      action(data as T);
    }
    return this;
  }
  
  /// Execute function if error
  Result<T> onError(void Function(String error) action) {
    if (isError) {
      action(this.error);
    }
    return this;
  }
  
  /// Get data or return default value
  T getOrElse(T defaultValue) {
    return isSuccess ? data as T : defaultValue;
  }
  
  /// Get data or throw exception
  T getOrThrow() {
    if (isSuccess) {
      return data as T;
    } else {
      throw Exception(error);
    }
  }
}

/// Helper functions for creating results
Result<T> success<T>(T data) => Success(data);
Result<T> error<T>(String message) => Error(message);

/// Try-catch wrapper that returns Result
Result<T> tryResult<T>(T Function() action) {
  try {
    return Success(action());
  } catch (e) {
    return Error(e.toString());
  }
}

/// Async try-catch wrapper that returns Result
Future<Result<T>> tryResultAsync<T>(Future<T> Function() action) async {
  try {
    final result = await action();
    return Success(result);
  } catch (e) {
    return Error(e.toString());
  }
}
