<?php
/**
 * Payments API Routes - LiteSpeed Optimized
 */

require_once __DIR__ . '/../../controllers/PaymentController.php';

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];

// Remove /api/payments prefix and get the endpoint
$endpoint = str_replace('/api/payments', '', parse_url($path, PHP_URL_PATH));
$endpoint = trim($endpoint, '/');

// Split path into segments
$segments = explode('/', $endpoint);
$paymentId = $segments[0] ?? null;
$action = $segments[1] ?? null;

// Initialize controller
$paymentController = new PaymentController();

// LiteSpeed cache control for payments endpoints
if ($method === 'GET') {
    header('X-LiteSpeed-Cache-Control: public, max-age=300'); // 5 minutes
    header('X-LiteSpeed-Tag: payments');
} else {
    header('X-LiteSpeed-Cache-Control: no-cache');
}

try {
    switch ($method) {
        case 'GET':
            if (empty($paymentId)) {
                // GET /api/payments - List all payments
                $paymentController->getAllPayments();
            } elseif (empty($action)) {
                // GET /api/payments/{id} - Get specific payment
                $paymentController->getPayment($paymentId);
            } elseif ($action === 'receipt') {
                // GET /api/payments/{id}/receipt - Get payment receipt
                $paymentController->getPaymentReceipt($paymentId);
            } elseif ($action === 'history') {
                // GET /api/payments/{id}/history - Get payment history
                $paymentController->getPaymentHistory($paymentId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'POST':
            if (empty($paymentId)) {
                // POST /api/payments - Record new payment
                $paymentController->recordPayment();
            } elseif ($action === 'verify') {
                // POST /api/payments/{id}/verify - Verify payment
                $paymentController->verifyPayment($paymentId);
            } elseif ($action === 'approve') {
                // POST /api/payments/{id}/approve - Approve payment
                $paymentController->approvePayment($paymentId);
            } elseif ($action === 'reject') {
                // POST /api/payments/{id}/reject - Reject payment
                $paymentController->rejectPayment($paymentId);
            } elseif ($action === 'refund') {
                // POST /api/payments/{id}/refund - Process refund
                $paymentController->processRefund($paymentId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'PUT':
            if (!empty($paymentId) && empty($action)) {
                // PUT /api/payments/{id} - Update payment
                $paymentController->updatePayment($paymentId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'DELETE':
            if (!empty($paymentId) && empty($action)) {
                // DELETE /api/payments/{id} - Cancel payment
                $paymentController->cancelPayment($paymentId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        default:
            sendError('Method not allowed', 405);
            break;
    }
} catch (Exception $e) {
    error_log("Payments API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

?>
