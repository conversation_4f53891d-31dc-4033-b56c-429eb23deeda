import 'package:flutter/material.dart';
import '../../../core/router/app_router.dart';
import 'dashboard_card.dart';

class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        LayoutBuilder(
          builder: (context, constraints) {
            final screenWidth = constraints.maxWidth;
            final crossAxisCount = screenWidth > 600 ? 4 : 4; // Keep 4 for now
            final cardWidth = (screenWidth - (crossAxisCount - 1) * 12) / crossAxisCount;
            final cardHeight = cardWidth * 1.5; // Much taller to prevent overflow

            return GridView.count(
              crossAxisCount: crossAxisCount,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: cardWidth / cardHeight,
          children: [
            QuickActionCard(
              title: 'Create Chit',
              icon: Icons.add_circle_outline,
              color: Colors.blue,
              onTap: () {
                AppNavigation.goToCreateChit(context);
              },
            ),
            
            QuickActionCard(
              title: 'Join Chit',
              icon: Icons.group_add,
              color: Colors.green,
              onTap: () {
                AppNavigation.goToJoinChit(context);
              },
            ),
            
            QuickActionCard(
              title: 'Make Payment',
              icon: Icons.payment,
              color: Colors.orange,
              onTap: () {
                AppNavigation.goToMakePayment(context);
              },
            ),
            
            QuickActionCard(
              title: 'View Reports',
              icon: Icons.analytics,
              color: Colors.purple,
              onTap: () {
                AppNavigation.goToReports(context);
              },
            ),
            ],
          );
          },
        ),
      ],
    );
  }
}

// Alternative horizontal layout for quick actions
class QuickActionsHorizontal extends StatelessWidget {
  const QuickActionsHorizontal({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        SizedBox(
          height: 100,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildHorizontalAction(
                context,
                'Create Chit',
                Icons.add_circle_outline,
                Colors.blue,
                () => AppNavigation.goToCreateChit(context),
              ),
              
              const SizedBox(width: 12),
              
              _buildHorizontalAction(
                context,
                'Join Chit',
                Icons.group_add,
                Colors.green,
                () => AppNavigation.goToJoinChit(context),
              ),
              
              const SizedBox(width: 12),
              
              _buildHorizontalAction(
                context,
                'Make Payment',
                Icons.payment,
                Colors.orange,
                () => AppNavigation.goToMakePayment(context),
              ),
              
              const SizedBox(width: 12),
              
              _buildHorizontalAction(
                context,
                'View Reports',
                Icons.analytics,
                Colors.purple,
                () => AppNavigation.goToReports(context),
              ),
              
              const SizedBox(width: 12),
              
              _buildHorizontalAction(
                context,
                'Notifications',
                Icons.notifications,
                Colors.red,
                () => AppNavigation.goToNotifications(context),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHorizontalAction(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return SizedBox(
      width: 80,
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Expandable quick actions with more options
class ExpandableQuickActions extends StatefulWidget {
  const ExpandableQuickActions({super.key});

  @override
  State<ExpandableQuickActions> createState() => _ExpandableQuickActionsState();
}

class _ExpandableQuickActionsState extends State<ExpandableQuickActions> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              icon: Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                size: 20,
              ),
              label: Text(_isExpanded ? 'Less' : 'More'),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        GridView.count(
          crossAxisCount: 4,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 0.9,
          children: [
            // Primary actions (always visible)
            QuickActionCard(
              title: 'Create Chit',
              icon: Icons.add_circle_outline,
              color: Colors.blue,
              onTap: () => AppNavigation.goToCreateChit(context),
            ),
            
            QuickActionCard(
              title: 'Join Chit',
              icon: Icons.group_add,
              color: Colors.green,
              onTap: () => AppNavigation.goToJoinChit(context),
            ),
            
            QuickActionCard(
              title: 'Make Payment',
              icon: Icons.payment,
              color: Colors.orange,
              onTap: () => AppNavigation.goToMakePayment(context),
            ),
            
            QuickActionCard(
              title: 'View Reports',
              icon: Icons.analytics,
              color: Colors.purple,
              onTap: () => AppNavigation.goToReports(context),
            ),
            
            // Additional actions (shown when expanded)
            if (_isExpanded) ...[
              QuickActionCard(
                title: 'Notifications',
                icon: Icons.notifications,
                color: Colors.red,
                onTap: () => AppNavigation.goToNotifications(context),
              ),
              
              QuickActionCard(
                title: 'Profile',
                icon: Icons.person,
                color: Colors.indigo,
                onTap: () => AppNavigation.goToProfile(context),
              ),
              
              QuickActionCard(
                title: 'Settings',
                icon: Icons.settings,
                color: Colors.grey,
                onTap: () => AppNavigation.goToSettings(context),
              ),
              
              QuickActionCard(
                title: 'Help',
                icon: Icons.help_outline,
                color: Colors.teal,
                onTap: () {
                  // Show help dialog or navigate to help screen
                  _showHelpDialog(context);
                },
              ),
            ],
          ],
        ),
      ],
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const Text(
          'For assistance with the Chit Fund Manager app, please contact our support team or visit our help center.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to help screen or open support
            },
            child: const Text('Contact Support'),
          ),
        ],
      ),
    );
  }
}
