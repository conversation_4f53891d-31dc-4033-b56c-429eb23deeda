import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/chit_model.dart';
import 'api_service.dart';
import 'storage_service.dart';

class BiddingService {
  static late Dio _dio;

  static void initialize() {
    ApiService.initialize();
    _dio = Dio(BaseOptions(
      baseUrl: ApiService.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token to requests
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
      ),
    );
  }

  /// Start a new bidding round
  static Future<Result<BiddingRound>> startBiddingRound({
    required String chitId,
    required double startingBid,
    required DateTime meetingDate,
  }) async {
    try {
      final data = {
        'chit_id': chitId,
        'starting_bid': startingBid,
        'meeting_date': meetingDate.toIso8601String(),
      };

      final response = await _dio.post('/bidding/start', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final biddingRound = BiddingRound.fromJson(apiResponse.data!);
          return Success(biddingRound);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to start bidding round');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Place a bid in a bidding round
  static Future<Result<Bid>> placeBid({
    required String biddingRoundId,
    required double bidAmount,
  }) async {
    try {
      final data = {
        'bidding_round_id': biddingRoundId,
        'bid_amount': bidAmount,
      };

      final response = await _dio.post('/bidding/place-bid', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final bid = Bid.fromJson(apiResponse.data!);
          return Success(bid);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to place bid');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// End a bidding round
  static Future<Result<BiddingRound>> endBiddingRound({
    required String biddingRoundId,
  }) async {
    try {
      final data = {
        'bidding_round_id': biddingRoundId,
      };

      final response = await _dio.post('/bidding/end', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final biddingRound = BiddingRound.fromJson(apiResponse.data!);
          return Success(biddingRound);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to end bidding round');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get current bidding round for a chit
  static Future<Result<BiddingRound?>> getCurrentBiddingRound(String chitId) async {
    try {
      final response = await _dio.get('/bidding/current/$chitId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>?>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>?,
        );

        if (apiResponse.isSuccess) {
          if (apiResponse.data != null) {
            final biddingRound = BiddingRound.fromJson(apiResponse.data!);
            return Success(biddingRound);
          } else {
            return const Success(null);
          }
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get current bidding round');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get bidding history for a chit
  static Future<Result<List<BiddingRound>>> getBiddingHistory(String chitId) async {
    try {
      final response = await _dio.get('/bidding/history/$chitId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final biddingRounds = apiResponse.data!
              .map((roundJson) => BiddingRound.fromJson(roundJson as Map<String, dynamic>))
              .toList();
          return Success(biddingRounds);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get bidding history');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get bids for a specific bidding round
  static Future<Result<List<Bid>>> getBidsForRound(String biddingRoundId) async {
    try {
      final response = await _dio.get('/bidding/bids/$biddingRoundId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final bids = apiResponse.data!
              .map((bidJson) => Bid.fromJson(bidJson as Map<String, dynamic>))
              .toList();
          return Success(bids);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get bids for round');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle Dio errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Response timeout. Please try again.';
      case DioExceptionType.badResponse:
        if (error.response?.data is Map<String, dynamic>) {
          final data = error.response?.data as Map<String, dynamic>;
          return data['message'] ?? 'Server error occurred';
        } else {
          return 'Server error: ${error.response?.statusCode}';
        }
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      default:
        return 'Network error: ${error.message}';
    }
  }
}
