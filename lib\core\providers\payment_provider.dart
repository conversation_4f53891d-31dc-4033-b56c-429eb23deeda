import 'package:flutter/foundation.dart';
import '../models/payment_model.dart';
import '../models/api_response.dart';
import '../services/payment_service.dart';

class PaymentProvider extends ChangeNotifier {
  final List<Payment> _payments = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Payment> get payments => _payments;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Filtered payments
  List<Payment> get pendingPayments => 
      _payments.where((payment) => payment.paymentStatus == PaymentStatus.pending).toList();
  
  List<Payment> get paidPayments => 
      _payments.where((payment) => payment.paymentStatus == PaymentStatus.paid).toList();
  
  List<Payment> get overduePayments => 
      _payments.where((payment) => payment.paymentStatus == PaymentStatus.overdue).toList();

  List<Payment> get contributionPayments => 
      _payments.where((payment) => payment.paymentType == PaymentType.contribution).toList();
  
  List<Payment> get winningPayments => 
      _payments.where((payment) => payment.paymentType == PaymentType.winningAmount).toList();
  
  List<Payment> get commissionPayments => 
      _payments.where((payment) => payment.paymentType == PaymentType.commission).toList();

  // Initialize payments
  Future<void> initializePayments() async {
    _setLoading(true);

    try {
      final result = await PaymentService.getUserPayments();

      if (result.isSuccess) {
        _payments.clear();
        _payments.addAll(result.data!);
        _clearError();
      } else {
        _setError(result.error ?? 'Failed to load payments');
      }
    } catch (e) {
      _setError('Failed to initialize payments: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Record a new payment
  Future<Result<Payment>> recordPayment({
    required String chitId,
    required String memberId,
    required double amount,
    required PaymentMethod method,
    required PaymentType type,
    String? transactionId,
    String? notes,
  }) async {
    try {
      _setLoading(true);

      final result = await PaymentService.recordPayment(
        chitId: chitId,
        memberId: memberId,
        amount: amount,
        method: method,
        type: type,
        transactionId: transactionId,
        notes: notes,
      );

      if (result.isSuccess) {
        _payments.add(result.data!);
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to record payment');
        return result;
      }
    } catch (e) {
      final error = 'Failed to record payment: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Make a payment (simplified version for user payments)
  Future<bool> makePayment({
    required String chitId,
    required double amount,
    String? remarks,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await PaymentService.makePayment(
        chitId: chitId,
        amount: amount,
        remarks: remarks,
      );

      if (result.isSuccess) {
        // Refresh payments to get the updated list
        await refreshPayments();
        return true;
      } else {
        _setError(result.error ?? 'Failed to make payment');
        return false;
      }
    } catch (e) {
      _setError('Failed to make payment: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update payment
  Future<Result<Payment>> updatePayment({
    required String paymentId,
    PaymentStatus? status,
    String? transactionId,
    String? notes,
  }) async {
    try {
      _setLoading(true);

      final result = await PaymentService.updatePayment(
        paymentId: paymentId,
        status: status,
        transactionId: transactionId,
        notes: notes,
      );

      if (result.isSuccess) {
        final index = _payments.indexWhere((payment) => payment.id == paymentId);
        if (index != -1) {
          _payments[index] = result.data!;
        }

        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to update payment');
        return result;
      }
    } catch (e) {
      final error = 'Failed to update payment: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Mark payment as paid
  Future<bool> markPaymentAsPaid({
    required String paymentId,
    DateTime? paidDate,
    String? transactionReference,
    PaymentMethod? paymentMethod,
  }) async {
    try {
      _setLoading(true);
      
      final index = _payments.indexWhere((payment) => payment.id == paymentId);
      if (index != -1) {
        _payments[index] = _payments[index].markAsPaid(
          paidDate: paidDate,
          transactionReference: transactionReference,
          paymentMethod: paymentMethod,
        );
        
        // Update payment status via API
        await PaymentService.updatePayment(
          paymentId: paymentId,
          status: PaymentStatus.paid,
          transactionId: transactionReference,
        );
        
        _clearError();
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      _setError('Failed to mark payment as paid: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Mark payment as overdue
  Future<bool> markPaymentAsOverdue(String paymentId) async {
    try {
      _setLoading(true);
      
      final index = _payments.indexWhere((payment) => payment.id == paymentId);
      if (index != -1) {
        _payments[index] = _payments[index].markAsOverdue();
        
        // Update payment status via API
        await PaymentService.updatePayment(
          paymentId: paymentId,
          status: PaymentStatus.overdue,
        );
        
        _clearError();
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      _setError('Failed to mark payment as overdue: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get payments for a specific chit
  List<Payment> getPaymentsForChit(String chitId) {
    return _payments.where((payment) => payment.chitId == chitId).toList();
  }

  // Get payments for a specific member
  List<Payment> getPaymentsForMember(String memberId) {
    return _payments.where((payment) => payment.memberId == memberId).toList();
  }

  // Get payments for a specific bidding round
  List<Payment> getPaymentsForBiddingRound(String biddingRoundId) {
    return _payments.where((payment) => payment.biddingRoundId == biddingRoundId).toList();
  }

  // Get payments by type
  List<Payment> getPaymentsByType(PaymentType type) {
    return _payments.where((payment) => payment.paymentType == type).toList();
  }

  // Get payments by status
  List<Payment> getPaymentsByStatus(PaymentStatus status) {
    return _payments.where((payment) => payment.paymentStatus == status).toList();
  }

  // Get payments by method
  List<Payment> getPaymentsByMethod(PaymentMethod method) {
    return _payments.where((payment) => payment.paymentMethod == method).toList();
  }

  // Get overdue payments
  List<Payment> getOverduePayments() {
    final now = DateTime.now();
    return _payments.where((payment) {
      return payment.paymentStatus == PaymentStatus.pending && 
             payment.dueDate.isBefore(now);
    }).toList();
  }

  // Get upcoming payments (due within next 7 days)
  List<Payment> getUpcomingPayments() {
    final now = DateTime.now();
    final nextWeek = now.add(const Duration(days: 7));
    
    return _payments.where((payment) {
      return payment.paymentStatus == PaymentStatus.pending &&
             payment.dueDate.isAfter(now) &&
             payment.dueDate.isBefore(nextWeek);
    }).toList();
  }

  // Calculate total amount by type
  double getTotalAmountByType(PaymentType type) {
    return _payments
        .where((payment) => payment.paymentType == type)
        .fold(0.0, (sum, payment) => sum + (payment.amount ?? 0.0));
  }

  // Calculate total amount by status
  double getTotalAmountByStatus(PaymentStatus status) {
    return _payments
        .where((payment) => payment.paymentStatus == status)
        .fold(0.0, (sum, payment) => sum + (payment.amount ?? 0.0));
  }

  // Calculate total paid amount
  double get totalPaidAmount {
    return getTotalAmountByStatus(PaymentStatus.paid);
  }

  // Calculate total pending amount
  double get totalPendingAmount {
    return getTotalAmountByStatus(PaymentStatus.pending);
  }

  // Calculate total overdue amount
  double get totalOverdueAmount {
    return getTotalAmountByStatus(PaymentStatus.overdue);
  }

  // Get payment statistics
  Map<String, dynamic> getPaymentStatistics() {
    final typeStats = <String, double>{};
    final statusStats = <String, double>{};
    final methodStats = <String, double>{};

    for (final payment in _payments) {
      // Type statistics
      final type = payment.paymentType.name;
      typeStats[type] = (typeStats[type] ?? 0.0) + (payment.amount ?? 0.0);

      // Status statistics
      final status = payment.paymentStatus.name;
      statusStats[status] = (statusStats[status] ?? 0.0) + (payment.amount ?? 0.0);

      // Method statistics (only for paid payments)
      if (payment.paymentStatus == PaymentStatus.paid && payment.paymentMethod != null) {
        final method = payment.paymentMethod!.name;
        methodStats[method] = (methodStats[method] ?? 0.0) + (payment.amount ?? 0.0);
      }
    }

    return {
      'totalPayments': _payments.length,
      'totalAmount': _payments.fold(0.0, (sum, p) => sum + (p.amount ?? 0.0)),
      'paidCount': paidPayments.length,
      'pendingCount': pendingPayments.length,
      'overdueCount': overduePayments.length,
      'upcomingCount': getUpcomingPayments().length,
      'typeStats': typeStats,
      'statusStats': statusStats,
      'methodStats': methodStats,
    };
  }

  // Search payments
  List<Payment> searchPayments(String query) {
    if (query.isEmpty) return _payments;
    
    final lowercaseQuery = query.toLowerCase();
    return _payments.where((payment) {
      return payment.transactionReference?.toLowerCase().contains(lowercaseQuery) == true ||
             payment.notes?.toLowerCase().contains(lowercaseQuery) == true;
    }).toList();
  }

  // Get payment by ID
  Payment? getPaymentById(String paymentId) {
    try {
      return _payments.firstWhere((payment) => payment.id == paymentId);
    } catch (e) {
      return null;
    }
  }

  // Remove payment
  Future<void> removePayment(String paymentId) async {
    try {
      _setLoading(true);
      
      _payments.removeWhere((payment) => payment.id == paymentId);
      
      // Note: Payment deletion is handled locally only
      
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to remove payment: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Clear all payments
  void clearPayments() {
    _payments.clear();
    notifyListeners();
  }

  // Refresh payments
  Future<void> refreshPayments() async {
    await initializePayments();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }
}
