# LiteSpeed Shared Hosting Deployment Guide

## 🚀 **Optimized for LiteSpeed Shared Hosting**

This guide will help you deploy the Chit Fund API backend on LiteSpeed shared hosting with optimal performance.

## 📋 **Prerequisites**

- LiteSpeed shared hosting account
- PHP 7.4+ or PHP 8.0+ support
- MySQL 5.7+ or MariaDB 10.3+
- SSL certificate (recommended)
- FTP/SFTP access or File Manager

## 🔧 **Step 1: Prepare Your Files**

### 1.1 Configuration Setup
```bash
# Copy the LiteSpeed optimized configuration
cp config/env.litespeed.php config/env.php

# Update the configuration with your hosting details
nano config/env.php
```

### 1.2 Update Database Settings
```php
// In config/env.php
define('DB_HOST', 'localhost'); // Usually localhost
define('DB_NAME', 'your_cpanel_database_name');
define('DB_USER', 'your_cpanel_database_user');
define('DB_PASS', 'your_database_password');
```

### 1.3 Update Domain Settings
```php
// In config/env.php
define('APP_URL', 'https://yourdomain.com');
define('API_BASE_URL', 'https://yourdomain.com/api');

// Update CORS origins
define('CORS_ALLOWED_ORIGINS', [
    'https://yourdomain.com',
    'https://www.yourdomain.com'
]);
```

## 📁 **Step 2: File Structure for Shared Hosting**

```
public_html/
├── api/
│   ├── index.php (main API entry point)
│   ├── auth/
│   ├── chits/
│   ├── members/
│   ├── payments/
│   ├── bidding/
│   └── reports/
├── config/
│   ├── env.php (your production config)
│   ├── litespeed_config.php
│   ├── litespeed_database.php
│   └── database.php
├── uploads/ (chmod 755)
├── logs/ (chmod 755)
├── cache/ (chmod 755)
├── .htaccess (LiteSpeed optimized)
└── README.md
```

## 🗄️ **Step 3: Database Setup**

### 3.1 Create Database via cPanel
1. Login to cPanel
2. Go to "MySQL Databases"
3. Create a new database: `your_prefix_chitfund`
4. Create a database user with full privileges
5. Note down the database name, username, and password

### 3.2 Import Database Schema
```sql
-- Run this SQL in phpMyAdmin or MySQL command line
-- (Use the provided database.sql file)

-- Or upload via phpMyAdmin:
-- 1. Go to phpMyAdmin in cPanel
-- 2. Select your database
-- 3. Click "Import"
-- 4. Upload the database.sql file
```

## 🔐 **Step 4: Security Configuration**

### 4.1 Generate Strong JWT Secret
```php
// Generate a strong 64-character secret
$secret = bin2hex(random_bytes(32));
echo $secret; // Use this in your env.php
```

### 4.2 Set File Permissions
```bash
# Set proper permissions
chmod 755 public_html/
chmod 755 public_html/api/
chmod 755 public_html/uploads/
chmod 755 public_html/logs/
chmod 755 public_html/cache/
chmod 644 public_html/.htaccess
chmod 600 config/env.php
```

### 4.3 Secure Sensitive Directories
```apache
# Add to .htaccess in config/ directory
Order deny,allow
Deny from all

# Add to .htaccess in logs/ directory
Order deny,allow
Deny from all
```

## ⚡ **Step 5: LiteSpeed Optimizations**

### 5.1 Enable OPcache (if available)
```php
// Add to .htaccess or ask hosting provider
php_value opcache.enable 1
php_value opcache.memory_consumption 128
php_value opcache.max_accelerated_files 4000
php_value opcache.revalidate_freq 60
```

### 5.2 Configure LiteSpeed Cache
```apache
# In .htaccess
<IfModule Litespeed>
    CacheLookup on
    
    # Cache API responses for 30 minutes
    <LocationMatch "\.php$">
        Header set X-LiteSpeed-Cache-Control "public, max-age=1800"
        Header append X-LiteSpeed-Tag "chitfund-api"
    </LocationMatch>
    
    # Don't cache sensitive endpoints
    <LocationMatch "(login|logout|register|password)">
        Header set X-LiteSpeed-Cache-Control "no-cache"
    </LocationMatch>
</IfModule>
```

### 5.3 Enable Compression
```apache
# In .htaccess
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>
```

## 📧 **Step 6: Email Configuration**

### 6.1 SMTP Settings
```php
// In config/env.php - Use your hosting SMTP
define('SMTP_HOST', 'mail.yourdomain.com');
define('SMTP_PORT', 587); // or 465 for SSL
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_email_password');
define('SMTP_ENCRYPTION', 'tls'); // or 'ssl'
```

### 6.2 Test Email Configuration
```php
// Create test_email.php
<?php
require_once 'config/env.php';
require_once 'utils/EmailService.php';

$emailService = new EmailService();
$result = $emailService->sendTestEmail('<EMAIL>');
echo $result ? 'Email sent successfully!' : 'Email failed!';
?>
```

## 🔍 **Step 7: Testing and Verification**

### 7.1 API Health Check
```bash
# Test API endpoint
curl -X GET https://yourdomain.com/api/health
```

### 7.2 Database Connection Test
```php
// Create test_db.php
<?php
require_once 'config/env.php';
require_once 'config/litespeed_database.php';

$db = new LiteSpeedDatabase();
$conn = $db->getConnection();

if ($conn) {
    echo "Database connection successful!";
    echo "\nDatabase: " . DB_NAME;
    echo "\nHost: " . DB_HOST;
} else {
    echo "Database connection failed!";
}
?>
```

### 7.3 Performance Test
```bash
# Test API performance
curl -w "@curl-format.txt" -o /dev/null -s https://yourdomain.com/api/auth/login
```

## 📊 **Step 8: Monitoring and Maintenance**

### 8.1 Log Monitoring
```bash
# Check error logs
tail -f logs/php_errors.log
tail -f logs/app.log
```

### 8.2 Cache Management
```php
// Create cache_clear.php for manual cache clearing
<?php
require_once 'config/litespeed_database.php';

$db = new LiteSpeedDatabase();
$db->clearCache();
echo "Cache cleared successfully!";
?>
```

### 8.3 Database Optimization
```sql
-- Run weekly via cron job or manually
OPTIMIZE TABLE users;
OPTIMIZE TABLE chits;
OPTIMIZE TABLE members;
OPTIMIZE TABLE payments;
OPTIMIZE TABLE bidding_rounds;
```

## 🚨 **Troubleshooting**

### Common Issues and Solutions

#### 1. 500 Internal Server Error
```bash
# Check error logs
tail -n 50 logs/php_errors.log

# Common causes:
# - Incorrect file permissions
# - PHP syntax errors
# - Missing PHP extensions
# - Database connection issues
```

#### 2. Database Connection Failed
```php
// Check database credentials
// Verify database exists
// Check if database user has proper privileges
// Test connection manually
```

#### 3. CORS Issues
```apache
# Update .htaccess CORS headers
Header always set Access-Control-Allow-Origin "https://yourdomain.com"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
```

#### 4. Performance Issues
```php
// Enable query caching
define('ENABLE_QUERY_CACHE', true);

// Reduce cache TTL
define('CACHE_TTL', 900); // 15 minutes

// Enable compression
define('ENABLE_COMPRESSION', true);
```

## 📈 **Performance Optimization Tips**

### 1. Database Optimization
- Use indexes on frequently queried columns
- Optimize queries to avoid N+1 problems
- Use connection pooling
- Enable query caching

### 2. Caching Strategy
- Cache API responses for 30 minutes
- Use LiteSpeed cache headers
- Implement database query caching
- Cache static assets for 1 year

### 3. File Optimization
- Minimize file sizes
- Use compression
- Optimize images before upload
- Clean up old log files regularly

### 4. Security Best Practices
- Use HTTPS only
- Implement rate limiting
- Validate all inputs
- Use prepared statements
- Keep software updated

## 🔄 **Backup Strategy**

### 1. Database Backup
```bash
# Daily database backup (via cron)
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql
```

### 2. File Backup
```bash
# Weekly file backup
tar -czf backup_files_$(date +%Y%m%d).tar.gz public_html/
```

### 3. Automated Backups
```php
// Create backup script for cron job
<?php
// backup.php - Run daily via cron
require_once 'config/env.php';

// Database backup
$backup_file = 'backup_' . date('Y-m-d') . '.sql';
$command = "mysqldump -h " . DB_HOST . " -u " . DB_USER . " -p" . DB_PASS . " " . DB_NAME . " > backups/" . $backup_file;
exec($command);

// Clean old backups (keep 30 days)
$old_backups = glob('backups/backup_*.sql');
if (count($old_backups) > 30) {
    array_map('unlink', array_slice($old_backups, 0, -30));
}

echo "Backup completed: " . $backup_file;
?>
```

## 📞 **Support and Maintenance**

### Regular Maintenance Tasks
1. **Weekly**: Check error logs, clear cache, optimize database
2. **Monthly**: Update dependencies, review security logs
3. **Quarterly**: Performance review, backup verification

### Monitoring Checklist
- [ ] API response times < 2 seconds
- [ ] Database queries < 100ms average
- [ ] Error rate < 1%
- [ ] Disk usage < 80%
- [ ] Memory usage < 80%

---

## 🎉 **Deployment Complete!**

Your Chit Fund API is now optimized and deployed on LiteSpeed shared hosting. The system is configured for:

✅ **High Performance** - LiteSpeed cache, OPcache, compression
✅ **Security** - HTTPS, rate limiting, input validation
✅ **Scalability** - Connection pooling, query optimization
✅ **Monitoring** - Comprehensive logging and error tracking
✅ **Maintenance** - Automated backups and cleanup

For support, check the logs first, then contact your hosting provider if needed.
