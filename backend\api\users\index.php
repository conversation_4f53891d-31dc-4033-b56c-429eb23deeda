<?php
/**
 * Users API Routes - LiteSpeed Optimized
 */

require_once __DIR__ . '/../../controllers/UserController.php';

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];

// Remove /api/users prefix and get the endpoint
$endpoint = str_replace('/api/users', '', parse_url($path, PHP_URL_PATH));
$endpoint = trim($endpoint, '/');

// Split path into segments
$segments = explode('/', $endpoint);
$userId = $segments[0] ?? null;
$action = $segments[1] ?? null;

// Initialize controller
$userController = new UserController();

// LiteSpeed cache control for users endpoints
if ($method === 'GET') {
    header('X-LiteSpeed-Cache-Control: public, max-age=600'); // 10 minutes
    header('X-LiteSpeed-Tag: users');
} else {
    header('X-LiteSpeed-Cache-Control: no-cache');
}

try {
    switch ($method) {
        case 'GET':
            if (empty($userId)) {
                // GET /api/users - List all users
                $userController->getAllUsers();
            } elseif ($userId === 'search') {
                // GET /api/users/search - Search users
                $userController->searchUsers();
            } elseif (empty($action)) {
                // GET /api/users/{id} - Get specific user
                $userController->getUser($userId);
            } elseif ($action === 'chits') {
                // GET /api/users/{id}/chits - Get user's chits
                $userController->getUserChits($userId);
            } elseif ($action === 'payments') {
                // GET /api/users/{id}/payments - Get user's payments
                $userController->getUserPayments($userId);
            } elseif ($action === 'activity') {
                // GET /api/users/{id}/activity - Get user activity
                $userController->getUserActivity($userId);
            } elseif ($action === 'summary') {
                // GET /api/users/{id}/summary - Get user summary
                $userController->getUserSummary($userId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'POST':
            if (empty($userId)) {
                // POST /api/users - Create new user
                $userController->createUser();
            } elseif ($action === 'activate') {
                // POST /api/users/{id}/activate - Activate user
                $userController->activateUser($userId);
            } elseif ($action === 'deactivate') {
                // POST /api/users/{id}/deactivate - Deactivate user
                $userController->deactivateUser($userId);
            } elseif ($action === 'reset-password') {
                // POST /api/users/{id}/reset-password - Reset user password
                $userController->resetUserPassword($userId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'PUT':
            if (!empty($userId) && empty($action)) {
                // PUT /api/users/{id} - Update user
                $userController->updateUser($userId);
            } elseif (!empty($userId) && $action === 'role') {
                // PUT /api/users/{id}/role - Update user role
                $userController->updateUserRole($userId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'DELETE':
            if (!empty($userId) && empty($action)) {
                // DELETE /api/users/{id} - Delete user
                $userController->deleteUser($userId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        default:
            sendError('Method not allowed', 405);
            break;
    }
} catch (Exception $e) {
    error_log("Users API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

?>
