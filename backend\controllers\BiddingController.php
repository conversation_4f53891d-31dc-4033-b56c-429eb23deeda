<?php
/**
 * Bidding Controller
 *
 * Handles bidding operations
 */

// Import required dependencies
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../utils/JWT.php';
require_once __DIR__ . '/../models/BiddingRound.php';
require_once __DIR__ . '/../models/Bid.php';
require_once __DIR__ . '/../models/ChitMember.php';

class BiddingController {
    
    /**
     * Start a new bidding round
     */
    public function startBidding() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['chit_id'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($data['chit_id'])) {
                sendError('Chit not found', 404);
            }
            
            // Check if user is organizer
            if ($chit->organizer_id != $userId) {
                sendError('Only organizer can start bidding', 403);
            }
            
            // Check if chit is active
            if ($chit->status !== 'active') {
                sendError('Chit must be active to start bidding', 400);
            }
            
            // Check if there's already an active bidding round
            $biddingRound = new BiddingRound();
            if ($biddingRound->hasActiveBidding($data['chit_id'])) {
                sendError('There is already an active bidding round', 400);
            }
            
            // Create new bidding round
            $biddingRound->chit_id = $data['chit_id'];
            $biddingRound->round_number = $chit->current_round ?? 1;
            $biddingRound->status = 'active';
            $biddingRound->start_time = date('Y-m-d H:i:s');
            $biddingRound->end_time = date('Y-m-d H:i:s', strtotime('+30 minutes')); // 30 minutes bidding time
            
            if ($biddingRound->create()) {
                // Log activity
                logActivity($userId, 'bidding_started', [
                    'chit_id' => $data['chit_id'],
                    'round_number' => $biddingRound->round_number
                ]);
                
                sendSuccess($biddingRound->getDetails(), 'Bidding round started successfully');
            } else {
                sendError('Failed to start bidding round', 500);
            }
            
        } catch (Exception $e) {
            error_log("Start bidding error: " . $e->getMessage());
            sendError('Failed to start bidding', 500);
        }
    }
    
    /**
     * Place a bid
     */
    public function placeBid() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['bidding_round_id', 'bid_amount'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        // Validate bid amount
        if ($data['bid_amount'] <= 0) {
            sendError('Bid amount must be greater than 0', 400);
        }
        
        try {
            $biddingRound = new BiddingRound();
            
            if (!$biddingRound->findById($data['bidding_round_id'])) {
                sendError('Bidding round not found', 404);
            }
            
            // Check if bidding is still active
            if ($biddingRound->status !== 'active') {
                sendError('Bidding round is not active', 400);
            }
            
            // Check if bidding time has expired
            if (strtotime($biddingRound->end_time) < time()) {
                sendError('Bidding time has expired', 400);
            }
            
            // Check if user is a member of this chit
            $chitMember = new ChitMember();
            if (!$chitMember->isMember($biddingRound->chit_id, $userId)) {
                sendError('You are not a member of this chit', 403);
            }
            
            // Check if user is eligible to bid (hasn't won yet)
            $chitMember = new ChitMember();
            $db = new Database();
            $conn = $db->getConnection();
            $sql = "SELECT has_won FROM chit_members WHERE chit_id = ? AND user_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$biddingRound->chit_id, $userId]);
            $member = $stmt->fetch();
            
            if ($member && $member['has_won']) {
                sendError('You have already won in a previous round', 400);
            }
            
            // Create or update bid
            $bid = new Bid();
            $existingBid = $bid->getUserBid($data['bidding_round_id'], $userId);
            
            if ($existingBid) {
                // Update existing bid
                $bid->findById($existingBid['id']);
                $bid->bid_amount = $data['bid_amount'];
                $bid->update();
            } else {
                // Create new bid
                $bid->bidding_round_id = $data['bidding_round_id'];
                $bid->user_id = $userId;
                $bid->bid_amount = $data['bid_amount'];
                $bid->create();
            }
            
            // Log activity
            logActivity($userId, 'bid_placed', [
                'bidding_round_id' => $data['bidding_round_id'],
                'bid_amount' => $data['bid_amount']
            ]);
            
            sendSuccess($bid->getDetails(), 'Bid placed successfully');
            
        } catch (Exception $e) {
            error_log("Place bid error: " . $e->getMessage());
            sendError('Failed to place bid', 500);
        }
    }
    
    /**
     * End bidding round
     */
    public function endBidding() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['bidding_round_id'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        try {
            $biddingRound = new BiddingRound();
            
            if (!$biddingRound->findById($data['bidding_round_id'])) {
                sendError('Bidding round not found', 404);
            }
            
            // Check if user is organizer
            $chit = new Chit();
            $chit->findById($biddingRound->chit_id);
            
            if ($chit->organizer_id != $userId) {
                sendError('Only organizer can end bidding', 403);
            }
            
            // Check if bidding is active
            if ($biddingRound->status !== 'active') {
                sendError('Bidding round is not active', 400);
            }
            
            // Get winning bid (lowest bid)
            $bid = new Bid();
            $winningBid = $bid->getWinningBid($data['bidding_round_id']);
            
            if (!$winningBid) {
                sendError('No bids found for this round', 400);
            }
            
            // End bidding round
            $biddingRound->status = 'completed';
            $biddingRound->winner_id = $winningBid['user_id'];
            $biddingRound->winning_amount = $winningBid['bid_amount'];
            $biddingRound->actual_end_time = date('Y-m-d H:i:s');
            $biddingRound->update();
            
            // Mark member as winner
            $chitMember = new ChitMember();
            $db = new Database();
            $conn = $db->getConnection();
            $sql = "SELECT id FROM chit_members WHERE chit_id = ? AND user_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$biddingRound->chit_id, $winningBid['user_id']]);
            $memberData = $stmt->fetch();
            
            if ($memberData) {
                $chitMember->findById($memberData['id']);
                $chitMember->markAsWinner($biddingRound->round_number, $winningBid['bid_amount']);
            }
            
            // Create payment records for all members
            $this->createPaymentRecords($biddingRound->chit_id, $biddingRound->round_number, $winningBid);
            
            // Update chit current round
            $chit->current_round = $biddingRound->round_number + 1;
            $chit->update();
            
            // Log activity
            logActivity($userId, 'bidding_ended', [
                'bidding_round_id' => $data['bidding_round_id'],
                'winner_id' => $winningBid['user_id'],
                'winning_amount' => $winningBid['bid_amount']
            ]);
            
            sendSuccess([
                'bidding_round' => $biddingRound->getDetails(),
                'winner' => $winningBid
            ], 'Bidding round ended successfully');
            
        } catch (Exception $e) {
            error_log("End bidding error: " . $e->getMessage());
            sendError('Failed to end bidding', 500);
        }
    }
    
    /**
     * Get current bidding round for a chit
     */
    public function getCurrentBidding($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user has access to this chit
            if (!$chit->hasUserAccess($userId)) {
                sendError('Access denied', 403);
            }
            
            $biddingRound = new BiddingRound();
            $currentBidding = $biddingRound->getCurrentBidding($chitId);
            
            if ($currentBidding) {
                // Get all bids for this round
                $bid = new Bid();
                $bids = $bid->getRoundBids($currentBidding['id']);
                $currentBidding['bids'] = $bids;
            }
            
            sendSuccess($currentBidding, 'Current bidding retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get current bidding error: " . $e->getMessage());
            sendError('Failed to retrieve current bidding', 500);
        }
    }
    
    /**
     * Get bidding history for a chit
     */
    public function getBiddingHistory($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user has access to this chit
            if (!$chit->hasUserAccess($userId)) {
                sendError('Access denied', 403);
            }
            
            $biddingRound = new BiddingRound();
            $history = $biddingRound->getChitHistory($chitId);
            
            sendSuccess($history, 'Bidding history retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get bidding history error: " . $e->getMessage());
            sendError('Failed to retrieve bidding history', 500);
        }
    }
    
    /**
     * Create payment records for all members after bidding ends
     */
    private function createPaymentRecords($chitId, $roundNumber, $winningBid) {
        try {
            $chitMember = new ChitMember();
            $members = $chitMember->getActiveMembers($chitId);
            
            $chit = new Chit();
            $chit->findById($chitId);
            
            foreach ($members as $member) {
                $payment = new Payment();
                $payment->chit_id = $chitId;
                $payment->user_id = $member['user_id'];
                $payment->round_number = $roundNumber;
                $payment->amount = $chit->monthly_amount;
                $payment->due_date = date('Y-m-d', strtotime('+7 days')); // 7 days to pay
                $payment->status = 'pending';
                $payment->payment_type = 'monthly_contribution';
                
                // Winner gets the amount minus commission
                if ($member['user_id'] == $winningBid['user_id']) {
                    $commission = ($chit->monthly_amount * $chit->number_of_members) * ($chit->commission_percentage / 100);
                    $payment->amount = ($chit->monthly_amount * $chit->number_of_members) - $winningBid['bid_amount'] - $commission;
                    $payment->status = 'paid';
                    $payment->payment_type = 'winning_amount';
                    $payment->paid_at = date('Y-m-d H:i:s');
                }
                
                $payment->create();
            }
            
        } catch (Exception $e) {
            error_log("Create payment records error: " . $e->getMessage());
        }
    }
}
?>
