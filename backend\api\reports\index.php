<?php
/**
 * Reports API Routes - LiteSpeed Optimized
 */

require_once __DIR__ . '/../../controllers/ReportController.php';

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];

// Remove /api/reports prefix and get the endpoint
$endpoint = str_replace('/api/reports', '', parse_url($path, PHP_URL_PATH));
$endpoint = trim($endpoint, '/');

// Split path into segments
$segments = explode('/', $endpoint);
$reportType = $segments[0] ?? null;
$action = $segments[1] ?? null;

// Initialize controller
$reportController = new ReportController();

// LiteSpeed cache control for reports endpoints
header('X-LiteSpeed-Cache-Control: public, max-age=1800'); // 30 minutes
header('X-LiteSpeed-Tag: reports');

try {
    switch ($method) {
        case 'GET':
            if (empty($reportType)) {
                // GET /api/reports - List available reports
                $reportController->getAvailableReports();
            } elseif ($reportType === 'dashboard') {
                // GET /api/reports/dashboard - Dashboard summary
                $reportController->getDashboardReport();
            } elseif ($reportType === 'financial') {
                if ($action === 'summary') {
                    // GET /api/reports/financial/summary - Financial summary
                    $reportController->getFinancialSummary();
                } elseif ($action === 'detailed') {
                    // GET /api/reports/financial/detailed - Detailed financial report
                    $reportController->getDetailedFinancialReport();
                } else {
                    // GET /api/reports/financial - Financial overview
                    $reportController->getFinancialReport();
                }
            } elseif ($reportType === 'chits') {
                if ($action === 'performance') {
                    // GET /api/reports/chits/performance - Chit performance report
                    $reportController->getChitPerformanceReport();
                } elseif ($action === 'status') {
                    // GET /api/reports/chits/status - Chit status report
                    $reportController->getChitStatusReport();
                } else {
                    // GET /api/reports/chits - Chits overview
                    $reportController->getChitsReport();
                }
            } elseif ($reportType === 'members') {
                if ($action === 'activity') {
                    // GET /api/reports/members/activity - Member activity report
                    $reportController->getMemberActivityReport();
                } elseif ($action === 'payments') {
                    // GET /api/reports/members/payments - Member payments report
                    $reportController->getMemberPaymentsReport();
                } else {
                    // GET /api/reports/members - Members overview
                    $reportController->getMembersReport();
                }
            } elseif ($reportType === 'payments') {
                if ($action === 'pending') {
                    // GET /api/reports/payments/pending - Pending payments
                    $reportController->getPendingPaymentsReport();
                } elseif ($action === 'overdue') {
                    // GET /api/reports/payments/overdue - Overdue payments
                    $reportController->getOverduePaymentsReport();
                } elseif ($action === 'summary') {
                    // GET /api/reports/payments/summary - Payments summary
                    $reportController->getPaymentsSummaryReport();
                } else {
                    // GET /api/reports/payments - Payments overview
                    $reportController->getPaymentsReport();
                }
            } elseif ($reportType === 'bidding') {
                if ($action === 'history') {
                    // GET /api/reports/bidding/history - Bidding history
                    $reportController->getBiddingHistoryReport();
                } elseif ($action === 'analytics') {
                    // GET /api/reports/bidding/analytics - Bidding analytics
                    $reportController->getBiddingAnalyticsReport();
                } else {
                    // GET /api/reports/bidding - Bidding overview
                    $reportController->getBiddingReport();
                }
            } elseif ($reportType === 'export') {
                if ($action === 'pdf') {
                    // GET /api/reports/export/pdf - Export to PDF
                    $reportController->exportToPDF();
                } elseif ($action === 'excel') {
                    // GET /api/reports/export/excel - Export to Excel
                    $reportController->exportToExcel();
                } elseif ($action === 'csv') {
                    // GET /api/reports/export/csv - Export to CSV
                    $reportController->exportToCSV();
                } else {
                    sendError('Export format not specified', 400);
                }
            } elseif ($reportType === 'analytics') {
                if ($action === 'trends') {
                    // GET /api/reports/analytics/trends - Trend analysis
                    $reportController->getTrendsAnalysis();
                } elseif ($action === 'forecasting') {
                    // GET /api/reports/analytics/forecasting - Forecasting
                    $reportController->getForecasting();
                } else {
                    // GET /api/reports/analytics - Analytics overview
                    $reportController->getAnalyticsReport();
                }
            } else {
                sendError('Report type not found', 404);
            }
            break;
            
        case 'POST':
            if ($reportType === 'custom') {
                // POST /api/reports/custom - Generate custom report
                $reportController->generateCustomReport();
            } elseif ($reportType === 'schedule') {
                // POST /api/reports/schedule - Schedule report
                $reportController->scheduleReport();
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        default:
            sendError('Method not allowed', 405);
            break;
    }
} catch (Exception $e) {
    error_log("Reports API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

?>
