import 'package:json_annotation/json_annotation.dart';

part 'notification_model.g.dart';

@JsonSerializable()
class NotificationModel {
  final String id;
  final String userId;
  final String? chitId;
  final String title;
  final String message;
  final NotificationType type;
  final bool isRead;
  final DateTime? scheduledAt;
  final DateTime? sentAt;
  final DateTime createdAt;
  final NotificationPriority priority;
  final bool isArchived;
  final Map<String, dynamic> data;

  const NotificationModel({
    required this.id,
    required this.userId,
    this.chitId,
    required this.title,
    required this.message,
    required this.type,
    this.isRead = false,
    this.scheduledAt,
    this.sentAt,
    required this.createdAt,
    this.priority = NotificationPriority.normal,
    this.isArchived = false,
    this.data = const {},
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) => _$NotificationModelFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? chitId,
    String? title,
    String? message,
    NotificationType? type,
    bool? isRead,
    DateTime? scheduledAt,
    DateTime? sentAt,
    DateTime? createdAt,
    NotificationPriority? priority,
    bool? isArchived,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      chitId: chitId ?? this.chitId,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      sentAt: sentAt ?? this.sentAt,
      createdAt: createdAt ?? this.createdAt,
      priority: priority ?? this.priority,
      isArchived: isArchived ?? this.isArchived,
      data: data ?? this.data,
    );
  }

  NotificationModel markAsRead() {
    return copyWith(isRead: true);
  }

  // Convenience getters
  String get body => message;

  bool get isPending => scheduledAt != null && sentAt == null;
  bool get isSent => sentAt != null;
  bool get isOverdue => scheduledAt != null &&
                       scheduledAt!.isBefore(DateTime.now()) &&
                       sentAt == null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, isRead: $isRead)';
  }
}

enum NotificationType {
  @JsonValue('reminder')
  reminder,
  @JsonValue('bidding')
  bidding,
  @JsonValue('payment')
  payment,
  @JsonValue('general')
  general,
  @JsonValue('system')
  system,
}

enum NotificationPriority {
  @JsonValue('low')
  low,
  @JsonValue('normal')
  normal,
  @JsonValue('high')
  high,
}

extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.reminder:
        return 'Reminder';
      case NotificationType.bidding:
        return 'Bidding';
      case NotificationType.payment:
        return 'Payment';
      case NotificationType.general:
        return 'General';
      case NotificationType.system:
        return 'System';
    }
  }

  String get description {
    switch (this) {
      case NotificationType.reminder:
        return 'Meeting and event reminders';
      case NotificationType.bidding:
        return 'Bidding related notifications';
      case NotificationType.payment:
        return 'Payment due and received notifications';
      case NotificationType.general:
        return 'General chit fund updates';
      case NotificationType.system:
        return 'System announcements';
    }
  }
}

extension NotificationPriorityExtension on NotificationPriority {
  String get displayName {
    switch (this) {
      case NotificationPriority.low:
        return 'Low';
      case NotificationPriority.normal:
        return 'Normal';
      case NotificationPriority.high:
        return 'High';
    }
  }
}
