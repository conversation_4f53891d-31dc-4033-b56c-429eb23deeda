<?php
/**
 * Get Test OTP - For development testing only
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Include configuration
    if (file_exists(__DIR__ . '/../config/env.php')) {
        require_once __DIR__ . '/../config/env.php';
    }
    
    // Include database
    require_once __DIR__ . '/../config/database.php';
    
    $db = new Database();
    $conn = $db->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get the email parameter
    $email = $_GET['email'] ?? '<EMAIL>';
    
    // Get the most recent OTP for this email
    $sql = "SELECT otp, type, expires_at, created_at 
            FROM otps 
            WHERE email = ? 
            ORDER BY created_at DESC 
            LIMIT 1";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$email]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        $isExpired = strtotime($result['expires_at']) < time();
        
        echo json_encode([
            'success' => true,
            'message' => 'OTP found',
            'data' => [
                'email' => $email,
                'otp' => $result['otp'],
                'type' => $result['type'],
                'expires_at' => $result['expires_at'],
                'created_at' => $result['created_at'],
                'is_expired' => $isExpired,
                'time_remaining' => $isExpired ? 0 : (strtotime($result['expires_at']) - time())
            ]
        ], JSON_PRETTY_PRINT);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No OTP found for this email',
            'data' => [
                'email' => $email,
                'suggestion' => 'Try sending an OTP first through the registration flow'
            ]
        ], JSON_PRETTY_PRINT);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error retrieving OTP',
        'error' => $e->getMessage()
    ], JSON_PRETTY_PRINT);
}

?>
