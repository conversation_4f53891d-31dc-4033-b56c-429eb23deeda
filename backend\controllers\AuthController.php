<?php
/**
 * Authentication Controller - LiteSpeed Optimized
 *
 * Handles user authentication operations with OTP validation
 */

// Import required dependencies
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../utils/JWT.php';
require_once __DIR__ . '/../utils/EmailService.php';
require_once __DIR__ . '/../models/User.php';

class AuthController {

    /**
     * Generate and send OTP
     */
    public function sendOTP() {
        $data = getRequestData();

        // Debug mode - add detailed error reporting
        $debug = isset($data['debug']) && $data['debug'] === true;

        if ($debug) {
            error_log("DEBUG: sendOTP called with data: " . json_encode($data));
        }

        // Validate required fields
        $required = ['email', 'type'];
        $missing = validateRequired($data, $required);

        if (!empty($missing)) {
            if ($debug) {
                error_log("DEBUG: Missing required fields: " . implode(', ', $missing));
            }
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }

        $email = sanitizeInput($data['email']);
        $type = sanitizeInput($data['type']); // 'registration' or 'forgot_password'

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            sendError('Invalid email format', 400);
        }

        // Validate OTP type
        if (!in_array($type, ['registration', 'forgot_password'])) {
            sendError('Invalid OTP type', 400);
        }

        try {
            if ($debug) {
                error_log("DEBUG: Starting OTP generation for $email, type: $type");
            }

            $user = new User();

            // For registration, check if email doesn't exist
            if ($type === 'registration' && $user->emailExists($email)) {
                if ($debug) {
                    error_log("DEBUG: Email already exists: $email");
                }
                sendError('Email already registered', 409);
            }

            // For forgot password, check if email exists
            if ($type === 'forgot_password' && !$user->findByEmail($email)) {
                // Don't reveal if email exists or not for security
                sendSuccess(null, 'If the email exists, an OTP has been sent');
                return;
            }

            // Generate 6-digit OTP
            $otp = sprintf('%06d', mt_rand(0, 999999));
            $expires_at = date('Y-m-d H:i:s', time() + 600); // 10 minutes

            // Store OTP in database
            if ($debug) {
                error_log("DEBUG: Connecting to database");
            }

            $db = new Database();
            $conn = $db->getConnection();

            if (!$conn) {
                throw new Exception("Database connection failed");
            }

            if ($debug) {
                error_log("DEBUG: Database connected, inserting OTP: $otp");
            }

            // Delete any existing OTPs for this email and type
            $sql = "DELETE FROM otps WHERE email = ? AND type = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email, $type]);

            // Insert new OTP
            $sql = "INSERT INTO otps (email, otp, type, expires_at, created_at) VALUES (?, ?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute([$email, $otp, $type, $expires_at]);

            if (!$result) {
                throw new Exception("Failed to insert OTP into database");
            }

            if ($debug) {
                error_log("DEBUG: OTP inserted successfully");
            }

            // Send OTP via email (with error handling)
            try {
                EmailService::sendOTP($email, $otp, $user->name ?? '');
            } catch (Exception $emailError) {
                error_log("Email sending failed: " . $emailError->getMessage());
                // Continue without failing the entire process
            }

            // Log activity (with error handling)
            try {
                logActivity(null, 'otp_sent', [
                    'email' => $email,
                    'type' => $type,
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? null
                ]);
            } catch (Exception $logError) {
                error_log("Activity logging failed: " . $logError->getMessage());
                // Continue without failing the entire process
            }

            if ($debug) {
                error_log("DEBUG: All steps completed successfully");
            }

            sendSuccess([
                'email' => $email,
                'expires_in' => 600 // 10 minutes
            ], 'OTP sent successfully to your email');

        } catch (Exception $e) {
            error_log("Send OTP error: " . $e->getMessage());
            error_log("Send OTP error trace: " . $e->getTraceAsString());

            if ($debug) {
                sendError('Failed to send OTP: ' . $e->getMessage(), 500);
            } else {
                sendError('Failed to send OTP', 500);
            }
        }
    }

    /**
     * Verify OTP
     */
    public function verifyOTP() {
        $data = getRequestData();

        // Validate required fields
        $required = ['email', 'otp', 'type'];
        $missing = validateRequired($data, $required);

        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }

        $email = sanitizeInput($data['email']);
        $otp = sanitizeInput($data['otp']);
        $type = sanitizeInput($data['type']);

        // Validate OTP format (6 digits)
        if (!preg_match('/^\d{6}$/', $otp)) {
            sendError('Invalid OTP format', 400);
        }

        try {
            $db = new Database();
            $conn = $db->getConnection();

            // Find valid OTP
            $sql = "SELECT id FROM otps WHERE email = ? AND otp = ? AND type = ? AND expires_at > NOW() AND verified = 0";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email, $otp, $type]);

            if (!$row = $stmt->fetch()) {
                sendError('Invalid or expired OTP', 400);
            }

            $otpId = $row['id'];

            // Mark OTP as verified
            $sql = "UPDATE otps SET verified = 1, verified_at = NOW() WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$otpId]);

            // Log activity
            logActivity(null, 'otp_verified', [
                'email' => $email,
                'type' => $type,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? null
            ]);

            sendSuccess([
                'email' => $email,
                'verified' => true
            ], 'OTP verified successfully');

        } catch (Exception $e) {
            error_log("Verify OTP error: " . $e->getMessage());
            sendError('Failed to verify OTP', 500);
        }
    }

    /**
     * Send OTP via email using SMTP
     */
    private function sendOTPEmail($email, $otp, $type) {
        $subject = $type === 'registration' ? 'Chit Fund - Registration OTP' : 'Chit Fund - Password Reset OTP';

        // Create HTML email content
        $htmlMessage = $this->createOTPEmailHTML($otp, $type);
        $textMessage = "Your OTP for $type is: $otp\n\nThis OTP will expire in 10 minutes.\n\nIf you didn't request this, please ignore this email.";

        try {
            // Use SMTP configuration from env.php
            $this->sendSMTPEmail($email, $subject, $htmlMessage, $textMessage);
            error_log("OTP email sent successfully to: $email");
        } catch (Exception $e) {
            error_log("Failed to send OTP email to $email: " . $e->getMessage());
            // Log the OTP for debugging
            error_log("OTP for $email: $otp");
            throw $e;
        }
    }

    /**
     * Create HTML email template for OTP
     */
    private function createOTPEmailHTML($otp, $type) {
        $title = $type === 'registration' ? 'Registration Verification' : 'Password Reset';
        $action = $type === 'registration' ? 'complete your registration' : 'reset your password';

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>$title - Chit Fund</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;'>
            <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                <h1 style='color: white; margin: 0; font-size: 28px;'>Chit Fund Manager</h1>
                <p style='color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;'>$title</p>
            </div>

            <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;'>
                <h2 style='color: #333; margin-top: 0;'>Your Verification Code</h2>
                <p>Hello,</p>
                <p>Use the following OTP to $action:</p>

                <div style='background: white; border: 2px dashed #667eea; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;'>
                    <h1 style='color: #667eea; font-size: 36px; margin: 0; letter-spacing: 8px; font-family: monospace;'>$otp</h1>
                </div>

                <p><strong>Important:</strong></p>
                <ul style='color: #666;'>
                    <li>This OTP will expire in <strong>10 minutes</strong></li>
                    <li>Do not share this code with anyone</li>
                    <li>If you didn't request this, please ignore this email</li>
                </ul>

                <hr style='border: none; border-top: 1px solid #ddd; margin: 30px 0;'>
                <p style='color: #888; font-size: 14px; text-align: center;'>
                    This is an automated message from Chit Fund Manager.<br>
                    Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>";
    }

    /**
     * Send email using SMTP
     */
    private function sendSMTPEmail($to, $subject, $htmlBody, $textBody) {
        // SMTP Configuration
        $smtpHost = defined('SMTP_HOST') ? SMTP_HOST : 'smtp.gmail.com';
        $smtpPort = defined('SMTP_PORT') ? SMTP_PORT : 587;
        $smtpUsername = defined('SMTP_USERNAME') ? SMTP_USERNAME : '';
        $smtpPassword = defined('SMTP_PASSWORD') ? SMTP_PASSWORD : '';
        $fromEmail = defined('SMTP_FROM_EMAIL') ? SMTP_FROM_EMAIL : '<EMAIL>';
        $fromName = defined('SMTP_FROM_NAME') ? SMTP_FROM_NAME : 'Chit Fund Manager';

        if (empty($smtpUsername) || empty($smtpPassword)) {
            throw new Exception("SMTP credentials not configured");
        }

        // Create email headers
        $boundary = md5(time());

        $headers = "From: $fromName <$fromEmail>\r\n";
        $headers .= "Reply-To: $fromEmail\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: multipart/alternative; boundary=\"$boundary\"\r\n";

        // Create email body
        $body = "--$boundary\r\n";
        $body .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $body .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $body .= $textBody . "\r\n";
        $body .= "--$boundary\r\n";
        $body .= "Content-Type: text/html; charset=UTF-8\r\n";
        $body .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $body .= $htmlBody . "\r\n";
        $body .= "--$boundary--";

        // Send using PHP's mail function with proper headers
        $success = mail($to, $subject, $body, $headers);

        if (!$success) {
            throw new Exception("Failed to send email via SMTP");
        }
    }
    
    /**
     * User login
     */
    public function login() {
        $data = getRequestData();
        
        // Validate required fields
        $required = ['email', 'password'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        // Sanitize input
        $email = sanitizeInput($data['email']);
        $password = $data['password'];
        
        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            sendError('Invalid email format', 400);
        }
        
        try {
            $user = new User();
            
            // Find user by email
            if (!$user->findByEmail($email)) {
                sendError('Invalid credentials', 401);
            }
            
            // Check if user is active
            if (!$user->is_active) {
                sendError('Account is deactivated', 401);
            }
            
            // Check for account lockout
            if ($this->isAccountLocked($email)) {
                sendError('Account temporarily locked due to multiple failed login attempts. Please try again later.', 423);
            }

            // Verify password
            if (!$user->verifyPassword($password)) {
                // Record failed login attempt
                $this->recordFailedLogin($email);

                // Check if account should be locked
                if ($this->shouldLockAccount($email)) {
                    logActivity($user->id, 'account_locked', [
                        'reason' => 'multiple_failed_logins',
                        'ip' => $_SERVER['REMOTE_ADDR'] ?? null
                    ]);
                }

                sendError('Invalid credentials', 401);
            }

            // Clear failed login attempts on successful login
            $this->clearFailedLogins($email);
            
            // Generate JWT token
            $token = JWT::createUserToken($user->id, [
                'email' => $user->email,
                'role' => $user->role
            ]);
            
            // Generate refresh token
            $refreshToken = JWT::createRefreshToken($user->id);
            
            // Log activity
            logActivity($user->id, 'user_login', [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
            
            // Return success response
            sendSuccess([
                'token' => $token,
                'refresh_token' => $refreshToken,
                'user' => $user->getPublicData()
            ], 'Login successful');
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            sendError('Login failed', 500);
        }
    }
    
    /**
     * User registration with OTP verification
     */
    public function register() {
        $data = getRequestData();

        // Validate required fields
        $required = ['name', 'email', 'phone', 'password', 'otp'];
        $missing = validateRequired($data, $required);

        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }

        // Sanitize input
        $name = sanitizeInput($data['name']);
        $email = sanitizeInput($data['email']);
        $phone = sanitizeInput($data['phone']);
        $password = $data['password'];
        $otp = sanitizeInput($data['otp']);

        // Validate input
        if (strlen($name) < 2) {
            sendError('Name must be at least 2 characters long', 400);
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            sendError('Invalid email format', 400);
        }

        if (strlen($password) < 6) {
            sendError('Password must be at least 6 characters long', 400);
        }

        // Validate phone number (Indian format)
        if (!preg_match('/^[6-9]\d{9}$/', $phone)) {
            sendError('Invalid phone number format (10 digits starting with 6-9)', 400);
        }

        // Validate OTP format
        if (!preg_match('/^\d{6}$/', $otp)) {
            sendError('Invalid OTP format', 400);
        }

        try {
            $db = new Database();
            $conn = $db->getConnection();

            // Find and verify OTP (check both verified and unverified)
            $sql = "SELECT id, verified FROM otps WHERE email = ? AND otp = ? AND type = 'registration' AND expires_at > NOW()";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email, $otp]);

            if (!$otpRow = $stmt->fetch()) {
                sendError('Invalid or expired OTP', 400);
            }

            // If OTP is not verified yet, verify it now
            if (!$otpRow['verified']) {
                $sql = "UPDATE otps SET verified = 1, verified_at = NOW() WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$otpRow['id']]);

                error_log("OTP verified during registration for: $email");
            }

            $user = new User();

            // Check if email already exists
            if ($user->emailExists($email)) {
                sendError('Email already registered', 409);
            }

            // Check if phone already exists
            if ($user->phoneExists($phone)) {
                sendError('Phone number already registered', 409);
            }

            // Create new user
            $user->name = $name;
            $user->email = $email;
            $user->phone = $phone;
            $user->password_hash = $password; // Will be hashed in create method
            $user->role = 'member'; // Default role

            if ($user->create()) {
                // Delete used OTP
                $sql = "DELETE FROM otps WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$otpRow['id']]);

                // Log activity
                logActivity($user->id, 'user_registered', [
                    'email' => $email,
                    'phone' => $phone
                ]);

                // Generate JWT token for immediate login
                $token = JWT::createUserToken($user->id, [
                    'email' => $user->email,
                    'role' => $user->role
                ]);

                $refreshToken = JWT::createRefreshToken($user->id);

                sendSuccess([
                    'token' => $token,
                    'refresh_token' => $refreshToken,
                    'user' => $user->getPublicData()
                ], 'Registration successful');
            } else {
                sendError('Registration failed', 500);
            }

        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            sendError('Registration failed', 500);
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        $userId = JWT::getUserIdFromToken();
        
        if ($userId) {
            // Log activity
            logActivity($userId, 'user_logout');
        }
        
        // In a more sophisticated implementation, you would:
        // 1. Add token to blacklist
        // 2. Clear server-side sessions
        // For now, we just return success as JWT is stateless
        
        sendSuccess(null, 'Logout successful');
    }
    
    /**
     * Verify token
     */
    public function verifyToken() {
        $token = JWT::getTokenFromHeader();
        
        if (!$token) {
            sendError('No token provided', 401);
        }
        
        $payload = JWT::decode($token);
        
        if (!$payload) {
            sendError('Invalid token', 401);
        }
        
        // Verify user still exists and is active
        $user = new User();
        if (!$user->findById($payload['user_id'])) {
            sendError('User not found', 401);
        }
        
        sendSuccess([
            'valid' => true,
            'user' => $user->getPublicData()
        ], 'Token is valid');
    }
    
    /**
     * Refresh token
     */
    public function refreshToken() {
        $data = getRequestData();
        
        if (!isset($data['refresh_token'])) {
            sendError('Refresh token required', 400);
        }
        
        $refreshToken = $data['refresh_token'];
        
        // Verify refresh token
        if (!JWT::validateTokenType($refreshToken, 'refresh_token')) {
            sendError('Invalid refresh token', 401);
        }
        
        $payload = JWT::decode($refreshToken);
        
        if (!$payload) {
            sendError('Invalid refresh token', 401);
        }
        
        // Verify user still exists and is active
        $user = new User();
        if (!$user->findById($payload['user_id'])) {
            sendError('User not found', 401);
        }
        
        // Generate new access token
        $newToken = JWT::createUserToken($user->id, [
            'email' => $user->email,
            'role' => $user->role
        ]);
        
        sendSuccess([
            'token' => $newToken,
            'user' => $user->getPublicData()
        ], 'Token refreshed successfully');
    }
    
    /**
     * Reset password with OTP verification
     */
    public function resetPassword() {
        $data = getRequestData();

        // Validate required fields
        $required = ['email', 'otp', 'new_password'];
        $missing = validateRequired($data, $required);

        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }

        $email = sanitizeInput($data['email']);
        $otp = sanitizeInput($data['otp']);
        $newPassword = $data['new_password'];

        // Validate input
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            sendError('Invalid email format', 400);
        }

        if (!preg_match('/^\d{6}$/', $otp)) {
            sendError('Invalid OTP format', 400);
        }

        if (strlen($newPassword) < 6) {
            sendError('Password must be at least 6 characters long', 400);
        }

        try {
            $db = new Database();
            $conn = $db->getConnection();

            // Verify OTP
            $sql = "SELECT id FROM otps WHERE email = ? AND otp = ? AND type = 'forgot_password' AND expires_at > NOW() AND verified = 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email, $otp]);

            if (!$otpRow = $stmt->fetch()) {
                sendError('Invalid or unverified OTP. Please verify OTP first.', 400);
            }

            // Find user
            $user = new User();
            if (!$user->findByEmail($email)) {
                sendError('User not found', 404);
            }

            // Update password
            if ($user->updatePassword($newPassword)) {
                // Delete used OTP
                $sql = "DELETE FROM otps WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$otpRow['id']]);

                // Log activity
                logActivity($user->id, 'password_reset_completed', ['email' => $email]);

                sendSuccess(null, 'Password reset successful');
            } else {
                sendError('Failed to reset password', 500);
            }

        } catch (Exception $e) {
            error_log("Reset password error: " . $e->getMessage());
            sendError('Failed to reset password', 500);
        }
    }

    /**
     * Check if account is locked due to failed login attempts
     */
    private function isAccountLocked($email) {
        try {
            $db = new Database();
            $conn = $db->getConnection();

            $lockoutTime = 15 * 60; // 15 minutes
            $maxAttempts = 5;

            $sql = "SELECT COUNT(*) FROM failed_login_attempts
                    WHERE email = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email, $lockoutTime]);

            $attempts = (int)$stmt->fetchColumn();
            return $attempts >= $maxAttempts;

        } catch (Exception $e) {
            error_log("Account lock check error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Record failed login attempt
     */
    private function recordFailedLogin($email) {
        try {
            $db = new Database();
            $conn = $db->getConnection();

            $sql = "INSERT INTO failed_login_attempts (email, ip_address, user_agent)
                    VALUES (?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $email,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);

        } catch (Exception $e) {
            error_log("Failed login recording error: " . $e->getMessage());
        }
    }

    /**
     * Check if account should be locked
     */
    private function shouldLockAccount($email) {
        try {
            $db = new Database();
            $conn = $db->getConnection();

            $lockoutTime = 15 * 60; // 15 minutes
            $maxAttempts = 5;

            $sql = "SELECT COUNT(*) FROM failed_login_attempts
                    WHERE email = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email, $lockoutTime]);

            $attempts = (int)$stmt->fetchColumn();
            return $attempts >= $maxAttempts;

        } catch (Exception $e) {
            error_log("Account lock check error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear failed login attempts for successful login
     */
    private function clearFailedLogins($email) {
        try {
            $db = new Database();
            $conn = $db->getConnection();

            $sql = "DELETE FROM failed_login_attempts WHERE email = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email]);

        } catch (Exception $e) {
            error_log("Clear failed logins error: " . $e->getMessage());
        }
    }

    /**
     * Enhanced logout with token blacklisting
     */
    public function logoutSecure() {
        try {
            $token = JWT::getTokenFromHeader();

            if ($token) {
                // Add token to blacklist
                blacklistToken($token);

                // Get user for logging
                $payload = JWT::decode($token);
                if ($payload && isset($payload['user_id'])) {
                    logActivity($payload['user_id'], 'user_logout', [
                        'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
                        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
                    ]);
                }
            }

            sendSuccess(null, 'Logged out successfully');

        } catch (Exception $e) {
            error_log("Logout error: " . $e->getMessage());
            sendError('Logout failed', 500);
        }
    }

}
?>
