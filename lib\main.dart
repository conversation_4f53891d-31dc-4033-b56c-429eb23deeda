import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:firebase_core/firebase_core.dart';


import 'core/router/app_router.dart';
import 'core/providers/auth_provider.dart';
import 'core/providers/chit_provider.dart';
import 'core/providers/bidding_provider.dart';
import 'core/providers/member_provider.dart';
import 'core/providers/notification_provider.dart';
import 'core/providers/payment_provider.dart';
import 'core/providers/activity_provider.dart';
import 'core/providers/theme_provider.dart';

import 'core/services/storage_service.dart';
import 'core/services/api_service.dart';
import 'core/services/auth_service.dart';
import 'core/services/chit_service.dart';
import 'core/services/bidding_service.dart';
import 'core/services/member_service.dart';
import 'core/services/payment_service.dart';
import 'core/services/user_service.dart';
import 'core/services/activity_service.dart';
import 'core/services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Hive for local storage
    await Hive.initFlutter();
    await StorageService.init();

    // Initialize Firebase
    await Firebase.initializeApp();

    // Initialize API services
    ApiService.initialize();
    AuthService.initialize();
    ChitService.initialize();
    BiddingService.initialize();
    MemberService.initialize();
    PaymentService.initialize();
    UserService.initialize();
    ActivityService.initialize();
    await NotificationService.initialize();

    runApp(const ChitFundApp());
  } catch (e) {
    debugPrint('Initialization failed: $e');
    // Run app anyway with minimal functionality
    runApp(const ChitFundApp());
  }
}

class ChitFundApp extends StatelessWidget {
  const ChitFundApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => ChitProvider()),
        ChangeNotifierProvider(create: (_) => BiddingProvider()),
        ChangeNotifierProvider(create: (_) => MemberProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => PaymentProvider()),
        ChangeNotifierProvider(create: (_) => ActivityProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: Consumer2<AuthProvider, ThemeProvider>(
        builder: (context, authProvider, themeProvider, _) {
          // Initialize theme on first build
          if (!themeProvider.isInitialized) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              themeProvider.initializeTheme();
            });
          }

          return MaterialApp.router(
            title: 'Chit Fund Manager',
            theme: ThemeProvider.lightTheme,
            darkTheme: ThemeProvider.darkTheme,
            themeMode: themeProvider.materialThemeMode,
            routerConfig: AppRouter.router,
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
