import 'package:flutter/material.dart';
import '../../../core/models/payment_model.dart';

class PaymentFilterChips extends StatelessWidget {
  final PaymentStatus? selectedStatus;
  final PaymentType? selectedType;
  final Function(PaymentStatus?) onStatusChanged;
  final Function(PaymentType?) onTypeChanged;

  const PaymentFilterChips({
    super.key,
    this.selectedStatus,
    this.selectedType,
    required this.onStatusChanged,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Status Filter
        Text(
          'Status',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            FilterChip(
              label: const Text('All'),
              selected: selectedStatus == null,
              onSelected: (selected) {
                onStatusChanged(null);
              },
            ),
            ...PaymentStatus.values.map((status) => FilterChip(
              label: Text(status.displayName),
              selected: selectedStatus == status,
              selectedColor: status.color.withValues(alpha: 0.2),
              onSelected: (selected) {
                onStatusChanged(selected ? status : null);
              },
            )),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Type Filter
        Text(
          'Payment Type',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            FilterChip(
              label: const Text('All'),
              selected: selectedType == null,
              onSelected: (selected) {
                onTypeChanged(null);
              },
            ),
            ...PaymentType.values.map((type) => FilterChip(
              label: Text(type.displayName),
              selected: selectedType == type,
              onSelected: (selected) {
                onTypeChanged(selected ? type : null);
              },
            )),
          ],
        ),
      ],
    );
  }
}

// Quick filter buttons for common filters
class QuickPaymentFilters extends StatelessWidget {
  final Function(PaymentStatus?) onStatusFilter;
  final Function(PaymentType?) onTypeFilter;

  const QuickPaymentFilters({
    super.key,
    required this.onStatusFilter,
    required this.onTypeFilter,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Filters',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          
          // Quick action buttons
          Row(
            children: [
              Expanded(
                child: _buildQuickFilterButton(
                  context,
                  'Overdue',
                  Icons.warning,
                  Colors.red,
                  () => onStatusFilter(PaymentStatus.overdue),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickFilterButton(
                  context,
                  'Due Today',
                  Icons.today,
                  Colors.orange,
                  () => _filterDueToday(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickFilterButton(
                  context,
                  'Paid',
                  Icons.check_circle,
                  Colors.green,
                  () => onStatusFilter(PaymentStatus.paid),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildQuickFilterButton(
                  context,
                  'Contributions',
                  Icons.account_balance_wallet,
                  Colors.blue,
                  () => onTypeFilter(PaymentType.contribution),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickFilterButton(
                  context,
                  'Winnings',
                  Icons.emoji_events,
                  Colors.green,
                  () => onTypeFilter(PaymentType.winningAmount),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickFilterButton(
                  context,
                  'Clear All',
                  Icons.clear,
                  Colors.grey,
                  () {
                    onStatusFilter(null);
                    onTypeFilter(null);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilterButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16, color: color),
      label: Text(
        label,
        style: TextStyle(color: color, fontSize: 12),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
    );
  }

  void _filterDueToday() {
    // This would need to be implemented with a custom filter
    // For now, just filter pending payments
    onStatusFilter(PaymentStatus.pending);
  }
}

// Advanced filter dialog
class AdvancedPaymentFilters extends StatefulWidget {
  final PaymentStatus? selectedStatus;
  final PaymentType? selectedType;
  final PaymentMethod? selectedMethod;
  final DateTimeRange? selectedDateRange;
  final double? minAmount;
  final double? maxAmount;
  final Function(Map<String, dynamic>) onFiltersChanged;

  const AdvancedPaymentFilters({
    super.key,
    this.selectedStatus,
    this.selectedType,
    this.selectedMethod,
    this.selectedDateRange,
    this.minAmount,
    this.maxAmount,
    required this.onFiltersChanged,
  });

  @override
  State<AdvancedPaymentFilters> createState() => _AdvancedPaymentFiltersState();
}

class _AdvancedPaymentFiltersState extends State<AdvancedPaymentFilters> {
  PaymentStatus? _status;
  PaymentType? _type;
  PaymentMethod? _method;
  DateTimeRange? _dateRange;
  final _minAmountController = TextEditingController();
  final _maxAmountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _status = widget.selectedStatus;
    _type = widget.selectedType;
    _method = widget.selectedMethod;
    _dateRange = widget.selectedDateRange;
    _minAmountController.text = widget.minAmount?.toString() ?? '';
    _maxAmountController.text = widget.maxAmount?.toString() ?? '';
  }

  @override
  void dispose() {
    _minAmountController.dispose();
    _maxAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Advanced Filters'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status
            _buildDropdownField(
              'Status',
              _status?.displayName ?? 'All',
              () => _showStatusPicker(),
            ),
            
            const SizedBox(height: 16),
            
            // Type
            _buildDropdownField(
              'Payment Type',
              _type?.displayName ?? 'All',
              () => _showTypePicker(),
            ),
            
            const SizedBox(height: 16),
            
            // Method
            _buildDropdownField(
              'Payment Method',
              _method?.displayName ?? 'All',
              () => _showMethodPicker(),
            ),
            
            const SizedBox(height: 16),
            
            // Date Range
            _buildDropdownField(
              'Date Range',
              _dateRange != null 
                  ? '${_formatDate(_dateRange!.start)} - ${_formatDate(_dateRange!.end)}'
                  : 'All dates',
              () => _showDateRangePicker(),
            ),
            
            const SizedBox(height: 16),
            
            // Amount Range
            Text(
              'Amount Range',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _minAmountController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Min Amount',
                      prefixText: '₹',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _maxAmountController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Max Amount',
                      prefixText: '₹',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            _clearAllFilters();
          },
          child: const Text('Clear All'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            _applyFilters();
            Navigator.of(context).pop();
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }

  Widget _buildDropdownField(String label, String value, VoidCallback onTap) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        InkWell(
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(value),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showStatusPicker() {
    showDialog(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('Select Status'),
        children: [
          SimpleDialogOption(
            onPressed: () {
              setState(() {
                _status = null;
              });
              Navigator.of(context).pop();
            },
            child: const Text('All'),
          ),
          ...PaymentStatus.values.map((status) => SimpleDialogOption(
            onPressed: () {
              setState(() {
                _status = status;
              });
              Navigator.of(context).pop();
            },
            child: Text(status.displayName),
          )),
        ],
      ),
    );
  }

  void _showTypePicker() {
    showDialog(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('Select Payment Type'),
        children: [
          SimpleDialogOption(
            onPressed: () {
              setState(() {
                _type = null;
              });
              Navigator.of(context).pop();
            },
            child: const Text('All'),
          ),
          ...PaymentType.values.map((type) => SimpleDialogOption(
            onPressed: () {
              setState(() {
                _type = type;
              });
              Navigator.of(context).pop();
            },
            child: Text(type.displayName),
          )),
        ],
      ),
    );
  }

  void _showMethodPicker() {
    showDialog(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('Select Payment Method'),
        children: [
          SimpleDialogOption(
            onPressed: () {
              setState(() {
                _method = null;
              });
              Navigator.of(context).pop();
            },
            child: const Text('All'),
          ),
          ...PaymentMethod.values.map((method) => SimpleDialogOption(
            onPressed: () {
              setState(() {
                _method = method;
              });
              Navigator.of(context).pop();
            },
            child: Text(method.displayName),
          )),
        ],
      ),
    );
  }

  void _showDateRangePicker() async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _dateRange,
    );
    
    if (dateRange != null) {
      setState(() {
        _dateRange = dateRange;
      });
    }
  }

  void _clearAllFilters() {
    setState(() {
      _status = null;
      _type = null;
      _method = null;
      _dateRange = null;
      _minAmountController.clear();
      _maxAmountController.clear();
    });
  }

  void _applyFilters() {
    final filters = <String, dynamic>{
      'status': _status,
      'type': _type,
      'method': _method,
      'dateRange': _dateRange,
      'minAmount': double.tryParse(_minAmountController.text),
      'maxAmount': double.tryParse(_maxAmountController.text),
    };
    
    widget.onFiltersChanged(filters);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
