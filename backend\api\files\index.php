<?php
/**
 * Files API Routes
 */

require_once __DIR__ . '/../../controllers/FileController.php';

$fileController = new FileController();

// Get remaining path segments
$segments = array_slice(explode('/', trim($_SERVER['REQUEST_URI'], '/')), 2);
$action = $segments[0] ?? '';

switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        switch ($action) {
            case 'upload-profile-image':
                $fileController->uploadProfileImage();
                break;
                
            case 'upload-payment-proof':
                $fileController->uploadPaymentProof();
                break;
                
            default:
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Endpoint not found']);
                break;
        }
        break;
        
    case 'GET':
        switch ($action) {
            case 'user-files':
                $fileController->getUserFiles();
                break;
                
            default:
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Endpoint not found']);
                break;
        }
        break;
        
    case 'DELETE':
        $fileController->deleteFile();
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}
?>
