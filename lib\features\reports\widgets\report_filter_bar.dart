import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';

class ReportFilterBar extends StatelessWidget {
  final DateTimeRange? selectedDateRange;
  final String? selectedChitId;
  final Function(DateTimeRange?) onDateRangeChanged;
  final Function(String?) onChitChanged;

  const ReportFilterBar({
    super.key,
    this.selectedDateRange,
    this.selectedChitId,
    required this.onDateRangeChanged,
    required this.onChitChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Quick date filters
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildQuickDateFilter(context, 'This Month', _getThisMonth()),
                const SizedBox(width: 8),
                _buildQuickDateFilter(context, 'Last Month', _getLastMonth()),
                const SizedBox(width: 8),
                _buildQuickDateFilter(context, 'This Quarter', _getThisQuarter()),
                const SizedBox(width: 8),
                _buildQuickDateFilter(context, 'This Year', _getThisYear()),
                const SizedBox(width: 8),
                _buildCustomDateFilter(context),
              ],
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Chit filter and clear button
          Row(
            children: [
              Expanded(
                child: _buildChitFilter(context),
              ),
              const SizedBox(width: 12),
              if (selectedDateRange != null || selectedChitId != null)
                OutlinedButton.icon(
                  onPressed: () {
                    onDateRangeChanged(null);
                    onChitChanged(null);
                  },
                  icon: const Icon(Icons.clear, size: 16),
                  label: const Text('Clear'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickDateFilter(BuildContext context, String label, DateTimeRange dateRange) {
    final isSelected = selectedDateRange != null &&
        selectedDateRange!.start.isAtSameMomentAs(dateRange.start) &&
        selectedDateRange!.end.isAtSameMomentAs(dateRange.end);

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        onDateRangeChanged(selected ? dateRange : null);
      },
      selectedColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
    );
  }

  Widget _buildCustomDateFilter(BuildContext context) {
    final hasCustomRange = selectedDateRange != null && !_isQuickDateRange(selectedDateRange!);

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.date_range, size: 16),
          const SizedBox(width: 4),
          Text(hasCustomRange ? 'Custom' : 'Pick Dates'),
        ],
      ),
      selected: hasCustomRange,
      onSelected: (selected) {
        _showDateRangePicker(context);
      },
    );
  }

  Widget _buildChitFilter(BuildContext context) {
    return Consumer<ChitProvider>(
      builder: (context, chitProvider, child) {
        final chits = chitProvider.chits;
        
        return DropdownButtonFormField<String>(
          value: selectedChitId,
          decoration: const InputDecoration(
            labelText: 'Filter by Chit',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('All Chits'),
            ),
            ...chits.map((chit) => DropdownMenuItem<String>(
              value: chit.id,
              child: Text(chit.name),
            )),
          ],
          onChanged: onChitChanged,
        );
      },
    );
  }

  void _showDateRangePicker(BuildContext context) async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: selectedDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (dateRange != null) {
      onDateRangeChanged(dateRange);
    }
  }

  DateTimeRange _getThisMonth() {
    final now = DateTime.now();
    final start = DateTime(now.year, now.month, 1);
    final end = DateTime(now.year, now.month + 1, 0);
    return DateTimeRange(start: start, end: end);
  }

  DateTimeRange _getLastMonth() {
    final now = DateTime.now();
    final start = DateTime(now.year, now.month - 1, 1);
    final end = DateTime(now.year, now.month, 0);
    return DateTimeRange(start: start, end: end);
  }

  DateTimeRange _getThisQuarter() {
    final now = DateTime.now();
    final quarterStart = ((now.month - 1) ~/ 3) * 3 + 1;
    final start = DateTime(now.year, quarterStart, 1);
    final end = DateTime(now.year, quarterStart + 3, 0);
    return DateTimeRange(start: start, end: end);
  }

  DateTimeRange _getThisYear() {
    final now = DateTime.now();
    final start = DateTime(now.year, 1, 1);
    final end = DateTime(now.year, 12, 31);
    return DateTimeRange(start: start, end: end);
  }

  bool _isQuickDateRange(DateTimeRange range) {
    final thisMonth = _getThisMonth();
    final lastMonth = _getLastMonth();
    final thisQuarter = _getThisQuarter();
    final thisYear = _getThisYear();

    return _isSameDateRange(range, thisMonth) ||
           _isSameDateRange(range, lastMonth) ||
           _isSameDateRange(range, thisQuarter) ||
           _isSameDateRange(range, thisYear);
  }

  bool _isSameDateRange(DateTimeRange range1, DateTimeRange range2) {
    return range1.start.isAtSameMomentAs(range2.start) &&
           range1.end.isAtSameMomentAs(range2.end);
  }
}

// Advanced filter dialog
class AdvancedReportFilters extends StatefulWidget {
  final DateTimeRange? selectedDateRange;
  final String? selectedChitId;
  final List<String>? selectedStatuses;
  final double? minAmount;
  final double? maxAmount;
  final Function(Map<String, dynamic>) onFiltersChanged;

  const AdvancedReportFilters({
    super.key,
    this.selectedDateRange,
    this.selectedChitId,
    this.selectedStatuses,
    this.minAmount,
    this.maxAmount,
    required this.onFiltersChanged,
  });

  @override
  State<AdvancedReportFilters> createState() => _AdvancedReportFiltersState();
}

class _AdvancedReportFiltersState extends State<AdvancedReportFilters> {
  DateTimeRange? _dateRange;
  String? _chitId;
  List<String> _statuses = [];
  final _minAmountController = TextEditingController();
  final _maxAmountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _dateRange = widget.selectedDateRange;
    _chitId = widget.selectedChitId;
    _statuses = widget.selectedStatuses ?? [];
    _minAmountController.text = widget.minAmount?.toString() ?? '';
    _maxAmountController.text = widget.maxAmount?.toString() ?? '';
  }

  @override
  void dispose() {
    _minAmountController.dispose();
    _maxAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Advanced Filters'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date Range
            _buildSectionTitle('Date Range'),
            const SizedBox(height: 8),
            InkWell(
              onTap: _showDateRangePicker,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Theme.of(context).colorScheme.outline),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _dateRange != null
                      ? '${_formatDate(_dateRange!.start)} - ${_formatDate(_dateRange!.end)}'
                      : 'Select date range',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Chit Selection
            _buildSectionTitle('Chit Fund'),
            const SizedBox(height: 8),
            Consumer<ChitProvider>(
              builder: (context, chitProvider, child) {
                return DropdownButtonFormField<String>(
                  value: _chitId,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('All Chits'),
                    ),
                    ...chitProvider.chits.map((chit) => DropdownMenuItem<String>(
                      value: chit.id,
                      child: Text(chit.name),
                    )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _chitId = value;
                    });
                  },
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // Status Filters
            _buildSectionTitle('Status'),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: ['Active', 'Completed', 'Draft', 'Cancelled'].map((status) {
                return FilterChip(
                  label: Text(status),
                  selected: _statuses.contains(status),
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _statuses.add(status);
                      } else {
                        _statuses.remove(status);
                      }
                    });
                  },
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Amount Range
            _buildSectionTitle('Amount Range'),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _minAmountController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Min Amount',
                      prefixText: '₹',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _maxAmountController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Max Amount',
                      prefixText: '₹',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            _clearAllFilters();
          },
          child: const Text('Clear All'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            _applyFilters();
            Navigator.of(context).pop();
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  void _showDateRangePicker() async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
    );

    if (dateRange != null) {
      setState(() {
        _dateRange = dateRange;
      });
    }
  }

  void _clearAllFilters() {
    setState(() {
      _dateRange = null;
      _chitId = null;
      _statuses.clear();
      _minAmountController.clear();
      _maxAmountController.clear();
    });
  }

  void _applyFilters() {
    final filters = <String, dynamic>{
      'dateRange': _dateRange,
      'chitId': _chitId,
      'statuses': _statuses,
      'minAmount': double.tryParse(_minAmountController.text),
      'maxAmount': double.tryParse(_maxAmountController.text),
    };

    widget.onFiltersChanged(filters);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
