import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/payment_provider.dart';
import '../../../core/models/payment_model.dart';

class PaymentTrendsChart extends StatelessWidget {
  final DateTimeRange? dateRange;
  final String? chitId;

  const PaymentTrendsChart({
    super.key,
    this.dateRange,
    this.chitId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<PaymentProvider>(
      builder: (context, paymentProvider, child) {
        final trendsData = _calculateTrendsData(paymentProvider);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Payment Trends',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    DropdownButton<String>(
                      value: 'monthly',
                      items: const [
                        DropdownMenuItem(value: 'daily', child: Text('Daily')),
                        DropdownMenuItem(value: 'weekly', child: Text('Weekly')),
                        DropdownMenuItem(value: 'monthly', child: Text('Monthly')),
                      ],
                      onChanged: (value) {
                        // Handle period change
                      },
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Chart area
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                  child: _buildTrendsChart(context, trendsData),
                ),
                
                const SizedBox(height: 16),
                
                // Trends summary
                _buildTrendsSummary(context, trendsData),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTrendsChart(BuildContext context, Map<String, dynamic> data) {
    final trends = data['trends'] as List<Map<String, dynamic>>;
    
    if (trends.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No payment trends data available',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    final maxAmount = trends.map((t) => t['amount'] as double).reduce((a, b) => a > b ? a : b);
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Chart legend
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildLegendItem(context, 'Received', Colors.green),
              _buildLegendItem(context, 'Pending', Colors.orange),
              _buildLegendItem(context, 'Overdue', Colors.red),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Chart bars
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: trends.map((trend) {
                final heightFactor = (trend['amount'] as double) / maxAmount;
                
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Amount label
                        Text(
                          '₹${_formatAmount(trend['amount'])}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 10,
                          ),
                        ),
                        
                        const SizedBox(height: 4),
                        
                        // Bar
                        Container(
                          width: double.infinity,
                          height: 80 * heightFactor,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              colors: [
                                _getTrendColor(trend['status']),
                                _getTrendColor(trend['status']).withValues(alpha: 0.7),
                              ],
                            ),
                            borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
                          ),
                        ),
                        
                        const SizedBox(height: 4),
                        
                        // Period label
                        Text(
                          trend['period'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(BuildContext context, String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildTrendsSummary(BuildContext context, Map<String, dynamic> data) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryItem(
            context,
            'Total Collected',
            '₹${_formatAmount(data['totalCollected'])}',
            Icons.account_balance_wallet,
            Colors.green,
          ),
        ),
        Expanded(
          child: _buildSummaryItem(
            context,
            'Growth Rate',
            '${data['growthRate'].toStringAsFixed(1)}%',
            Icons.trending_up,
            data['growthRate'] >= 0 ? Colors.green : Colors.red,
          ),
        ),
        Expanded(
          child: _buildSummaryItem(
            context,
            'Avg. Monthly',
            '₹${_formatAmount(data['avgMonthly'])}',
            Icons.calendar_month,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Map<String, dynamic> _calculateTrendsData(PaymentProvider paymentProvider) {
    final payments = paymentProvider.payments;
    
    // Filter payments based on criteria
    final relevantPayments = chitId != null
        ? payments.where((p) => p.chitId == chitId)
        : payments;

    // Group payments by month (simplified)
    final monthlyData = <String, Map<String, double>>{};
    final now = DateTime.now();
    
    // Generate last 6 months of data
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.month.toString().padLeft(2, '0')}/${month.year.toString().substring(2)}';
      
      final monthPayments = relevantPayments.where((p) =>
          p.createdAt.year == month.year && p.createdAt.month == month.month);
      
      final received = monthPayments
          .where((p) => p.paymentStatus == PaymentStatus.paid)
          .fold(0.0, (sum, p) => sum + (p.amount ?? 0.0));

      final pending = monthPayments
          .where((p) => p.paymentStatus == PaymentStatus.pending)
          .fold(0.0, (sum, p) => sum + (p.amount ?? 0.0));

      final overdue = monthPayments
          .where((p) => p.paymentStatus == PaymentStatus.overdue)
          .fold(0.0, (sum, p) => sum + (p.amount ?? 0.0));
      
      monthlyData[monthKey] = {
        'received': received,
        'pending': pending,
        'overdue': overdue,
        'total': received + pending + overdue,
      };
    }

    // Convert to trends list
    final trends = monthlyData.entries.map((entry) {
      final total = entry.value['total']!;
      String status = 'received';
      
      if (entry.value['overdue']! > entry.value['received']!) {
        status = 'overdue';
      } else if (entry.value['pending']! > entry.value['received']!) {
        status = 'pending';
      }
      
      return {
        'period': entry.key,
        'amount': total,
        'status': status,
        'received': entry.value['received']!,
        'pending': entry.value['pending']!,
        'overdue': entry.value['overdue']!,
      };
    }).toList();

    // Calculate summary metrics
    final totalCollected = trends.fold(0.0, (sum, t) => sum + (t['received'] as double));
    
    final avgMonthly = trends.isNotEmpty 
        ? totalCollected / trends.length 
        : 0.0;
    
    // Calculate growth rate (comparing first and last month)
    double growthRate = 0.0;
    if (trends.length >= 2) {
      final firstMonth = trends.first['received'] as double;
      final lastMonth = trends.last['received'] as double;
      if (firstMonth > 0) {
        growthRate = ((lastMonth - firstMonth) / firstMonth) * 100;
      }
    }

    return {
      'trends': trends,
      'totalCollected': totalCollected,
      'avgMonthly': avgMonthly,
      'growthRate': growthRate,
    };
  }

  Color _getTrendColor(String status) {
    switch (status) {
      case 'received':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'overdue':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
