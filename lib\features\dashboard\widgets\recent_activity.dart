import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/router/app_router.dart';
import '../../../core/providers/activity_provider.dart';
import '../../../core/models/activity_log.dart';

class RecentActivity extends StatefulWidget {
  const RecentActivity({super.key});

  @override
  State<RecentActivity> createState() => _RecentActivityState();
}

class _RecentActivityState extends State<RecentActivity> {
  @override
  void initState() {
    super.initState();
    // Load recent activities when widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ActivityProvider>().loadRecentActivities(limit: 5);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                AppNavigation.goToActivityLog(context);
              },
              child: const Text('View All'),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Activity List
        Consumer<ActivityProvider>(
          builder: (context, activityProvider, child) {
            if (activityProvider.isLoading) {
              return const Card(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              );
            }

            if (activityProvider.errorMessage != null) {
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Failed to load activities',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        activityProvider.errorMessage!,
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          activityProvider.loadRecentActivities(limit: 5);
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              );
            }

            final activities = activityProvider.recentActivities;

            if (activities.isEmpty) {
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.history,
                          size: 48,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'No recent activity',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }

            return Card(
              child: Column(
                children: activities.asMap().entries.map((entry) {
                  final index = entry.key;
                  final activity = entry.value;
                  final isLast = index == activities.length - 1;

                  return Column(
                    children: [
                      _buildActivityItem(
                        context,
                        activity: activity,
                        onTap: () {
                          // Navigate based on activity type
                          _handleActivityTap(context, activity);
                        },
                      ),
                      if (!isLast) const Divider(height: 1),
                    ],
                  );
                }).toList(),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildActivityItem(
    BuildContext context, {
    required ActivityLog activity,
    VoidCallback? onTap,
  }) {
    final iconData = _getActivityIcon(activity.action);
    final iconColor = _getActivityColor(activity.severity);
    final timeAgo = _getTimeAgo(activity.createdAt);
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: iconColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          iconData,
          color: iconColor,
          size: 20,
        ),
      ),
      title: Text(
        activity.formattedAction,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 2),
          if (activity.details != null && activity.details!.isNotEmpty)
            Text(
              _getActivitySubtitle(activity),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          const SizedBox(height: 4),
          Text(
            timeAgo,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
      trailing: onTap != null
          ? Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            )
          : null,
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  // Helper methods
  IconData _getActivityIcon(String action) {
    switch (action) {
      case 'chit_created':
        return Icons.add_circle;
      case 'payment_made':
      case 'payment_received':
        return Icons.payment;
      case 'member_joined':
        return Icons.group_add;
      case 'bidding_started':
      case 'bidding_completed':
        return Icons.gavel;
      case 'notification_sent':
        return Icons.notifications;
      case 'user_login':
        return Icons.login;
      case 'user_logout':
        return Icons.logout;
      default:
        return Icons.info;
    }
  }

  Color _getActivityColor(ActivitySeverity severity) {
    switch (severity) {
      case ActivitySeverity.success:
        return Colors.green;
      case ActivitySeverity.warning:
        return Colors.orange;
      case ActivitySeverity.error:
        return Colors.red;
      case ActivitySeverity.info:
        return Colors.blue;
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }

  String _getActivitySubtitle(ActivityLog activity) {
    final details = activity.details;
    if (details == null || details.isEmpty) return '';

    switch (activity.action) {
      case 'chit_created':
        return details['chit_name'] ?? 'New chit fund';
      case 'payment_received':
        final amount = details['amount'];
        final from = details['from_user'];
        return 'From ${from ?? 'member'} - ₹${amount ?? '0'}';
      case 'member_joined':
        final chitName = details['chit_name'];
        final memberName = details['member_name'];
        return '${memberName ?? 'Member'} joined ${chitName ?? 'chit'}';
      case 'bidding_completed':
        final round = details['round'];
        final winner = details['winner'];
        return 'Round ${round ?? '?'} - Winner: ${winner ?? 'Unknown'}';
      default:
        return details.toString();
    }
  }

  void _handleActivityTap(BuildContext context, ActivityLog activity) {
    // Navigate based on activity type
    switch (activity.action) {
      case 'chit_created':
      case 'member_joined':
      case 'bidding_started':
      case 'bidding_completed':
        if (activity.chitId != null) {
          AppNavigation.goToChitDetails(context, activity.chitId!.toString());
        }
        break;
      case 'payment_made':
      case 'payment_received':
        AppNavigation.goToPayments(context);
        break;
      case 'notification_sent':
        AppNavigation.goToNotifications(context);
        break;
      default:
        // For other activities, go to activity log
        AppNavigation.goToActivityLog(context);
        break;
    }
  }
}

// Alternative compact activity widget
class CompactRecentActivity extends StatelessWidget {
  const CompactRecentActivity({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activity',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Icon(
                  Icons.history,
                  size: 20,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildCompactActivityItem(
              context,
              '🎯 Bidding completed for Monthly Savings Chit',
              '2 hours ago',
            ),
            
            const SizedBox(height: 8),
            
            _buildCompactActivityItem(
              context,
              '💰 Payment received from Rajesh Kumar',
              '4 hours ago',
            ),
            
            const SizedBox(height: 8),
            
            _buildCompactActivityItem(
              context,
              '👥 New member joined Festival Chit',
              '1 day ago',
            ),
            
            const SizedBox(height: 12),
            
            Center(
              child: TextButton(
                onPressed: () {
                  AppNavigation.goToActivityLog(context);
                },
                child: const Text('View All Activity'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactActivityItem(BuildContext context, String text, String time) {
    return Row(
      children: [
        Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(3),
          ),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                text,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                time,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

// Activity timeline widget
class ActivityTimeline extends StatelessWidget {
  const ActivityTimeline({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Activity Timeline',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildTimelineItem(
                  context,
                  time: '10:30 AM',
                  title: 'Bidding Started',
                  subtitle: 'Monthly Savings Chit - Round 4',
                  icon: Icons.gavel,
                  iconColor: Colors.blue,
                  isFirst: true,
                ),
                
                _buildTimelineItem(
                  context,
                  time: '09:15 AM',
                  title: 'Payment Received',
                  subtitle: 'From Priya Sharma - ₹5,000',
                  icon: Icons.payment,
                  iconColor: Colors.green,
                ),
                
                _buildTimelineItem(
                  context,
                  time: '08:45 AM',
                  title: 'Reminder Sent',
                  subtitle: 'Payment due notification to 3 members',
                  icon: Icons.notifications,
                  iconColor: Colors.orange,
                ),
                
                _buildTimelineItem(
                  context,
                  time: 'Yesterday',
                  title: 'New Member Added',
                  subtitle: 'Vikram Singh joined Business Growth Chit',
                  icon: Icons.person_add,
                  iconColor: Colors.purple,
                  isLast: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineItem(
    BuildContext context, {
    required String time,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color iconColor,
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            if (!isFirst)
              Container(
                width: 2,
                height: 16,
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: iconColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 16,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 16,
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
          ],
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
