<?php
/**
 * Database Setup Script
 * 
 * This script sets up the database schema and optionally loads sample data.
 * Run this script once to initialize the database.
 */

require_once __DIR__ . '/../config/config.php';

class DatabaseSetup {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Run the complete database setup
     */
    public function setup($includeSampleData = false) {
        try {
            echo "Starting database setup...\n";
            
            // Test connection
            if (!$this->testConnection()) {
                throw new Exception("Database connection failed");
            }
            
            // Create schema
            $this->createSchema();
            
            // Load sample data if requested
            if ($includeSampleData) {
                $this->loadSampleData();
            }
            
            echo "Database setup completed successfully!\n";
            return true;
            
        } catch (Exception $e) {
            echo "Database setup failed: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Test database connection
     */
    private function testConnection() {
        echo "Testing database connection...\n";
        
        $conn = $this->db->getConnection();
        if (!$conn) {
            echo "Failed to connect to database\n";
            return false;
        }
        
        echo "Database connection successful\n";
        return true;
    }
    
    /**
     * Create database schema
     */
    private function createSchema() {
        echo "Creating database schema...\n";
        
        $schemaFile = __DIR__ . '/schema.sql';
        if (!file_exists($schemaFile)) {
            throw new Exception("Schema file not found: $schemaFile");
        }
        
        $sql = file_get_contents($schemaFile);
        if (!$sql) {
            throw new Exception("Failed to read schema file");
        }
        
        // Split SQL into individual statements
        $statements = $this->splitSqlStatements($sql);
        
        $conn = $this->db->getConnection();
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            try {
                $conn->exec($statement);
            } catch (PDOException $e) {
                // Ignore "database exists" and "table exists" errors
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
        
        echo "Database schema created successfully\n";
    }
    
    /**
     * Load sample data
     */
    private function loadSampleData() {
        echo "Loading sample data...\n";
        
        $sampleDataFile = __DIR__ . '/sample_data.sql';
        if (!file_exists($sampleDataFile)) {
            echo "Sample data file not found, skipping...\n";
            return;
        }
        
        $sql = file_get_contents($sampleDataFile);
        if (!$sql) {
            throw new Exception("Failed to read sample data file");
        }
        
        // Split SQL into individual statements
        $statements = $this->splitSqlStatements($sql);
        
        $conn = $this->db->getConnection();
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            try {
                $conn->exec($statement);
            } catch (PDOException $e) {
                // Log error but continue with other statements
                echo "Warning: " . $e->getMessage() . "\n";
            }
        }
        
        echo "Sample data loaded successfully\n";
    }
    
    /**
     * Split SQL file into individual statements
     */
    private function splitSqlStatements($sql) {
        // Remove comments
        $sql = preg_replace('/--.*$/m', '', $sql);
        
        // Split by semicolon (simple approach)
        $statements = explode(';', $sql);
        
        return array_filter($statements, function($stmt) {
            return !empty(trim($stmt));
        });
    }
    
    /**
     * Check if database exists
     */
    public function databaseExists() {
        try {
            $conn = $this->db->getConnection();
            return $conn !== null;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Check if tables exist
     */
    public function tablesExist() {
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->query("SHOW TABLES LIKE 'users'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Reset database (drop all tables)
     */
    public function reset() {
        echo "Resetting database...\n";
        
        try {
            $conn = $this->db->getConnection();
            
            // Get all tables
            $stmt = $conn->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Disable foreign key checks
            $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // Drop all tables
            foreach ($tables as $table) {
                $conn->exec("DROP TABLE IF EXISTS `$table`");
                echo "Dropped table: $table\n";
            }
            
            // Re-enable foreign key checks
            $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            echo "Database reset completed\n";
            return true;
            
        } catch (Exception $e) {
            echo "Database reset failed: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Get database status
     */
    public function getStatus() {
        $status = [
            'connection' => $this->testConnection(),
            'database_exists' => $this->databaseExists(),
            'tables_exist' => $this->tablesExist()
        ];
        
        if ($status['tables_exist']) {
            try {
                $conn = $this->db->getConnection();
                
                // Count records in main tables
                $tables = ['users', 'chits', 'chit_members', 'bidding_rounds', 'payments'];
                foreach ($tables as $table) {
                    $stmt = $conn->query("SELECT COUNT(*) FROM `$table`");
                    $status['table_counts'][$table] = $stmt->fetchColumn();
                }
            } catch (Exception $e) {
                $status['error'] = $e->getMessage();
            }
        }
        
        return $status;
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    $setup = new DatabaseSetup();
    
    $command = $argv[1] ?? 'setup';
    
    switch ($command) {
        case 'setup':
            $includeSampleData = isset($argv[2]) && $argv[2] === '--with-sample-data';
            $setup->setup($includeSampleData);
            break;
            
        case 'reset':
            $setup->reset();
            break;
            
        case 'status':
            $status = $setup->getStatus();
            echo "Database Status:\n";
            echo json_encode($status, JSON_PRETTY_PRINT) . "\n";
            break;
            
        default:
            echo "Usage: php setup.php [setup|reset|status] [--with-sample-data]\n";
            echo "  setup                 - Create database schema\n";
            echo "  setup --with-sample-data - Create schema and load sample data\n";
            echo "  reset                 - Drop all tables\n";
            echo "  status                - Show database status\n";
            break;
    }
}
?>
