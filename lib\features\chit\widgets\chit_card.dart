import 'package:flutter/material.dart';
import '../../../core/models/chit_model.dart';

class ChitCard extends StatelessWidget {
  final Chit chit;
  final VoidCallback? onTap;
  final bool showActions;

  const ChitCard({
    super.key,
    required this.chit,
    this.onTap,
    this.showActions = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          chit.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          chit.description,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Status Badge
                  _buildStatusBadge(context),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Amount and Members Row
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Total Amount',
                      '₹${_formatAmount(chit.totalAmount)}',
                      Icons.account_balance_wallet,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Members',
                      '${chit.numberOfMembers}',
                      Icons.group,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Duration and Frequency Row
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Duration',
                      '${chit.durationMonths} months',
                      Icons.schedule,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Frequency',
                      chit.frequency.displayName,
                      Icons.repeat,
                    ),
                  ),
                ],
              ),
              
              if (chit.status == ChitStatus.active) ...[
                const SizedBox(height: 16),
                _buildProgressIndicator(context),
              ],
              
              if (showActions) ...[
                const SizedBox(height: 16),
                _buildActionButtons(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (chit.status) {
      case ChitStatus.active:
        backgroundColor = Colors.green;
        textColor = Colors.white;
        text = 'Active';
        break;
      case ChitStatus.completed:
        backgroundColor = Colors.blue;
        textColor = Colors.white;
        text = 'Completed';
        break;
      case ChitStatus.draft:
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        text = 'Draft';
        break;
      case ChitStatus.cancelled:
        backgroundColor = Colors.red;
        textColor = Colors.white;
        text = 'Cancelled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    // Calculate progress based on current round vs total rounds
    final progress = chit.currentRound / chit.numberOfMembers;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            Text(
              'Round ${chit.currentRound}/${chit.numberOfMembers}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              // Navigate to members
            },
            icon: const Icon(Icons.group, size: 16),
            label: const Text('Members'),
          ),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              // Navigate to bidding or payments
            },
            icon: const Icon(Icons.payment, size: 16),
            label: const Text('Payments'),
          ),
        ),
      ],
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}

// Compact version of ChitCard for lists
class CompactChitCard extends StatelessWidget {
  final Chit chit;
  final VoidCallback? onTap;

  const CompactChitCard({
    super.key,
    required this.chit,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        onTap: onTap,
        leading: CircleAvatar(
          backgroundColor: _getStatusColor().withValues(alpha: 0.1),
          child: Icon(
            _getStatusIcon(),
            color: _getStatusColor(),
            size: 20,
          ),
        ),
        title: Text(
          chit.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          '₹${_formatAmount(chit.totalAmount)} • ${chit.numberOfMembers} members',
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor(),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                chit.status.displayName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (chit.status == ChitStatus.active)
              Text(
                'Round ${chit.currentRound}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (chit.status) {
      case ChitStatus.active:
        return Colors.green;
      case ChitStatus.completed:
        return Colors.blue;
      case ChitStatus.draft:
        return Colors.orange;
      case ChitStatus.cancelled:
        return Colors.red;
    }
  }

  IconData _getStatusIcon() {
    switch (chit.status) {
      case ChitStatus.active:
        return Icons.play_circle_filled;
      case ChitStatus.completed:
        return Icons.check_circle;
      case ChitStatus.draft:
        return Icons.drafts;
      case ChitStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
