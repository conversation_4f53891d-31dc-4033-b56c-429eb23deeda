import 'package:flutter/foundation.dart';

/// Debug Logger for development and testing
/// This class provides structured logging for debugging purposes
class DebugLogger {
  static const String _tag = 'ChitFund';
  
  /// Log info message
  static void logInfo(String component, String message) {
    if (kDebugMode) {
      debugPrint('[$_tag][$component] ℹ️ $message');
    }
  }
  
  /// Log success message
  static void logSuccess(String component, String message) {
    if (kDebugMode) {
      debugPrint('[$_tag][$component] ✅ $message');
    }
  }
  
  /// Log warning message
  static void logWarning(String component, String message) {
    if (kDebugMode) {
      debugPrint('[$_tag][$component] ⚠️ $message');
    }
  }
  
  /// Log error message
  static void logError(String component, String message, [StackTrace? stackTrace]) {
    if (kDebugMode) {
      debugPrint('[$_tag][$component] ❌ $message');
      if (stackTrace != null) {
        debugPrint('[$_tag][$component] Stack trace: $stackTrace');
      }
    }
  }
  
  /// Log API request
  static void logApiRequest(String method, String url, Map<String, dynamic>? data) {
    if (kDebugMode) {
      debugPrint('[$_tag][API] 📤 $method $url');
      if (data != null) {
        debugPrint('[$_tag][API] Request data: $data');
      }
    }
  }
  
  /// Log API response
  static void logApiResponse(String url, int statusCode, dynamic data) {
    if (kDebugMode) {
      debugPrint('[$_tag][API] 📥 $url - Status: $statusCode');
      if (data != null) {
        debugPrint('[$_tag][API] Response data: $data');
      }
    }
  }
  
  /// Log API error
  static void logApiError(String url, String error, [dynamic exception]) {
    if (kDebugMode) {
      debugPrint('[$_tag][API] ❌ $url - Error: $error');
      if (exception != null) {
        debugPrint('[$_tag][API] Exception: $exception');
      }
    }
  }
  
  /// Log navigation
  static void logNavigation(String from, String to) {
    if (kDebugMode) {
      debugPrint('[$_tag][Navigation] 🧭 $from → $to');
    }
  }
  
  /// Log user action
  static void logUserAction(String action, [Map<String, dynamic>? details]) {
    if (kDebugMode) {
      debugPrint('[$_tag][User] 👤 $action');
      if (details != null) {
        debugPrint('[$_tag][User] Details: $details');
      }
    }
  }
  
  /// Log performance metric
  static void logPerformance(String operation, Duration duration) {
    if (kDebugMode) {
      debugPrint('[$_tag][Performance] ⏱️ $operation took ${duration.inMilliseconds}ms');
    }
  }
  
  /// Log memory usage (placeholder for future implementation)
  static void logMemoryUsage(String component) {
    if (kDebugMode) {
      debugPrint('[$_tag][Memory] 💾 $component memory check');
    }
  }
  
  /// Log network connectivity
  static void logNetworkStatus(bool isConnected) {
    if (kDebugMode) {
      debugPrint('[$_tag][Network] ${isConnected ? '🌐' : '📵'} Network ${isConnected ? 'connected' : 'disconnected'}');
    }
  }
  
  /// Log authentication events
  static void logAuth(String event, [String? userId]) {
    if (kDebugMode) {
      debugPrint('[$_tag][Auth] 🔐 $event${userId != null ? ' (User: $userId)' : ''}');
    }
  }
  
  /// Log data operations
  static void logData(String operation, String entity, [String? id]) {
    if (kDebugMode) {
      debugPrint('[$_tag][Data] 💾 $operation $entity${id != null ? ' (ID: $id)' : ''}');
    }
  }
  
  /// Log UI events
  static void logUI(String event, String component) {
    if (kDebugMode) {
      debugPrint('[$_tag][UI] 🎨 $event in $component');
    }
  }
  
  /// Log business logic events
  static void logBusiness(String event, [Map<String, dynamic>? context]) {
    if (kDebugMode) {
      debugPrint('[$_tag][Business] 💼 $event');
      if (context != null) {
        debugPrint('[$_tag][Business] Context: $context');
      }
    }
  }
  
  /// Log security events
  static void logSecurity(String event, [String? details]) {
    if (kDebugMode) {
      debugPrint('[$_tag][Security] 🔒 $event${details != null ? ' - $details' : ''}');
    }
  }
  
  /// Log configuration changes
  static void logConfig(String setting, dynamic oldValue, dynamic newValue) {
    if (kDebugMode) {
      debugPrint('[$_tag][Config] ⚙️ $setting: $oldValue → $newValue');
    }
  }
  
  /// Log feature usage
  static void logFeature(String feature, String action) {
    if (kDebugMode) {
      debugPrint('[$_tag][Feature] 🎯 $feature: $action');
    }
  }
  
  /// Log debug session start
  static void logSessionStart() {
    if (kDebugMode) {
      debugPrint('[$_tag][Session] 🚀 Debug session started');
      debugPrint('[$_tag][Session] Flutter version: ${kDebugMode ? 'Debug' : 'Release'}');
    }
  }
  
  /// Log debug session end
  static void logSessionEnd() {
    if (kDebugMode) {
      debugPrint('[$_tag][Session] 🏁 Debug session ended');
    }
  }
}
