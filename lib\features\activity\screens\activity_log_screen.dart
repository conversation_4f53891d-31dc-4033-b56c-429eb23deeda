import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/activity_provider.dart';
import '../../../core/models/activity_log.dart';

class ActivityLogScreen extends StatefulWidget {
  const ActivityLogScreen({super.key});

  @override
  State<ActivityLogScreen> createState() => _ActivityLogScreenState();
}

class _ActivityLogScreenState extends State<ActivityLogScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    // Load activities when screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ActivityProvider>().loadUserActivities();
    });

    // Setup infinite scroll
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreActivities();
    }
  }

  Future<void> _loadMoreActivities() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    final activityProvider = context.read<ActivityProvider>();
    final currentCount = activityProvider.userActivities.length;

    await activityProvider.loadUserActivities(
      offset: currentCount,
      append: true,
    );

    setState(() {
      _isLoadingMore = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Activity Log'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<ActivityProvider>().loadUserActivities();
            },
          ),
        ],
      ),
      body: Consumer<ActivityProvider>(
        builder: (context, activityProvider, child) {
          if (activityProvider.isLoading && activityProvider.userActivities.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (activityProvider.errorMessage != null && activityProvider.userActivities.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load activities',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    activityProvider.errorMessage!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      activityProvider.loadUserActivities();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final activities = activityProvider.userActivities;

          if (activities.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.history,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No activities yet',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your activities will appear here',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => activityProvider.loadUserActivities(),
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: activities.length + (_isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == activities.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final activity = activities[index];
                return _buildActivityCard(activity);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildActivityCard(ActivityLog activity) {
    final iconData = _getActivityIcon(activity.action);
    final iconColor = _getActivityColor(activity.severity);
    final timeAgo = _getTimeAgo(activity.createdAt);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            iconData,
            color: iconColor,
            size: 20,
          ),
        ),
        title: Text(
          activity.formattedAction,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            if (activity.details != null && activity.details!.isNotEmpty)
              Text(
                _getActivitySubtitle(activity),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            const SizedBox(height: 4),
            Text(
              timeAgo,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
        onTap: () {
          _showActivityDetails(activity);
        },
      ),
    );
  }

  void _showActivityDetails(ActivityLog activity) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(activity.formattedAction),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Time: ${activity.createdAt}'),
            if (activity.details != null && activity.details!.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text('Details:'),
              const SizedBox(height: 4),
              Text(activity.details.toString()),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  // Helper methods (same as in RecentActivity widget)
  IconData _getActivityIcon(String action) {
    switch (action) {
      case 'chit_created':
        return Icons.add_circle;
      case 'payment_made':
      case 'payment_received':
        return Icons.payment;
      case 'member_joined':
        return Icons.group_add;
      case 'bidding_started':
      case 'bidding_completed':
        return Icons.gavel;
      case 'notification_sent':
        return Icons.notifications;
      case 'user_login':
        return Icons.login;
      case 'user_logout':
        return Icons.logout;
      default:
        return Icons.info;
    }
  }

  Color _getActivityColor(ActivitySeverity severity) {
    switch (severity) {
      case ActivitySeverity.success:
        return Colors.green;
      case ActivitySeverity.warning:
        return Colors.orange;
      case ActivitySeverity.error:
        return Colors.red;
      case ActivitySeverity.info:
        return Colors.blue;
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }

  String _getActivitySubtitle(ActivityLog activity) {
    final details = activity.details;
    if (details == null || details.isEmpty) return '';

    switch (activity.action) {
      case 'chit_created':
        return details['chit_name'] ?? 'New chit fund';
      case 'payment_received':
        final amount = details['amount'];
        final from = details['from_user'];
        return 'From ${from ?? 'member'} - ₹${amount ?? '0'}';
      case 'member_joined':
        final chitName = details['chit_name'];
        final memberName = details['member_name'];
        return '${memberName ?? 'Member'} joined ${chitName ?? 'chit'}';
      case 'bidding_completed':
        final round = details['round'];
        final winner = details['winner'];
        return 'Round ${round ?? '?'} - Winner: ${winner ?? 'Unknown'}';
      default:
        return details.toString();
    }
  }
}
