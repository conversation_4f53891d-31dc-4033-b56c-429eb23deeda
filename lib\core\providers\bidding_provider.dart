import 'package:flutter/foundation.dart';
import '../models/chit_model.dart';
import '../models/api_response.dart';
import '../services/bidding_service.dart';

class BiddingProvider extends ChangeNotifier {
  BiddingRound? _currentBiddingRound;
  List<BiddingRound> _biddingHistory = [];
  List<Bid> _currentBids = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  BiddingRound? get currentBiddingRound => _currentBiddingRound;
  List<BiddingRound> get biddingHistory => _biddingHistory;
  List<Bid> get currentBids => _currentBids;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Check if bidding is active
  bool get isBiddingActive => _currentBiddingRound?.status == BiddingStatus.inProgress;

  // Get time remaining for current bidding round
  Duration? get timeRemaining {
    if (_currentBiddingRound?.endTime != null) {
      final now = DateTime.now();
      final endTime = _currentBiddingRound!.endTime!;
      if (endTime.isAfter(now)) {
        return endTime.difference(now);
      }
    }
    return null;
  }

  // Get current highest bid
  double? get currentHighestBid {
    if (_currentBids.isNotEmpty) {
      return _currentBids.map((bid) => bid.amount).reduce((a, b) => a > b ? a : b);
    }
    return null;
  }

  // Get current lowest bid (winner in chit fund)
  double? get currentLowestBid {
    if (_currentBids.isNotEmpty) {
      return _currentBids.map((bid) => bid.amount).reduce((a, b) => a < b ? a : b);
    }
    return null;
  }

  // Start a new bidding round
  Future<Result<BiddingRound>> startBiddingRound({
    required String chitId,
    required double startingBid,
    required DateTime meetingDate,
  }) async {
    try {
      _setLoading(true);
      
      final result = await BiddingService.startBiddingRound(
        chitId: chitId,
        startingBid: startingBid,
        meetingDate: meetingDate,
      );

      if (result.isSuccess) {
        _currentBiddingRound = result.data!;
        _currentBids = [];
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to start bidding round');
        return result;
      }
    } catch (e) {
      final error = 'Failed to start bidding round: $e';
      _setError(error);
      return Error<BiddingRound>(error);
    } finally {
      _setLoading(false);
    }
  }

  // Place a bid
  Future<Result<Bid>> placeBid({
    required String biddingRoundId,
    required double bidAmount,
  }) async {
    try {
      _setLoading(true);
      
      final result = await BiddingService.placeBid(
        biddingRoundId: biddingRoundId,
        bidAmount: bidAmount,
      );

      if (result.isSuccess) {
        // Add or update bid in current bids list
        final existingBidIndex = _currentBids.indexWhere(
          (bid) => bid.memberId == result.data!.memberId,
        );
        
        if (existingBidIndex != -1) {
          _currentBids[existingBidIndex] = result.data!;
        } else {
          _currentBids.add(result.data!);
        }
        
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to place bid');
        return result;
      }
    } catch (e) {
      final error = 'Failed to place bid: $e';
      _setError(error);
      return Error<Bid>(error);
    } finally {
      _setLoading(false);
    }
  }

  // End bidding round
  Future<Result<BiddingRound>> endBiddingRound(String biddingRoundId) async {
    try {
      _setLoading(true);
      
      final result = await BiddingService.endBiddingRound(
        biddingRoundId: biddingRoundId,
      );

      if (result.isSuccess) {
        _currentBiddingRound = result.data!;
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to end bidding round');
        return result;
      }
    } catch (e) {
      final error = 'Failed to end bidding round: $e';
      _setError(error);
      return Error<BiddingRound>(error);
    } finally {
      _setLoading(false);
    }
  }

  // Load current bidding round for a chit
  Future<void> loadCurrentBiddingRound(String chitId) async {
    try {
      _setLoading(true);
      
      final result = await BiddingService.getCurrentBiddingRound(chitId);

      if (result.isSuccess) {
        _currentBiddingRound = result.data;
        
        // Load bids for current round if it exists
        if (_currentBiddingRound != null) {
          await _loadBidsForCurrentRound();
        } else {
          _currentBids = [];
        }
        
        _clearError();
      } else {
        _setError(result.error ?? 'Failed to load current bidding round');
      }
    } catch (e) {
      _setError('Failed to load current bidding round: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load bidding history for a chit
  Future<void> loadBiddingHistory(String chitId) async {
    try {
      _setLoading(true);
      
      final result = await BiddingService.getBiddingHistory(chitId);

      if (result.isSuccess) {
        _biddingHistory = result.data!;
        _clearError();
      } else {
        _setError(result.error ?? 'Failed to load bidding history');
      }
    } catch (e) {
      _setError('Failed to load bidding history: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load bids for current round
  Future<void> _loadBidsForCurrentRound() async {
    if (_currentBiddingRound == null) return;
    
    try {
      final result = await BiddingService.getBidsForRound(_currentBiddingRound!.id);

      if (result.isSuccess) {
        _currentBids = result.data!;
      }
    } catch (e) {
      // Don't set error for this internal operation
      debugPrint('Failed to load bids for current round: $e');
    }
  }

  // Refresh current bidding data
  Future<void> refreshBiddingData(String chitId) async {
    await loadCurrentBiddingRound(chitId);
  }

  // Clear current bidding data
  void clearBiddingData() {
    _currentBiddingRound = null;
    _currentBids = [];
    _biddingHistory = [];
    _clearError();
    notifyListeners();
  }

  // Get bid by member ID
  Bid? getBidByMemberId(String memberId) {
    try {
      return _currentBids.firstWhere((bid) => bid.memberId == memberId);
    } catch (e) {
      return null;
    }
  }

  // Check if member has placed a bid
  bool hasMemberPlacedBid(String memberId) {
    return getBidByMemberId(memberId) != null;
  }

  // Get sorted bids (lowest to highest)
  List<Bid> get sortedBids {
    final bids = List<Bid>.from(_currentBids);
    bids.sort((a, b) => a.amount.compareTo(b.amount));
    return bids;
  }

  // Get winning bid (lowest bid)
  Bid? get winningBid {
    if (_currentBids.isNotEmpty) {
      return sortedBids.first;
    }
    return null;
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }
}
