<?php
/**
 * Advanced Data Protection Manager
 * 
 * Implements military-grade data protection:
 * - AES-256 encryption with key rotation
 * - Field-level encryption
 * - Data masking and tokenization
 * - Secure key management
 * - GDPR compliance features
 */

class DataProtectionManager {
    
    private static $masterKey;
    private static $keyRotationInterval = 86400; // 24 hours
    private static $encryptionAlgorithm = 'AES-256-GCM';
    
    // Sensitive fields that require encryption
    private static $sensitiveFields = [
        'password_hash', 'phone', 'email', 'bank_account', 
        'payment_details', 'personal_id', 'address'
    ];
    
    // PII fields for GDPR compliance
    private static $piiFields = [
        'name', 'email', 'phone', 'address', 'date_of_birth',
        'personal_id', 'bank_account', 'payment_details'
    ];
    
    /**
     * Initialize data protection system
     */
    public static function initialize() {
        self::$masterKey = self::getMasterKey();
        self::rotateKeysIfNeeded();
    }
    
    /**
     * Encrypt sensitive data with field-level encryption
     */
    public static function encryptField($data, $fieldName, $userId = null) {
        if (!in_array($fieldName, self::$sensitiveFields)) {
            return $data; // Not a sensitive field
        }
        
        try {
            // Generate field-specific key
            $fieldKey = self::deriveFieldKey($fieldName, $userId);
            
            // Generate random IV
            $iv = random_bytes(12); // 96-bit IV for GCM
            
            // Encrypt data
            $encrypted = openssl_encrypt(
                $data,
                self::$encryptionAlgorithm,
                $fieldKey,
                OPENSSL_RAW_DATA,
                $iv,
                $tag
            );
            
            if ($encrypted === false) {
                throw new Exception('Encryption failed');
            }
            
            // Combine IV, tag, and encrypted data
            $encryptedData = base64_encode($iv . $tag . $encrypted);
            
            // Log encryption event
            self::logEncryptionEvent($fieldName, $userId, 'encrypt');
            
            return $encryptedData;
            
        } catch (Exception $e) {
            error_log("Field encryption error for {$fieldName}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Decrypt sensitive data
     */
    public static function decryptField($encryptedData, $fieldName, $userId = null) {
        if (!in_array($fieldName, self::$sensitiveFields)) {
            return $encryptedData; // Not a sensitive field
        }
        
        try {
            // Generate field-specific key
            $fieldKey = self::deriveFieldKey($fieldName, $userId);
            
            // Decode encrypted data
            $data = base64_decode($encryptedData);
            
            // Extract IV, tag, and encrypted data
            $iv = substr($data, 0, 12);
            $tag = substr($data, 12, 16);
            $encrypted = substr($data, 28);
            
            // Decrypt data
            $decrypted = openssl_decrypt(
                $encrypted,
                self::$encryptionAlgorithm,
                $fieldKey,
                OPENSSL_RAW_DATA,
                $iv,
                $tag
            );
            
            if ($decrypted === false) {
                throw new Exception('Decryption failed');
            }
            
            // Log decryption event
            self::logEncryptionEvent($fieldName, $userId, 'decrypt');
            
            return $decrypted;
            
        } catch (Exception $e) {
            error_log("Field decryption error for {$fieldName}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Tokenize sensitive data (replace with non-sensitive tokens)
     */
    public static function tokenizeData($data, $fieldName) {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();
            
            // Generate unique token
            $token = 'TKN_' . bin2hex(random_bytes(16));
            
            // Store mapping in secure token vault
            $sql = "INSERT INTO token_vault (token, field_name, encrypted_value, created_at) 
                    VALUES (?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $token,
                $fieldName,
                self::encryptField($data, 'token_vault_data')
            ]);
            
            return $token;
            
        } catch (Exception $e) {
            error_log("Tokenization error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Detokenize data
     */
    public static function detokenizeData($token) {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            $sql = "SELECT encrypted_value FROM token_vault WHERE token = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$token]);
            
            $result = $stmt->fetch();
            if (!$result) {
                return false;
            }
            
            return self::decryptField($result['encrypted_value'], 'token_vault_data');
            
        } catch (Exception $e) {
            error_log("Detokenization error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mask sensitive data for display
     */
    public static function maskData($data, $fieldName) {
        switch ($fieldName) {
            case 'phone':
                return self::maskPhone($data);
            case 'email':
                return self::maskEmail($data);
            case 'bank_account':
                return self::maskBankAccount($data);
            case 'personal_id':
                return self::maskPersonalId($data);
            default:
                return self::maskGeneric($data);
        }
    }
    
    /**
     * GDPR compliance - Right to be forgotten
     */
    public static function forgetUser($userId) {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            // Start transaction
            $conn->beginTransaction();
            
            // 1. Anonymize user data
            $anonymizedData = [
                'name' => 'DELETED_USER_' . $userId,
                'email' => 'deleted_' . $userId . '@deleted.local',
                'phone' => 'DELETED',
                'status' => 'deleted'
            ];
            
            $sql = "UPDATE users SET name = ?, email = ?, phone = ?, status = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $anonymizedData['name'],
                $anonymizedData['email'],
                $anonymizedData['phone'],
                $anonymizedData['status'],
                $userId
            ]);
            
            // 2. Remove from token vault
            $sql = "DELETE FROM token_vault WHERE field_name LIKE ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute(['user_' . $userId . '_%']);
            
            // 3. Anonymize activity logs
            $sql = "UPDATE activity_logs SET details = ? WHERE user_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                json_encode(['anonymized' => true, 'reason' => 'gdpr_deletion']),
                $userId
            ]);
            
            // 4. Log GDPR deletion
            self::logGDPREvent($userId, 'user_forgotten', $anonymizedData);
            
            $conn->commit();
            return true;
            
        } catch (Exception $e) {
            $conn->rollback();
            error_log("GDPR forget user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * GDPR compliance - Data export
     */
    public static function exportUserData($userId) {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            $exportData = [];
            
            // 1. User profile data
            $sql = "SELECT name, email, phone, created_at FROM users WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$userId]);
            $exportData['profile'] = $stmt->fetch();
            
            // 2. Activity logs
            $sql = "SELECT action, details, created_at FROM activity_logs WHERE user_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$userId]);
            $exportData['activity'] = $stmt->fetchAll();
            
            // 3. Chit memberships
            $sql = "SELECT c.name, cm.member_number, cm.status, cm.joined_date 
                    FROM chit_members cm 
                    JOIN chits c ON cm.chit_id = c.id 
                    WHERE cm.user_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$userId]);
            $exportData['chits'] = $stmt->fetchAll();
            
            // 4. Payments
            $sql = "SELECT p.amount, p.type, p.status, p.due_date, p.paid_date 
                    FROM payments p 
                    JOIN chit_members cm ON p.member_id = cm.id 
                    WHERE cm.user_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$userId]);
            $exportData['payments'] = $stmt->fetchAll();
            
            // Log GDPR export
            self::logGDPREvent($userId, 'data_exported', ['export_size' => strlen(json_encode($exportData))]);
            
            return $exportData;
            
        } catch (Exception $e) {
            error_log("GDPR export error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Secure key rotation
     */
    public static function rotateKeys() {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            // Generate new master key
            $newMasterKey = bin2hex(random_bytes(32));
            
            // Store new key with timestamp
            $sql = "INSERT INTO encryption_keys (key_hash, created_at, is_active) VALUES (?, NOW(), 1)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([hash('sha256', $newMasterKey)]);
            
            // Deactivate old keys
            $sql = "UPDATE encryption_keys SET is_active = 0 WHERE key_hash != ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([hash('sha256', $newMasterKey)]);
            
            // Update master key
            self::$masterKey = $newMasterKey;
            
            // Log key rotation
            self::logSecurityEvent(null, 'key_rotation', ['timestamp' => time()]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Key rotation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Data integrity verification
     */
    public static function verifyDataIntegrity($tableName, $recordId) {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            // Get current record
            $sql = "SELECT * FROM {$tableName} WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$recordId]);
            $record = $stmt->fetch();
            
            if (!$record) {
                return false;
            }
            
            // Calculate current hash
            $currentHash = hash('sha256', json_encode($record));
            
            // Check against stored integrity hash
            $sql = "SELECT integrity_hash FROM data_integrity WHERE table_name = ? AND record_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$tableName, $recordId]);
            $storedHash = $stmt->fetchColumn();
            
            $isValid = ($currentHash === $storedHash);
            
            if (!$isValid) {
                self::logSecurityEvent(null, 'data_integrity_violation', [
                    'table' => $tableName,
                    'record_id' => $recordId,
                    'current_hash' => $currentHash,
                    'stored_hash' => $storedHash
                ]);
            }
            
            return $isValid;
            
        } catch (Exception $e) {
            error_log("Data integrity verification error: " . $e->getMessage());
            return false;
        }
    }
    
    // Private helper methods
    private static function getMasterKey() {
        return defined('MASTER_ENCRYPTION_KEY') ? MASTER_ENCRYPTION_KEY : bin2hex(random_bytes(32));
    }
    
    private static function deriveFieldKey($fieldName, $userId = null) {
        $context = $fieldName . ($userId ? "_user_{$userId}" : '');
        return hash('sha256', self::$masterKey . $context);
    }
    
    private static function rotateKeysIfNeeded() {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            $sql = "SELECT created_at FROM encryption_keys WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $lastRotation = $stmt->fetchColumn();
            
            if (!$lastRotation || (time() - strtotime($lastRotation)) > self::$keyRotationInterval) {
                self::rotateKeys();
            }
            
        } catch (Exception $e) {
            error_log("Key rotation check error: " . $e->getMessage());
        }
    }
    
    private static function maskPhone($phone) {
        if (strlen($phone) < 4) return '****';
        return substr($phone, 0, 2) . str_repeat('*', strlen($phone) - 4) . substr($phone, -2);
    }
    
    private static function maskEmail($email) {
        $parts = explode('@', $email);
        if (count($parts) != 2) return '****@****.***';
        
        $username = $parts[0];
        $domain = $parts[1];
        
        $maskedUsername = substr($username, 0, 1) . str_repeat('*', max(0, strlen($username) - 2)) . substr($username, -1);
        $maskedDomain = substr($domain, 0, 1) . str_repeat('*', max(0, strlen($domain) - 2)) . substr($domain, -1);
        
        return $maskedUsername . '@' . $maskedDomain;
    }
    
    private static function maskBankAccount($account) {
        if (strlen($account) < 4) return '****';
        return str_repeat('*', strlen($account) - 4) . substr($account, -4);
    }
    
    private static function maskPersonalId($id) {
        if (strlen($id) < 4) return '****';
        return substr($id, 0, 2) . str_repeat('*', strlen($id) - 4) . substr($id, -2);
    }
    
    private static function maskGeneric($data) {
        if (strlen($data) < 4) return '****';
        return substr($data, 0, 2) . str_repeat('*', strlen($data) - 4) . substr($data, -2);
    }
    
    private static function logEncryptionEvent($fieldName, $userId, $action) {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            $sql = "INSERT INTO encryption_logs (user_id, field_name, action, created_at) VALUES (?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$userId, $fieldName, $action]);
            
        } catch (Exception $e) {
            error_log("Encryption logging error: " . $e->getMessage());
        }
    }
    
    private static function logGDPREvent($userId, $event, $details) {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            $sql = "INSERT INTO gdpr_logs (user_id, event_type, details, created_at) VALUES (?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$userId, $event, json_encode($details)]);
            
        } catch (Exception $e) {
            error_log("GDPR logging error: " . $e->getMessage());
        }
    }
    
    private static function logSecurityEvent($userId, $event, $details) {
        // Use existing security logging function
        if (function_exists('logActivity')) {
            logActivity($userId, $event, $details);
        }
    }
}

?>
