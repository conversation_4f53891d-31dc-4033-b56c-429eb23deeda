import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/providers/payment_provider.dart';
import '../../../core/router/app_router.dart';
import 'dashboard_card.dart';

class StatisticsOverview extends StatelessWidget {
  const StatisticsOverview({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Statistics Grid
        Consumer2<ChitProvider, PaymentProvider>(
          builder: (context, chitProvider, paymentProvider, child) {
            return LayoutBuilder(
              builder: (context, constraints) {
                final cardWidth = (constraints.maxWidth - 16) / 2; // Account for spacing
                final cardHeight = cardWidth * 0.85; // More flexible ratio

                return GridView.count(
                  crossAxisCount: 2,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: cardWidth / cardHeight,
              children: [
                // Total Chits
                StatCard(
                  title: 'Total Chits',
                  value: '${chitProvider.chits.length}',
                  icon: Icons.account_balance_wallet,
                  color: Colors.blue,
                  subtitle: _getActiveChitsSubtitle(chitProvider),
                  onTap: () {
                    AppNavigation.goToChitList(context);
                  },
                ),
                
                // Total Investment
                StatCard(
                  title: 'Total Investment',
                  value: '₹${_formatAmount(_getTotalInvestment(chitProvider))}',
                  icon: Icons.trending_up,
                  color: Colors.green,
                  subtitle: 'Across all chits',
                ),
                
                // Pending Payments
                StatCard(
                  title: 'Pending Payments',
                  value: '${paymentProvider.pendingPayments.length}',
                  icon: Icons.payment,
                  color: Colors.orange,
                  subtitle: '₹${_formatAmount(paymentProvider.totalPendingAmount)}',
                  onTap: () {
                    AppNavigation.goToPayments(context);
                  },
                ),
                
                // This Month
                StatCard(
                  title: 'This Month',
                  value: '₹${_formatAmount(_getThisMonthAmount(paymentProvider))}',
                  icon: Icons.calendar_today,
                  color: Colors.purple,
                  subtitle: 'Contributions',
                ),
                ],
              );
              },
            );
          },
        ),
      ],
    );
  }

  String _getActiveChitsSubtitle(ChitProvider chitProvider) {
    final activeCount = chitProvider.chits.where((chit) => chit.status.name == 'active').length;
    return '$activeCount active';
  }

  double _getTotalInvestment(ChitProvider chitProvider) {
    return chitProvider.chits.fold(0.0, (sum, chit) => sum + chit.totalAmount);
  }

  double _getThisMonthAmount(PaymentProvider paymentProvider) {
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month);
    final nextMonth = DateTime(now.year, now.month + 1);
    
    return paymentProvider.payments
        .where((payment) => 
            payment.dueDate.isAfter(thisMonth) && 
            payment.dueDate.isBefore(nextMonth))
        .fold(0.0, (sum, payment) => sum + (payment.amount ?? 0.0));
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}

// Additional statistics widgets
class MonthlyTrend extends StatelessWidget {
  const MonthlyTrend({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Monthly Trend',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Icon(
                  Icons.trending_up,
                  color: Colors.green,
                  size: 20,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Placeholder for chart
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.bar_chart,
                      size: 32,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Analytics Dashboard',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PaymentSummary extends StatelessWidget {
  const PaymentSummary({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PaymentProvider>(
      builder: (context, paymentProvider, child) {
        final stats = paymentProvider.getPaymentStatistics();
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Payment Summary',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryItem(
                        context,
                        'Paid',
                        '${stats['paidCount']}',
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        context,
                        'Pending',
                        '${stats['pendingCount']}',
                        Colors.orange,
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        context,
                        'Overdue',
                        '${stats['overdueCount']}',
                        Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummaryItem(BuildContext context, String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }
}
