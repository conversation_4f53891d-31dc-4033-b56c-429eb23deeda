<?php
/**
 * Reset OTP verification status for testing
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Include configuration
    if (file_exists(__DIR__ . '/../config/env.php')) {
        require_once __DIR__ . '/../config/env.php';
    }
    
    // Include database
    require_once __DIR__ . '/../config/database.php';
    
    $db = new Database();
    $conn = $db->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Get email parameter
    $email = $_GET['email'] ?? $_POST['email'] ?? '<EMAIL>';
    
    // Reset all OTPs for this email to unverified status
    $sql = "UPDATE otps SET verified = 0, verified_at = NULL WHERE email = ?";
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([$email]);
    
    if ($result) {
        // Get the current OTP info
        $sql = "SELECT otp, type, expires_at, verified, created_at FROM otps WHERE email = ? ORDER BY created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$email]);
        $otpInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'message' => 'OTP verification status reset successfully',
            'data' => [
                'email' => $email,
                'current_otp' => $otpInfo,
                'instructions' => 'You can now use this OTP for verification testing'
            ]
        ], JSON_PRETTY_PRINT);
    } else {
        throw new Exception("Failed to reset OTP verification status");
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to reset OTP',
        'error' => $e->getMessage()
    ], JSON_PRETTY_PRINT);
}

?>
