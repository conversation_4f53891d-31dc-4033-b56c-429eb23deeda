# Chit Fund API Documentation - LiteSpeed Optimized with OTP

## 🚀 **Overview**

This API is optimized for LiteSpeed shared hosting and includes comprehensive OTP (One-Time Password) verification for secure user registration and password reset functionality.

## 🔐 **Authentication Endpoints**

### Base URL: `/api/auth`

---

## **1. Send OTP**
**Endpoint:** `POST /api/auth/send-otp`

Send a 6-digit OTP to user's email for registration or password reset.

### Request Body:
```json
{
    "email": "<EMAIL>",
    "type": "registration" // or "forgot_password"
}
```

### Response:
```json
{
    "success": true,
    "data": {
        "email": "<EMAIL>",
        "expires_in": 600
    },
    "message": "OTP sent successfully"
}
```

### Error Responses:
- `400` - Invalid email format or type
- `409` - Email already registered (for registration type)
- `500` - Failed to send OTP

---

## **2. Verify OTP**
**Endpoint:** `POST /api/auth/verify-otp`

Verify the 6-digit OTP sent to user's email.

### Request Body:
```json
{
    "email": "<EMAIL>",
    "otp": "123456",
    "type": "registration" // or "forgot_password"
}
```

### Response:
```json
{
    "success": true,
    "data": {
        "email": "<EMAIL>",
        "verified": true
    },
    "message": "OTP verified successfully"
}
```

### Error Responses:
- `400` - Invalid OTP format or expired OTP
- `500` - Verification failed

---

## **3. User Registration**
**Endpoint:** `POST /api/auth/register`

Register a new user with OTP verification.

### Request Body:
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "**********",
    "password": "securepassword123",
    "otp": "123456"
}
```

### Response:
```json
{
    "success": true,
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "**********",
            "role": "member"
        }
    },
    "message": "Registration successful"
}
```

### Validation Rules:
- **Name:** Minimum 2 characters
- **Email:** Valid email format
- **Phone:** 10 digits starting with 6-9 (Indian format)
- **Password:** Minimum 6 characters
- **OTP:** Must be verified before registration

### Error Responses:
- `400` - Validation errors or unverified OTP
- `409` - Email or phone already registered
- `500` - Registration failed

---

## **4. User Login**
**Endpoint:** `POST /api/auth/login`

Authenticate user with email and password.

### Request Body:
```json
{
    "email": "<EMAIL>",
    "password": "securepassword123"
}
```

### Response:
```json
{
    "success": true,
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "**********",
            "role": "member"
        }
    },
    "message": "Login successful"
}
```

### Error Responses:
- `400` - Missing email or password
- `401` - Invalid credentials
- `423` - Account locked due to too many failed attempts
- `500` - Login failed

---

## **5. Reset Password**
**Endpoint:** `POST /api/auth/reset-password`

Reset user password with OTP verification.

### Request Body:
```json
{
    "email": "<EMAIL>",
    "otp": "123456",
    "new_password": "newsecurepassword123"
}
```

### Response:
```json
{
    "success": true,
    "data": null,
    "message": "Password reset successful"
}
```

### Error Responses:
- `400` - Invalid OTP or password requirements not met
- `404` - User not found
- `500` - Password reset failed

---

## **6. Logout**
**Endpoint:** `POST /api/auth/logout`

Logout user and invalidate tokens.

### Headers:
```
Authorization: Bearer <token>
```

### Response:
```json
{
    "success": true,
    "data": null,
    "message": "Logout successful"
}
```

---

## **7. Refresh Token**
**Endpoint:** `POST /api/auth/refresh`

Refresh access token using refresh token.

### Request Body:
```json
{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### Response:
```json
{
    "success": true,
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    },
    "message": "Token refreshed successfully"
}
```

---

## **8. Verify Token**
**Endpoint:** `GET /api/auth/verify`

Verify if the current token is valid.

### Headers:
```
Authorization: Bearer <token>
```

### Response:
```json
{
    "success": true,
    "data": {
        "valid": true,
        "user": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "role": "member"
        }
    },
    "message": "Token is valid"
}
```

---

## **9. Get Profile**
**Endpoint:** `GET /api/auth/profile`

Get current user's profile information.

### Headers:
```
Authorization: Bearer <token>
```

### Response:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "**********",
        "role": "member",
        "created_at": "2024-01-01T00:00:00Z"
    },
    "message": "Profile retrieved successfully"
}
```

---

## **10. Update Profile**
**Endpoint:** `PUT /api/auth/profile`

Update current user's profile information.

### Headers:
```
Authorization: Bearer <token>
```

### Request Body:
```json
{
    "name": "John Updated",
    "phone": "9876543211"
}
```

### Response:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "John Updated",
        "email": "<EMAIL>",
        "phone": "9876543211",
        "role": "member"
    },
    "message": "Profile updated successfully"
}
```

---

## **11. Change Password**
**Endpoint:** `POST /api/auth/change-password`

Change user's password (requires current password).

### Headers:
```
Authorization: Bearer <token>
```

### Request Body:
```json
{
    "current_password": "oldpassword123",
    "new_password": "newpassword123"
}
```

### Response:
```json
{
    "success": true,
    "data": null,
    "message": "Password changed successfully"
}
```

---

## 🔧 **System Endpoints**

### **Health Check**
**Endpoint:** `GET /api/health`

Check API health and system status.

### Response:
```json
{
    "success": true,
    "data": {
        "status": "healthy",
        "timestamp": "2024-01-01 12:00:00",
        "version": "1.0.0",
        "environment": "production",
        "features": {
            "otp_verification": true,
            "litespeed_optimized": true,
            "rate_limiting": true,
            "caching": true
        },
        "performance": {
            "execution_time_ms": 45.67,
            "memory_usage_mb": 12.34,
            "peak_memory_mb": 15.67
        },
        "database": "connected",
        "cache": {
            "enabled": true,
            "litespeed": true
        }
    }
}
```

---

## 🛡️ **Security Features**

### **Rate Limiting**
- **Limit:** 60 requests per minute per IP
- **Response:** `429 Too Many Requests` when exceeded

### **OTP Security**
- **Format:** 6-digit numeric code
- **Expiry:** 10 minutes
- **Single Use:** OTP is deleted after successful verification
- **Type Specific:** Separate OTPs for registration and password reset

### **Password Requirements**
- **Minimum Length:** 6 characters
- **Hashing:** bcrypt with cost factor 10 (optimized for shared hosting)

### **JWT Tokens**
- **Access Token:** 24 hours expiry
- **Refresh Token:** 7 days expiry
- **Algorithm:** HS256

---

## 🚀 **LiteSpeed Optimizations**

### **Caching**
- **LiteSpeed Cache:** Enabled for cacheable endpoints
- **Cache Headers:** `X-LiteSpeed-Cache-Control`
- **Cache Tags:** Route-specific cache invalidation

### **Performance**
- **Connection Pooling:** Database connection reuse
- **OPcache:** PHP bytecode caching
- **Compression:** Gzip compression enabled
- **Memory Optimization:** 128MB limit for shared hosting

### **Error Handling**
- **Production Safe:** No sensitive information in error responses
- **Logging:** Comprehensive error logging
- **Monitoring:** Performance metrics and slow query detection

---

## 📝 **Usage Flow**

### **Registration Flow:**
1. `POST /api/auth/send-otp` with `type: "registration"`
2. `POST /api/auth/verify-otp` to verify the OTP
3. `POST /api/auth/register` with verified OTP

### **Password Reset Flow:**
1. `POST /api/auth/send-otp` with `type: "forgot_password"`
2. `POST /api/auth/verify-otp` to verify the OTP
3. `POST /api/auth/reset-password` with verified OTP and new password

### **Authentication Flow:**
1. `POST /api/auth/login` to get tokens
2. Use `Authorization: Bearer <token>` header for protected endpoints
3. `POST /api/auth/refresh` when token expires
4. `POST /api/auth/logout` to invalidate tokens

---

## 🔍 **Error Codes**

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input or validation error |
| 401 | Unauthorized - Invalid credentials or token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 423 | Locked - Account locked due to failed attempts |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |
| 503 | Service Unavailable - Maintenance mode |

---

## 📧 **Email Configuration**

For OTP functionality to work properly, configure SMTP settings in your environment:

```php
// In config/env.php
define('SMTP_HOST', 'mail.yourdomain.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_email_password');
define('SMTP_ENCRYPTION', 'tls');
```

---

## 🎯 **Production Deployment**

1. **Upload optimized files** to your LiteSpeed shared hosting
2. **Configure database** credentials in `config/env.php`
3. **Set up email** SMTP settings for OTP delivery
4. **Import database schema** from `database/schema.sql`
5. **Test endpoints** using the health check: `GET /api/health`

Your Chit Fund API is now **production-ready** with comprehensive OTP verification and LiteSpeed optimizations! 🚀
