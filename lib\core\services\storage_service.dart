import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static late Box _userBox;
  static late Box _chitBox;
  static late Box _settingsBox;
  static late SharedPreferences _prefs;

  // Box names
  static const String userBoxName = 'user_box';
  static const String chitBoxName = 'chit_box';
  static const String settingsBoxName = 'settings_box';

  // Keys
  static const String authTokenKey = 'auth_token';
  static const String userIdKey = 'user_id';
  static const String userDataKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';
  static const String rememberMeKey = 'remember_me';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String notificationsEnabledKey = 'notifications_enabled';
  static const String emailNotificationsKey = 'email_notifications';
  static const String paymentRemindersKey = 'payment_reminders';
  static const String biddingAlertsKey = 'bidding_alerts';
  static const String chitUpdatesKey = 'chit_updates';
  static const String memberUpdatesKey = 'member_updates';
  static const String systemNotificationsKey = 'system_notifications';
  static const String marketingEmailsKey = 'marketing_emails';
  static const String biometricEnabledKey = 'biometric_enabled';
  static const String twoFactorEnabledKey = 'two_factor_enabled';
  static const String loginAlertsEnabledKey = 'login_alerts_enabled';
  static const String sessionTimeoutEnabledKey = 'session_timeout_enabled';
  static const String sessionTimeoutMinutesKey = 'session_timeout_minutes';
  static const String autoBackupEnabledKey = 'auto_backup_enabled';

  static Future<void> init() async {
    try {
      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();

      // Open Hive boxes
      _userBox = await Hive.openBox(userBoxName);
      _chitBox = await Hive.openBox(chitBoxName);
      _settingsBox = await Hive.openBox(settingsBoxName);
    } catch (e) {
      throw Exception('Failed to initialize storage: $e');
    }
  }

  // Auth Token Management
  static Future<void> saveAuthToken(String token) async {
    await _prefs.setString(authTokenKey, token);
  }

  static String? getAuthToken() {
    return _prefs.getString(authTokenKey);
  }

  static Future<void> removeAuthToken() async {
    await _prefs.remove(authTokenKey);
  }

  // User ID Management
  static Future<void> saveUserId(String userId) async {
    await _prefs.setString(userIdKey, userId);
  }

  static String? getUserId() {
    return _prefs.getString(userIdKey);
  }

  static Future<void> removeUserId() async {
    await _prefs.remove(userIdKey);
  }

  // Login Status
  static Future<void> setLoggedIn(bool isLoggedIn) async {
    await _prefs.setBool(isLoggedInKey, isLoggedIn);
  }

  static bool isLoggedIn() {
    return _prefs.getBool(isLoggedInKey) ?? false;
  }

  // Remember Me Status
  static Future<void> setRememberMe(bool rememberMe) async {
    await _prefs.setBool(rememberMeKey, rememberMe);
  }

  static bool getRememberMe() {
    return _prefs.getBool(rememberMeKey) ?? true; // Default to true
  }

  // User Data Management (using Hive for complex objects)
  static Future<void> saveUserData(Map<String, dynamic> userData) async {
    await _userBox.put(userDataKey, userData);
  }

  static Map<String, dynamic>? getUserData() {
    final data = _userBox.get(userDataKey);
    return data != null ? Map<String, dynamic>.from(data) : null;
  }

  static Future<void> removeUserData() async {
    await _userBox.delete(userDataKey);
  }

  // Chit Data Management
  static Future<void> saveChitData(String chitId, Map<String, dynamic> chitData) async {
    await _chitBox.put(chitId, chitData);
  }

  static Map<String, dynamic>? getChitData(String chitId) {
    final data = _chitBox.get(chitId);
    return data != null ? Map<String, dynamic>.from(data) : null;
  }

  static List<Map<String, dynamic>> getAllChits() {
    final chits = <Map<String, dynamic>>[];
    for (final key in _chitBox.keys) {
      final chitData = _chitBox.get(key);
      if (chitData != null) {
        chits.add(Map<String, dynamic>.from(chitData));
      }
    }
    return chits;
  }

  static Future<void> removeChitData(String chitId) async {
    await _chitBox.delete(chitId);
  }

  static Future<void> clearAllChits() async {
    await _chitBox.clear();
  }

  // Settings Management
  static Future<void> saveThemeMode(String themeMode) async {
    await _settingsBox.put(themeKey, themeMode);
  }

  static String getThemeMode() {
    return _settingsBox.get(themeKey, defaultValue: 'system');
  }

  static Future<void> saveLanguage(String language) async {
    await _settingsBox.put(languageKey, language);
  }

  static String getLanguage() {
    return _settingsBox.get(languageKey, defaultValue: 'en');
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    await _settingsBox.put(notificationsEnabledKey, enabled);
  }

  static bool areNotificationsEnabled() {
    return _settingsBox.get(notificationsEnabledKey, defaultValue: true);
  }

  static Future<void> setEmailNotifications(bool enabled) async {
    await _settingsBox.put(emailNotificationsKey, enabled);
  }

  static bool getEmailNotifications() {
    return _settingsBox.get(emailNotificationsKey, defaultValue: true);
  }

  static Future<void> setPaymentReminders(bool enabled) async {
    await _settingsBox.put(paymentRemindersKey, enabled);
  }

  static bool getPaymentReminders() {
    return _settingsBox.get(paymentRemindersKey, defaultValue: true);
  }

  static Future<void> setBiddingAlerts(bool enabled) async {
    await _settingsBox.put(biddingAlertsKey, enabled);
  }

  static bool getBiddingAlerts() {
    return _settingsBox.get(biddingAlertsKey, defaultValue: true);
  }

  static Future<void> setChitUpdates(bool enabled) async {
    await _settingsBox.put(chitUpdatesKey, enabled);
  }

  static bool getChitUpdates() {
    return _settingsBox.get(chitUpdatesKey, defaultValue: true);
  }

  static Future<void> setMemberUpdates(bool enabled) async {
    await _settingsBox.put(memberUpdatesKey, enabled);
  }

  static bool getMemberUpdates() {
    return _settingsBox.get(memberUpdatesKey, defaultValue: true);
  }

  static Future<void> setSystemNotifications(bool enabled) async {
    await _settingsBox.put(systemNotificationsKey, enabled);
  }

  static bool getSystemNotifications() {
    return _settingsBox.get(systemNotificationsKey, defaultValue: true);
  }

  static Future<void> setMarketingEmails(bool enabled) async {
    await _settingsBox.put(marketingEmailsKey, enabled);
  }

  static bool getMarketingEmails() {
    return _settingsBox.get(marketingEmailsKey, defaultValue: false);
  }

  // Security Settings
  static Future<void> setBiometricEnabled(bool enabled) async {
    await _settingsBox.put(biometricEnabledKey, enabled);
  }

  static bool getBiometricEnabled() {
    return _settingsBox.get(biometricEnabledKey, defaultValue: false);
  }

  static Future<void> setTwoFactorEnabled(bool enabled) async {
    await _settingsBox.put(twoFactorEnabledKey, enabled);
  }

  static bool getTwoFactorEnabled() {
    return _settingsBox.get(twoFactorEnabledKey, defaultValue: false);
  }

  static Future<void> setLoginAlertsEnabled(bool enabled) async {
    await _settingsBox.put(loginAlertsEnabledKey, enabled);
  }

  static bool getLoginAlertsEnabled() {
    return _settingsBox.get(loginAlertsEnabledKey, defaultValue: true);
  }

  static Future<void> setSessionTimeoutEnabled(bool enabled) async {
    await _settingsBox.put(sessionTimeoutEnabledKey, enabled);
  }

  static bool getSessionTimeoutEnabled() {
    return _settingsBox.get(sessionTimeoutEnabledKey, defaultValue: true);
  }

  static Future<void> setSessionTimeoutMinutes(int minutes) async {
    await _settingsBox.put(sessionTimeoutMinutesKey, minutes);
  }

  static int getSessionTimeoutMinutes() {
    return _settingsBox.get(sessionTimeoutMinutesKey, defaultValue: 30);
  }

  // Backup Settings
  static Future<void> setAutoBackupEnabled(bool enabled) async {
    await _settingsBox.put(autoBackupEnabledKey, enabled);
  }

  static bool getAutoBackupEnabled() {
    return _settingsBox.get(autoBackupEnabledKey, defaultValue: true);
  }

  // Cache Management
  static Future<void> saveCacheData(String key, dynamic data) async {
    await _settingsBox.put('cache_$key', data);
  }

  static T? getCacheData<T>(String key) {
    return _settingsBox.get('cache_$key');
  }

  static Future<void> removeCacheData(String key) async {
    await _settingsBox.delete('cache_$key');
  }

  // Clear all data (logout)
  static Future<void> clearAllData() async {
    await _prefs.clear();
    await _userBox.clear();
    await _chitBox.clear();
    // Keep settings box for user preferences
  }

  // Clear only user-specific data
  static Future<void> clearUserData() async {
    await removeAuthToken();
    await removeUserId();
    await removeUserData();
    await setLoggedIn(false);
    await clearAllChits();
  }

  // Backup and Restore
  static Map<String, dynamic> exportUserData() {
    return {
      'userData': getUserData(),
      'chits': getAllChits(),
      'settings': {
        'theme': getThemeMode(),
        'language': getLanguage(),
        'notifications': areNotificationsEnabled(),
      },
    };
  }

  static Future<void> importUserData(Map<String, dynamic> data) async {
    try {
      // Import user data
      if (data['userData'] != null) {
        await saveUserData(data['userData']);
      }

      // Import chits
      if (data['chits'] != null) {
        final chits = List<Map<String, dynamic>>.from(data['chits']);
        for (final chit in chits) {
          if (chit['id'] != null) {
            await saveChitData(chit['id'], chit);
          }
        }
      }

      // Import settings
      if (data['settings'] != null) {
        final settings = data['settings'];
        if (settings['theme'] != null) {
          await saveThemeMode(settings['theme']);
        }
        if (settings['language'] != null) {
          await saveLanguage(settings['language']);
        }
        if (settings['notifications'] != null) {
          await setNotificationsEnabled(settings['notifications']);
        }
      }
    } catch (e) {
      throw Exception('Failed to import user data: $e');
    }
  }

  // Storage info
  static Map<String, dynamic> getStorageInfo() {
    return {
      'userBoxSize': _userBox.length,
      'chitBoxSize': _chitBox.length,
      'settingsBoxSize': _settingsBox.length,
      'isLoggedIn': isLoggedIn(),
      'hasAuthToken': getAuthToken() != null,
      'userId': getUserId(),
    };
  }
}
