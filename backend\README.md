# Chit Fund Management System - Backend API

A comprehensive PHP/MySQL backend API for managing chit funds with authentication, bidding system, and payment tracking.

## Features

- **User Authentication**: JWT-based authentication with login, registration, and password reset
- **Chit Management**: Create and manage chit funds with multiple members
- **Bidding System**: Real-time bidding interface with multiple rounds
- **Payment Tracking**: Track contributions, winnings, and commission payments
- **Notifications**: System notifications for reminders and updates
- **Audit Trail**: Complete activity logging for transparency
- **Role-based Access**: Admin, Organizer, and Member roles

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- mod_rewrite enabled (for Apache)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chit_fund/backend
   ```

2. **Configure environment**
   ```bash
   cp config/env.example.php config/env.php
   ```
   
   Edit `config/env.php` with your database credentials and settings.

3. **Set up database**
   ```bash
   php database/setup.php setup --with-sample-data
   ```

4. **Configure web server**
   
   **Apache**: Ensure mod_rewrite is enabled and point document root to the `api` directory.
   
   **Nginx**: Configure URL rewriting to route all requests to `api/index.php`.

5. **Set permissions**
   ```bash
   chmod 755 api/
   chmod 644 api/.htaccess
   ```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `POST /api/auth/verify-token` - Verify JWT token
- `POST /api/auth/refresh-token` - Refresh JWT token
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password with token

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/change-password` - Change password

### Chits
- `GET /api/chits/list` - List user's chits
- `POST /api/chits/create` - Create new chit
- `GET /api/chits/details/{id}` - Get chit details
- `PUT /api/chits/update/{id}` - Update chit
- `POST /api/chits/join/{id}` - Join a chit

### Health Check
- `GET /api/health` - API health status

## Database Schema

The system uses the following main tables:

- **users**: User accounts and authentication
- **chits**: Chit fund definitions
- **chit_members**: Members of each chit
- **bidding_rounds**: Bidding rounds for each chit
- **bids**: Individual bids in each round
- **payments**: Payment tracking
- **notifications**: System notifications
- **activity_logs**: Audit trail

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Error Handling

All API responses follow a consistent format:

**Success Response:**
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... }
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Error description",
  "details": { ... }
}
```

## Security Features

- Password hashing with bcrypt
- JWT token authentication
- Input validation and sanitization
- SQL injection prevention with prepared statements
- CORS configuration
- Rate limiting (configurable)
- Activity logging

## Configuration

Key configuration options in `config/env.php`:

- **Database**: Connection settings
- **JWT**: Secret key and expiry settings
- **Security**: Password requirements, login attempts
- **Email**: SMTP settings for notifications
- **File Upload**: Size limits and allowed types

## Development

### Running Tests
```bash
# Database setup test
php database/setup.php status

# API health check
curl http://localhost/api/health
```

### Sample Data

The setup script can load sample data for testing:
```bash
php database/setup.php setup --with-sample-data
```

This creates:
- Sample users with different roles
- Example chit funds
- Completed bidding rounds
- Payment records

### Logging

Application logs are written to the configured log file. Check logs for debugging:
```bash
tail -f logs/app.log
```

## Production Deployment

1. **Security Checklist**:
   - Change default JWT secret
   - Use strong database passwords
   - Enable HTTPS
   - Configure proper CORS origins
   - Set up regular database backups

2. **Performance**:
   - Enable PHP OPcache
   - Configure MySQL query cache
   - Set up proper indexing
   - Monitor slow queries

3. **Monitoring**:
   - Set up error logging
   - Monitor API response times
   - Track database performance
   - Set up alerts for failures

## API Documentation

For detailed API documentation with request/response examples, see the [API Documentation](docs/api.md) file.

## Support

For issues and questions:
1. Check the logs for error details
2. Verify database connection and schema
3. Ensure proper file permissions
4. Check web server configuration

## License

This project is licensed under the MIT License - see the LICENSE file for details.
