import 'debug_logger.dart';

/// API Validator to ensure requests match backend expectations
class ApiValidator {
  
  /// Validate Create Chit Request
  static Map<String, dynamic> validateCreateChitRequest({
    required String name,
    required String description,
    required double totalAmount,
    required int numberOfMembers,
    required String frequency,
    required String startDate,
    double? commissionPercentage,
    List<Map<String, dynamic>>? members,
  }) {
    // Calculate monthly amount (total amount divided by number of members)
    final monthlyAmount = totalAmount / numberOfMembers;
    
    final data = {
      'name': name,
      'description': description,
      'total_amount': totalAmount,
      'monthly_amount': monthlyAmount, // REQUIRED by PHP backend
      'number_of_members': numberOfMembers,
      'frequency': frequency,
      'start_date': startDate,
      'commission_percentage': commissionPercentage ?? 0.0,
      'members': members ?? [],
    };
    
    DebugLogger.logInfo('ApiValidator', 'Create Chit Request Validated');
    DebugLogger.logInfo('ApiValidator', 'Monthly Amount Calculated: $monthlyAmount');
    
    return data;
  }
  
  /// Validate Login Request
  static Map<String, dynamic> validateLoginRequest({
    required String email,
    required String password,
  }) {
    final data = {
      'email': email,
      'password': password,
    };
    
    DebugLogger.logInfo('ApiValidator', 'Login Request Validated');
    return data;
  }
  
  /// Validate Registration Request
  static Map<String, dynamic> validateRegistrationRequest({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String otp,
  }) {
    final data = {
      'name': name,
      'email': email,
      'phone': phone,
      'password': password,
      'otp': otp,
    };
    
    DebugLogger.logInfo('ApiValidator', 'Registration Request Validated');
    return data;
  }
  
  /// Validate Update Profile Request
  static Map<String, dynamic> validateUpdateProfileRequest({
    required String name,
    required String email,
    required String phone,
  }) {
    final data = {
      'name': name,
      'email': email,
      'phone': phone,
    };
    
    DebugLogger.logInfo('ApiValidator', 'Update Profile Request Validated');
    return data;
  }
  
  /// Validate Change Password Request
  static Map<String, dynamic> validateChangePasswordRequest({
    required String currentPassword,
    required String newPassword,
  }) {
    final data = {
      'current_password': currentPassword,
      'new_password': newPassword,
    };
    
    DebugLogger.logInfo('ApiValidator', 'Change Password Request Validated');
    return data;
  }
  
  /// Validate Join Chit Request
  static Map<String, dynamic> validateJoinChitRequest({
    required String chitCode,
  }) {
    final data = {
      'chit_code': chitCode,
    };
    
    DebugLogger.logInfo('ApiValidator', 'Join Chit Request Validated');
    return data;
  }
  
  /// Validate Make Payment Request
  static Map<String, dynamic> validateMakePaymentRequest({
    required String chitId,
    required double amount,
    String? remarks,
    String? paymentType,
    String? paymentMethod,
  }) {
    final data = {
      'chit_id': chitId,
      'amount': amount,
      'payment_type': paymentType ?? 'contribution',
      'payment_method': paymentMethod ?? 'online',
      'remarks': remarks,
    };
    
    DebugLogger.logInfo('ApiValidator', 'Make Payment Request Validated');
    return data;
  }
  
  /// Validate Send OTP Request
  static Map<String, dynamic> validateSendOtpRequest({
    required String email,
  }) {
    final data = {
      'email': email,
    };
    
    DebugLogger.logInfo('ApiValidator', 'Send OTP Request Validated');
    return data;
  }
  
  /// Validate Verify OTP Request
  static Map<String, dynamic> validateVerifyOtpRequest({
    required String email,
    required String otp,
  }) {
    final data = {
      'email': email,
      'otp': otp,
    };
    
    DebugLogger.logInfo('ApiValidator', 'Verify OTP Request Validated');
    return data;
  }
  
  /// Log API Request for Debugging
  static void logApiRequest(String endpoint, Map<String, dynamic> data) {
    DebugLogger.logInfo('ApiValidator', '=== API REQUEST VALIDATION ===');
    DebugLogger.logInfo('ApiValidator', 'Endpoint: $endpoint');
    DebugLogger.logInfo('ApiValidator', 'Request Data: $data');
    DebugLogger.logInfo('ApiValidator', 'Field Count: ${data.length}');
    
    // Log each field for detailed validation
    data.forEach((key, value) {
      DebugLogger.logInfo('ApiValidator', '  $key: $value (${value.runtimeType})');
    });
    
    DebugLogger.logInfo('ApiValidator', '=== END VALIDATION ===');
  }
  
  /// Validate Required Fields
  static bool validateRequiredFields(Map<String, dynamic> data, List<String> requiredFields) {
    final missingFields = <String>[];
    
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        missingFields.add(field);
      }
    }
    
    if (missingFields.isNotEmpty) {
      DebugLogger.logError('ApiValidator', 'Missing required fields: $missingFields');
      return false;
    }
    
    DebugLogger.logSuccess('ApiValidator', 'All required fields present');
    return true;
  }
}
