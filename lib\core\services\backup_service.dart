import 'package:dio/dio.dart';
import '../models/api_response.dart';
import 'api_service.dart';

/// Backup Service - Handles data backup and restore operations
class BackupService {
  static final Dio _dio = ApiService.dio;

  /// Create database backup
  static Future<Result<Map<String, dynamic>>> createDatabaseBackup() async {
    try {
      final response = await _dio.post('/backup/create-database');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess) {
          return Success(apiResponse.data ?? {});
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to create database backup');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Create files backup
  static Future<Result<Map<String, dynamic>>> createFilesBackup() async {
    try {
      final response = await _dio.post('/backup/create-files');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess) {
          return Success(apiResponse.data ?? {});
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to create files backup');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Create complete backup (database + files)
  static Future<Result<Map<String, dynamic>>> createCompleteBackup() async {
    try {
      final response = await _dio.post('/backup/create-complete');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess) {
          return Success(apiResponse.data ?? {});
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to create complete backup');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// List available backups
  static Future<Result<List<Map<String, dynamic>>>> listBackups() async {
    try {
      final response = await _dio.get('/backup/list');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final backups = apiResponse.data!
              .map((backup) => backup as Map<String, dynamic>)
              .toList();
          return Success(backups);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to retrieve backups');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Download backup file
  static Future<Result<String>> downloadBackup(String filename) async {
    try {
      final response = await _dio.get(
        '/backup/download',
        queryParameters: {'filename': filename},
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        // In a real app, you would save the file to device storage
        // For now, we'll return a success message
        return Success('Backup downloaded successfully');
      } else {
        return Error('Failed to download backup');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Delete backup file
  static Future<Result<void>> deleteBackup(String filename) async {
    try {
      final response = await _dio.delete('/backup', data: {
        'filename': filename,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess) {
          return Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to delete backup');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get backup statistics
  static Future<Result<List<Map<String, dynamic>>>> getBackupStats() async {
    try {
      final response = await _dio.get('/backup/stats');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final stats = apiResponse.data!
              .map((stat) => stat as Map<String, dynamic>)
              .toList();
          return Success(stats);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to retrieve backup statistics');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle Dio errors
  static String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout';
      case DioExceptionType.sendTimeout:
        return 'Send timeout';
      case DioExceptionType.receiveTimeout:
        return 'Receive timeout';
      case DioExceptionType.badResponse:
        if (e.response?.statusCode == 401) {
          return 'Unauthorized access';
        } else if (e.response?.statusCode == 403) {
          return 'Admin privileges required';
        } else if (e.response?.statusCode == 404) {
          return 'Backup not found';
        } else if (e.response?.statusCode == 500) {
          return 'Server error occurred';
        }
        return 'Request failed with status: ${e.response?.statusCode}';
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error';
      case DioExceptionType.unknown:
        return 'Unknown error occurred';
      default:
        return 'Network error occurred';
    }
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Get backup type icon
  static String getBackupTypeIcon(String type) {
    switch (type) {
      case 'database':
        return '🗄️';
      case 'files':
        return '📁';
      case 'complete':
        return '💾';
      default:
        return '📦';
    }
  }

  /// Get backup type description
  static String getBackupTypeDescription(String type) {
    switch (type) {
      case 'database':
        return 'Database backup containing all chit fund data';
      case 'files':
        return 'Files backup containing uploaded documents and images';
      case 'complete':
        return 'Complete backup including database and files';
      default:
        return 'Unknown backup type';
    }
  }
}
