import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'profile_image')
  final String? profileImage;
  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;
  @Json<PERSON>ey(name: 'updated_at')
  final String? updatedAt;
  @JsonKey(name: 'is_active')
  final bool isActive;
  final UserRole role;

  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    this.profileImage,
    this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.role = UserRole.member,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    try {
      return User(
        id: json['id']?.toString() ?? '',
        name: json['name']?.toString() ?? '',
        email: json['email']?.toString() ?? '',
        phone: json['phone']?.toString() ?? '',
        profileImage: json['profile_image']?.toString(),
        createdAt: json['created_at']?.toString(),
        updatedAt: json['updated_at']?.toString(),
        isActive: json['is_active'] == true || json['is_active'] == 1,
        role: UserRole.values.firstWhere(
          (role) => role.toString().split('.').last == (json['role'] ?? 'member'),
          orElse: () => UserRole.member,
        ),
      );
    } catch (e) {
      debugPrint('Error parsing User from JSON: $e');
      debugPrint('JSON data: $json');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? profileImage,
    String? createdAt,
    String? updatedAt,
    bool? isActive,
    UserRole? role,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      role: role ?? this.role,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, phone: $phone, role: $role)';
  }
}

enum UserRole {
  @JsonValue('admin')
  admin,
  @JsonValue('organizer')
  organizer,
  @JsonValue('member')
  member,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.organizer:
        return 'Chit Organizer';
      case UserRole.member:
        return 'Member';
    }
  }

  String get description {
    switch (this) {
      case UserRole.admin:
        return 'Full system access and management';
      case UserRole.organizer:
        return 'Can create and manage chit funds';
      case UserRole.member:
        return 'Can participate in chit funds';
    }
  }

  bool get canCreateChits {
    return this == UserRole.admin || this == UserRole.organizer;
  }

  bool get canManageUsers {
    return this == UserRole.admin;
  }

  bool get canViewReports {
    return this == UserRole.admin || this == UserRole.organizer;
  }
}
