import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';

class NotificationProvider extends ChangeNotifier {
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _errorMessage;
  int _unreadCount = 0;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  int get unreadCount => _unreadCount;

  // Filtered notifications
  List<NotificationModel> get unreadNotifications => 
      _notifications.where((notification) => !notification.isRead).toList();
  
  List<NotificationModel> get readNotifications => 
      _notifications.where((notification) => notification.isRead).toList();

  List<NotificationModel> getNotificationsByType(NotificationType type) =>
      _notifications.where((notification) => notification.type == type).toList();

  // Initialize notifications
  Future<void> initializeNotifications() async {
    await loadNotifications();
  }

  // Load notifications from server
  Future<void> loadNotifications({
    int page = 1,
    int limit = 50,
    bool? isRead,
    String? type,
  }) async {
    try {
      _setLoading(true);
      
      final result = await NotificationService.getNotifications(
        page: page,
        limit: limit,
        isRead: isRead,
        type: type,
      );
      
      if (result.isSuccess) {
        if (page == 1) {
          _notifications = result.data!;
        } else {
          _notifications.addAll(result.data!);
        }
        
        _updateUnreadCount();
        _clearError();
        notifyListeners();
      } else {
        _setError(result.error ?? 'Failed to load notifications');
      }
      
    } catch (e) {
      _setError('Failed to load notifications: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Mark notification as read
  Future<bool> markAsRead(String notificationId) async {
    try {
      final result = await NotificationService.markAsRead(notificationId);
      
      if (result.isSuccess) {
        // Update local state
        final index = _notifications.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          _notifications[index] = _notifications[index].markAsRead();
          _updateUnreadCount();
          notifyListeners();
        }
        return true;
      } else {
        _setError(result.error ?? 'Failed to mark notification as read');
        return false;
      }
    } catch (e) {
      _setError('Failed to mark notification as read: $e');
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead() async {
    try {
      final result = await NotificationService.markAllAsRead();
      
      if (result.isSuccess) {
        // Update local state
        _notifications = _notifications
            .map((notification) => notification.markAsRead())
            .toList();
        _updateUnreadCount();
        notifyListeners();
        return true;
      } else {
        _setError(result.error ?? 'Failed to mark all notifications as read');
        return false;
      }
    } catch (e) {
      _setError('Failed to mark all notifications as read: $e');
      return false;
    }
  }

  // Delete notification
  Future<bool> deleteNotification(String notificationId) async {
    try {
      final result = await NotificationService.deleteNotification(notificationId);
      
      if (result.isSuccess) {
        // Update local state
        _notifications.removeWhere((n) => n.id == notificationId);
        _updateUnreadCount();
        notifyListeners();
        return true;
      } else {
        _setError(result.error ?? 'Failed to delete notification');
        return false;
      }
    } catch (e) {
      _setError('Failed to delete notification: $e');
      return false;
    }
  }

  // Refresh notifications
  Future<void> refreshNotifications() async {
    await loadNotifications(page: 1);
  }

  // Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (_isLoading) return;
    
    final currentPage = (_notifications.length / 50).ceil() + 1;
    await loadNotifications(page: currentPage);
  }

  // Add new notification (for real-time updates)
  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    _updateUnreadCount();
    notifyListeners();
  }

  // Update notification (for real-time updates)
  void updateNotification(NotificationModel updatedNotification) {
    final index = _notifications.indexWhere((n) => n.id == updatedNotification.id);
    if (index != -1) {
      _notifications[index] = updatedNotification;
      _updateUnreadCount();
      notifyListeners();
    }
  }

  // Remove notification (for real-time updates)
  void removeNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    _updateUnreadCount();
    notifyListeners();
  }

  // Clear all notifications
  void clearNotifications() {
    _notifications.clear();
    _unreadCount = 0;
    notifyListeners();
  }

  // Archive a notification
  Future<bool> archiveNotification(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isArchived: true);
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('Failed to archive notification: $e');
      return false;
    }
  }

  // Unarchive a notification
  Future<bool> unarchiveNotification(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isArchived: false);
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('Failed to unarchive notification: $e');
      return false;
    }
  }

  // Clear all notifications (alias for clearNotifications)
  Future<bool> clearAllNotifications() async {
    try {
      clearNotifications();
      return true;
    } catch (e) {
      _setError('Failed to clear notifications: $e');
      return false;
    }
  }

  // Get notification by ID
  NotificationModel? getNotificationById(String notificationId) {
    try {
      return _notifications.firstWhere((n) => n.id == notificationId);
    } catch (e) {
      return null;
    }
  }

  // Search notifications
  List<NotificationModel> searchNotifications(String query) {
    if (query.isEmpty) return _notifications;
    
    final lowercaseQuery = query.toLowerCase();
    return _notifications.where((notification) {
      return notification.title.toLowerCase().contains(lowercaseQuery) ||
             notification.message.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Get notifications by date range
  List<NotificationModel> getNotificationsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) {
    return _notifications.where((notification) {
      return notification.createdAt.isAfter(startDate) &&
             notification.createdAt.isBefore(endDate);
    }).toList();
  }

  // Get recent notifications (last 24 hours)
  List<NotificationModel> getRecentNotifications() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return _notifications.where((notification) {
      return notification.createdAt.isAfter(yesterday);
    }).toList();
  }

  // Get statistics
  Map<String, dynamic> getStatistics() {
    final typeCount = <String, int>{};
    for (final notification in _notifications) {
      final type = notification.type.name;
      typeCount[type] = (typeCount[type] ?? 0) + 1;
    }

    return {
      'totalNotifications': _notifications.length,
      'unreadCount': _unreadCount,
      'readCount': _notifications.length - _unreadCount,
      'typeCount': typeCount,
      'recentCount': getRecentNotifications().length,
    };
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  void _updateUnreadCount() {
    _unreadCount = _notifications.where((n) => !n.isRead).length;
  }

  // Show local notification
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    await NotificationService.showLocalNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: body,
      payload: payload,
    );
  }

  // Schedule notification
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    await NotificationService.scheduleNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: body,
      scheduledDate: scheduledDate,
      payload: payload,
    );
  }

  // Cancel all local notifications
  Future<void> cancelAllLocalNotifications() async {
    await NotificationService.cancelAllNotifications();
  }
}
