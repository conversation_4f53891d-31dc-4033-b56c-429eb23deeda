import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/payment_model.dart';
import 'api_service.dart';
import 'storage_service.dart';

class PaymentService {
  static late Dio _dio;

  static void initialize() {
    ApiService.initialize();
    _dio = Dio(BaseOptions(
      baseUrl: ApiService.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token to requests
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
      ),
    );
  }

  /// Record a payment
  static Future<Result<Payment>> recordPayment({
    required String chitId,
    required String memberId,
    required double amount,
    required PaymentMethod method,
    required PaymentType type,
    String? transactionId,
    String? notes,
  }) async {
    try {
      final data = {
        'chit_id': chitId,
        'member_id': memberId,
        'amount': amount,
        'method': method.name,
        'type': type.name,
        'transaction_id': transactionId,
        'notes': notes,
      };

      final response = await _dio.post('/payments', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final payment = Payment.fromJson(apiResponse.data!);
          return Success(payment);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to record payment');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Make a payment (simplified for user payments)
  static Future<Result<Payment>> makePayment({
    required String chitId,
    required double amount,
    String? remarks,
  }) async {
    try {
      final data = {
        'chit_id': chitId,
        'amount': amount,
        'payment_type': 'contribution',
        'payment_method': 'online',
        'remarks': remarks,
      };

      final response = await _dio.post('/payments/make', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final payment = Payment.fromJson(apiResponse.data!);
          return Success(payment);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to make payment: ${response.statusMessage}');
      }
    } catch (e) {
      return Error('Failed to make payment: $e');
    }
  }

  /// Update payment status
  static Future<Result<Payment>> updatePayment({
    required String paymentId,
    PaymentStatus? status,
    String? transactionId,
    String? notes,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (status != null) data['status'] = status.name;
      if (transactionId != null) data['transaction_id'] = transactionId;
      if (notes != null) data['notes'] = notes;

      final response = await _dio.put('/payments/update/$paymentId', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final payment = Payment.fromJson(apiResponse.data!);
          return Success(payment);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to update payment');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get payments for a user
  static Future<Result<List<Payment>>> getUserPayments() async {
    try {
      final response = await _dio.get('/payments/list');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final payments = apiResponse.data!
              .map((paymentJson) => Payment.fromJson(paymentJson as Map<String, dynamic>))
              .toList();
          return Success(payments);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get user payments');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get pending payments
  static Future<Result<List<Payment>>> getPendingPayments() async {
    try {
      final response = await _dio.get('/payments/pending');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final payments = apiResponse.data!
              .map((paymentJson) => Payment.fromJson(paymentJson as Map<String, dynamic>))
              .toList();
          return Success(payments);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get pending payments');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get overdue payments
  static Future<Result<List<Payment>>> getOverduePayments() async {
    try {
      final response = await _dio.get('/payments/overdue');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final payments = apiResponse.data!
              .map((paymentJson) => Payment.fromJson(paymentJson as Map<String, dynamic>))
              .toList();
          return Success(payments);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get overdue payments');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get payments for a specific chit
  static Future<Result<List<Payment>>> getChitPayments(String chitId) async {
    try {
      final response = await _dio.get('/payments/chit/$chitId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final payments = apiResponse.data!
              .map((paymentJson) => Payment.fromJson(paymentJson as Map<String, dynamic>))
              .toList();
          return Success(payments);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get chit payments');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get payment statistics
  static Future<Result<Map<String, dynamic>>> getPaymentStats() async {
    try {
      final response = await _dio.get('/payments/stats');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          return Success(apiResponse.data!);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get payment statistics');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get payment statistics for a specific chit
  static Future<Result<Map<String, dynamic>>> getChitPaymentStats(String chitId) async {
    try {
      final response = await _dio.get('/payments/stats/$chitId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          return Success(apiResponse.data!);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get chit payment statistics');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle Dio errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Response timeout. Please try again.';
      case DioExceptionType.badResponse:
        if (error.response?.data is Map<String, dynamic>) {
          final data = error.response?.data as Map<String, dynamic>;
          return data['message'] ?? 'Server error occurred';
        } else {
          return 'Server error: ${error.response?.statusCode}';
        }
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      default:
        return 'Network error: ${error.message}';
    }
  }
}
