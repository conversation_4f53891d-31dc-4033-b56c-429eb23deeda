<?php
/**
 * LiteSpeed Production Environment Configuration
 * 
 * Copy this file to env.php for production deployment
 * Optimized for LiteSpeed shared hosting
 */

// Environment
define('APP_ENV', 'production');
define('APP_DEBUG', false); // ALWAYS false in production
define('APP_NAME', 'Chit Fund Manager');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'https://yourdomain.com');
define('APP_TIMEZONE', 'Asia/Kolkata');

// Database Configuration - Update with your hosting details
define('DB_HOST', 'localhost'); // Usually localhost for shared hosting
define('DB_NAME', 'your_database_name'); // Your database name from hosting panel
define('DB_USER', 'your_database_user'); // Your database username
define('DB_PASS', 'your_database_password'); // Your database password
define('DB_CHARSET', 'utf8mb4');

// LiteSpeed Database Optimizations
define('DB_PERSISTENT', true); // Use persistent connections
define('DB_TIMEOUT', 10); // Connection timeout in seconds
define('DB_POOL_SIZE', 3); // Connection pool size for shared hosting

// JWT Configuration - CHANGE THESE IN PRODUCTION!
define('JWT_SECRET', 'your-super-secret-jwt-key-minimum-32-characters-long-change-this-immediately');
define('JWT_ALGORITHM', 'HS256');
define('JWT_EXPIRY', 86400); // 24 hours in seconds

// API Configuration
define('API_VERSION', 'v1');
define('API_BASE_URL', 'https://yourdomain.com/api');
define('API_RATE_LIMIT', 60); // requests per minute
define('API_TIMEOUT', 20); // seconds

// Email Configuration - Update with your hosting email settings
define('SMTP_HOST', 'mail.yourdomain.com'); // Your hosting SMTP server
define('SMTP_PORT', 587); // Usually 587 for TLS or 465 for SSL
define('SMTP_USERNAME', '<EMAIL>'); // Your email address
define('SMTP_PASSWORD', 'your_email_password'); // Your email password
define('SMTP_ENCRYPTION', 'tls'); // tls or ssl
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Chit Fund Manager');

// File Upload Configuration - Shared hosting limits
define('UPLOAD_MAX_SIZE', 2097152); // 2MB in bytes
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'pdf']);
define('UPLOAD_PATH', __DIR__ . '/../uploads/');

// Security Configuration
define('BCRYPT_COST', 10); // Reduced for shared hosting performance
define('SESSION_LIFETIME', 7200); // 2 hours in seconds
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Cache Configuration - LiteSpeed LSCache integration
define('CACHE_ENABLED', true);
define('CACHE_TTL', 1800); // 30 minutes
define('CACHE_PREFIX', 'chitfund_');
define('LITESPEED_CACHE', true); // Enable LiteSpeed cache headers

// Logging Configuration - Optimized for shared hosting
define('LOG_LEVEL', 'ERROR'); // Only log errors in production
define('LOG_FILE', __DIR__ . '/../logs/app.log');
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_MAX_FILES', 7); // Keep only 7 days of logs
define('LOG_MAX_SIZE', 10485760); // 10MB max log file size

// Business Configuration
define('MIN_CHIT_AMOUNT', 1000);
define('MAX_CHIT_AMOUNT', 10000000);
define('MIN_CHIT_MEMBERS', 5);
define('MAX_CHIT_MEMBERS', 50);
define('DEFAULT_COMMISSION_RATE', 5.0);

// Notification Configuration
define('ENABLE_NOTIFICATIONS', true);
define('NOTIFICATION_REMINDER_DAYS', 1); // Days before chit meeting

// CORS Configuration - Update with your domain
define('CORS_ALLOWED_ORIGINS', [
    'https://yourdomain.com',
    'https://www.yourdomain.com',
    'https://app.yourdomain.com'
]);
define('CORS_ALLOWED_METHODS', ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']);
define('CORS_ALLOWED_HEADERS', ['Content-Type', 'Authorization', 'X-Requested-With', 'X-LiteSpeed-Cache-Control']);

// Rate Limiting Configuration
define('RATE_LIMIT_ENABLED', true);
define('RATE_LIMIT_REQUESTS', 60); // requests per minute
define('RATE_LIMIT_WINDOW', 60); // seconds

// Pagination Configuration
define('DEFAULT_PAGE_SIZE', 15);
define('MAX_PAGE_SIZE', 50);

// LiteSpeed Specific Optimizations
define('ENABLE_OPCACHE', true);
define('ENABLE_COMPRESSION', true);
define('ENABLE_QUERY_CACHE', true);

// Feature Flags
define('FEATURE_NOTIFICATIONS', true);
define('FEATURE_EMAIL_NOTIFICATIONS', true);
define('FEATURE_SMS_NOTIFICATIONS', false); // Disable if not configured
define('FEATURE_REPORTS', true);
define('FEATURE_EXPORTS', true);
define('FEATURE_BACKUP', false); // Usually disabled on shared hosting

// Company Information
define('COMPANY_NAME', 'Your Company Name');
define('COMPANY_ADDRESS', 'Your Company Address');
define('COMPANY_PHONE', '+91-9876543210');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_WEBSITE', 'https://yourdomain.com');

// Legal Information
define('TERMS_URL', 'https://yourdomain.com/terms');
define('PRIVACY_URL', 'https://yourdomain.com/privacy');
define('SUPPORT_EMAIL', '<EMAIL>');

// Shared Hosting Specific Settings
define('SHARED_HOSTING_MODE', true);
define('MAX_MEMORY_USAGE', '128M');
define('MAX_EXECUTION_TIME', 30);
define('ENABLE_ERROR_REPORTING', false); // Disable in production

// Performance Monitoring
define('ENABLE_PERFORMANCE_MONITORING', true);
define('PERFORMANCE_LOG_SLOW_REQUESTS', true);
define('SLOW_REQUEST_THRESHOLD', 3); // seconds

// Security Settings
define('ENABLE_RATE_LIMITING', true);
define('ENABLE_IP_BLOCKING', true);
define('ENABLE_CSRF_PROTECTION', true);
define('ENABLE_XSS_PROTECTION', true);

// Maintenance Mode
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'System is under maintenance. Please try again later.');
define('MAINTENANCE_ALLOWED_IPS', ['127.0.0.1', 'your.ip.address.here']);

// Third-party Integrations (optional)
define('GOOGLE_ANALYTICS_ID', ''); // Your GA tracking ID
define('FACEBOOK_APP_ID', ''); // For social login
define('GOOGLE_CLIENT_ID', ''); // For Google login

// Payment Gateway Configuration (if using)
define('PAYMENT_GATEWAY_ENABLED', false);
define('RAZORPAY_KEY_ID', ''); // Your Razorpay key
define('RAZORPAY_KEY_SECRET', ''); // Your Razorpay secret

// SMS Configuration (if using)
define('SMS_ENABLED', false);
define('SMS_PROVIDER', 'textlocal'); // textlocal, twilio, etc.
define('SMS_API_KEY', ''); // Your SMS provider API key
define('SMS_SENDER_ID', 'CHITFUND'); // Your sender ID

// Backup Configuration (usually disabled on shared hosting)
define('BACKUP_ENABLED', false);
define('BACKUP_PATH', __DIR__ . '/../backups/');
define('BACKUP_RETENTION_DAYS', 30);

// Error Handling
define('DISPLAY_ERRORS', false); // Never show errors in production
define('LOG_ERRORS', true);
define('ERROR_LOG_FILE', __DIR__ . '/../logs/php_errors.log');

// Session Configuration
define('SESSION_COOKIE_HTTPONLY', true);
define('SESSION_COOKIE_SECURE', true); // Only over HTTPS
define('SESSION_USE_STRICT_MODE', true);
define('SESSION_COOKIE_SAMESITE', 'Strict');

// Content Security Policy
define('CSP_ENABLED', true);
define('CSP_POLICY', "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'");

// Additional Security Headers
define('HSTS_ENABLED', true);
define('HSTS_MAX_AGE', 31536000); // 1 year
define('FRAME_OPTIONS', 'DENY');
define('CONTENT_TYPE_OPTIONS', 'nosniff');
define('XSS_PROTECTION', '1; mode=block');

// Database Connection Pool Settings
define('DB_MIN_CONNECTIONS', 1);
define('DB_MAX_CONNECTIONS', 5);
define('DB_CONNECTION_TIMEOUT', 30);
define('DB_IDLE_TIMEOUT', 300); // 5 minutes

// Query Optimization
define('ENABLE_QUERY_LOGGING', false); // Disable in production
define('SLOW_QUERY_LOG', true);
define('SLOW_QUERY_THRESHOLD', 2); // seconds

// File System Settings
define('TEMP_DIR', sys_get_temp_dir());
define('CACHE_DIR', __DIR__ . '/../cache/');
define('SESSION_SAVE_PATH', __DIR__ . '/../sessions/');

// Timezone and Locale
define('DEFAULT_LOCALE', 'en_IN');
define('SUPPORTED_LOCALES', ['en_IN', 'hi_IN', 'ta_IN']);

// API Documentation
define('API_DOCS_ENABLED', false); // Disable in production
define('API_DOCS_URL', 'https://yourdomain.com/api/docs');

// Monitoring and Analytics
define('ENABLE_ACCESS_LOGGING', true);
define('ACCESS_LOG_FILE', __DIR__ . '/../logs/access.log');
define('ENABLE_METRICS', true);
define('METRICS_ENDPOINT', '/api/metrics');

?>
