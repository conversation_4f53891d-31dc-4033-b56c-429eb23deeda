<?php
/**
 * SMS API Routes
 */

require_once __DIR__ . '/../../utils/SMSService.php';
require_once __DIR__ . '/../../utils/helpers.php';
require_once __DIR__ . '/../../utils/JWT.php';

// Get remaining path segments
$segments = array_slice(explode('/', trim($_SERVER['REQUEST_URI'], '/')), 2);
$action = $segments[0] ?? '';

// Verify authentication for all SMS operations
$userId = JWT::getUserIdFromToken();
if (!$userId) {
    sendError('Authentication required', 401);
}

switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        switch ($action) {
            case 'send':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['phone', 'message'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                // Validate phone number
                if (!SMSService::validatePhoneNumber($data['phone'])) {
                    sendError('Invalid phone number format', 400);
                }
                
                $type = $data['type'] ?? 'general';
                $result = SMSService::sendSMS($data['phone'], $data['message'], $type);
                
                if ($result) {
                    // Log activity
                    logActivity($userId, 'sms_sent', [
                        'phone' => $data['phone'],
                        'type' => $type
                    ]);
                    
                    sendSuccess(null, 'SMS sent successfully');
                } else {
                    sendError('Failed to send SMS', 500);
                }
                break;
                
            case 'send-otp':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['phone', 'otp'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                // Validate phone number
                if (!SMSService::validatePhoneNumber($data['phone'])) {
                    sendError('Invalid phone number format', 400);
                }
                
                $type = $data['type'] ?? 'verification';
                $result = SMSService::sendOTP($data['phone'], $data['otp'], $type);
                
                if ($result) {
                    // Log activity
                    logActivity($userId, 'otp_sms_sent', [
                        'phone' => $data['phone'],
                        'type' => $type
                    ]);
                    
                    sendSuccess(null, 'OTP SMS sent successfully');
                } else {
                    sendError('Failed to send OTP SMS', 500);
                }
                break;
                
            case 'send-payment-reminder':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['phone', 'chit_name', 'amount', 'due_date'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                // Validate phone number
                if (!SMSService::validatePhoneNumber($data['phone'])) {
                    sendError('Invalid phone number format', 400);
                }
                
                $result = SMSService::sendPaymentReminder(
                    $data['phone'],
                    $data['chit_name'],
                    $data['amount'],
                    $data['due_date']
                );
                
                if ($result) {
                    // Log activity
                    logActivity($userId, 'payment_reminder_sms_sent', [
                        'phone' => $data['phone'],
                        'chit_name' => $data['chit_name']
                    ]);
                    
                    sendSuccess(null, 'Payment reminder SMS sent successfully');
                } else {
                    sendError('Failed to send payment reminder SMS', 500);
                }
                break;
                
            case 'send-bidding-notification':
                $data = getRequestData();
                
                // Validate required fields
                $required = ['phone', 'chit_name', 'round_number'];
                $missing = validateRequired($data, $required);
                
                if (!empty($missing)) {
                    sendError('Missing required fields: ' . implode(', ', $missing), 400);
                }
                
                // Validate phone number
                if (!SMSService::validatePhoneNumber($data['phone'])) {
                    sendError('Invalid phone number format', 400);
                }
                
                $result = SMSService::sendBiddingNotification(
                    $data['phone'],
                    $data['chit_name'],
                    $data['round_number']
                );
                
                if ($result) {
                    // Log activity
                    logActivity($userId, 'bidding_notification_sms_sent', [
                        'phone' => $data['phone'],
                        'chit_name' => $data['chit_name']
                    ]);
                    
                    sendSuccess(null, 'Bidding notification SMS sent successfully');
                } else {
                    sendError('Failed to send bidding notification SMS', 500);
                }
                break;
                
            default:
                sendError('Invalid SMS action', 400);
                break;
        }
        break;
        
    case 'GET':
        switch ($action) {
            case 'balance':
                $balance = SMSService::getSMSBalance();
                sendSuccess($balance, 'SMS balance retrieved successfully');
                break;
                
            case 'validate-phone':
                $phone = $_GET['phone'] ?? '';
                if (!$phone) {
                    sendError('Phone number required', 400);
                }
                
                $isValid = SMSService::validatePhoneNumber($phone);
                sendSuccess(['valid' => $isValid], 'Phone number validation completed');
                break;
                
            default:
                sendError('Invalid SMS endpoint', 400);
                break;
        }
        break;
        
    default:
        sendError('Method not allowed', 405);
        break;
}
?>
