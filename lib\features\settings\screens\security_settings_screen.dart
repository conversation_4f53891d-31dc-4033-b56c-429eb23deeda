import 'package:flutter/material.dart';
import '../../../core/services/storage_service.dart';

class SecuritySettingsScreen extends StatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  bool _biometricEnabled = false;
  bool _twoFactorEnabled = false;
  bool _loginAlertsEnabled = true;
  bool _sessionTimeoutEnabled = true;
  int _sessionTimeoutMinutes = 30;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    setState(() {
      _biometricEnabled = StorageService.getBiometricEnabled();
      _twoFactorEnabled = StorageService.getTwoFactorEnabled();
      _loginAlertsEnabled = StorageService.getLoginAlertsEnabled();
      _sessionTimeoutEnabled = StorageService.getSessionTimeoutEnabled();
      _sessionTimeoutMinutes = StorageService.getSessionTimeoutMinutes();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Security Settings'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        children: [
          _buildSection(
            'Authentication',
            [
              _buildSwitchTile(
                'Biometric Login',
                'Use fingerprint or face recognition to login',
                _biometricEnabled,
                (value) async {
                  setState(() {
                    _biometricEnabled = value;
                  });
                  await StorageService.setBiometricEnabled(value);
                },
              ),
              _buildSwitchTile(
                'Two-Factor Authentication',
                'Add an extra layer of security with 2FA',
                _twoFactorEnabled,
                (value) async {
                  if (value) {
                    _showTwoFactorSetupDialog();
                  } else {
                    _showTwoFactorDisableDialog();
                  }
                },
              ),
            ],
          ),
          _buildSection(
            'Session Management',
            [
              _buildSwitchTile(
                'Auto Session Timeout',
                'Automatically logout after inactivity',
                _sessionTimeoutEnabled,
                (value) async {
                  setState(() {
                    _sessionTimeoutEnabled = value;
                  });
                  await StorageService.setSessionTimeoutEnabled(value);
                },
              ),
              ListTile(
                leading: const Icon(Icons.timer),
                title: const Text('Session Timeout'),
                subtitle: Text('Logout after $_sessionTimeoutMinutes minutes of inactivity'),
                trailing: const Icon(Icons.arrow_forward_ios),
                enabled: _sessionTimeoutEnabled,
                onTap: _sessionTimeoutEnabled ? _showTimeoutDialog : null,
              ),
            ],
          ),
          _buildSection(
            'Security Alerts',
            [
              _buildSwitchTile(
                'Login Alerts',
                'Get notified of new login attempts',
                _loginAlertsEnabled,
                (value) async {
                  setState(() {
                    _loginAlertsEnabled = value;
                  });
                  await StorageService.setLoginAlertsEnabled(value);
                },
              ),
            ],
          ),
          _buildSection(
            'Account Security',
            [
              ListTile(
                leading: const Icon(Icons.lock_reset),
                title: const Text('Change Password'),
                subtitle: const Text('Update your account password'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showChangePasswordDialog();
                },
              ),
              ListTile(
                leading: const Icon(Icons.devices),
                title: const Text('Active Sessions'),
                subtitle: const Text('Manage logged in devices'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showActiveSessionsDialog();
                },
              ),
              ListTile(
                leading: const Icon(Icons.security),
                title: const Text('Security Audit'),
                subtitle: const Text('Review your account security'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showSecurityAuditDialog();
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton.icon(
              onPressed: () {
                _showLogoutAllDevicesDialog();
              },
              icon: const Icon(Icons.logout),
              label: const Text('Logout All Devices'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged, {
    bool enabled = true,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: TextStyle(
          color: enabled ? null : Theme.of(context).disabledColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: enabled ? null : Theme.of(context).disabledColor,
        ),
      ),
      value: enabled ? value : false,
      onChanged: enabled ? onChanged : null,
    );
  }

  void _showTwoFactorSetupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Setup Two-Factor Authentication'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Two-factor authentication adds an extra layer of security to your account.'),
            SizedBox(height: 16),
            Text('This feature will be available in a future update.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showTwoFactorDisableDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Disable Two-Factor Authentication'),
        content: const Text('Are you sure you want to disable two-factor authentication? This will make your account less secure.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              setState(() {
                _twoFactorEnabled = false;
              });
              await StorageService.setTwoFactorEnabled(false);
            },
            child: const Text('Disable'),
          ),
        ],
      ),
    );
  }

  void _showTimeoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Session Timeout'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Choose how long to wait before automatically logging out:'),
            const SizedBox(height: 16),
            ...([15, 30, 60, 120].map((minutes) => RadioListTile<int>(
              title: Text('$minutes minutes'),
              value: minutes,
              groupValue: _sessionTimeoutMinutes,
              onChanged: (value) {
                Navigator.of(context).pop();
                setState(() {
                  _sessionTimeoutMinutes = value!;
                });
                StorageService.setSessionTimeoutMinutes(value!);
              },
            ))),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Password'),
        content: const Text('This feature will redirect you to the change password screen.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showActiveSessionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Active Sessions'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('View and manage devices that are currently logged into your account.'),
            SizedBox(height: 16),
            Text('This feature will be available in a future update.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSecurityAuditDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Security Audit'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Review your account security settings and recent activity.'),
            SizedBox(height: 16),
            Text('This feature will be available in a future update.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showLogoutAllDevicesDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout All Devices'),
        content: const Text('This will log you out of all devices. You will need to login again on each device.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // TODO: Implement logout all devices
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Logout all devices feature coming soon'),
                ),
              );
            },
            child: const Text('Logout All'),
          ),
        ],
      ),
    );
  }
}
