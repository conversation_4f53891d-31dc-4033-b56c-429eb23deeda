import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/user_model.dart';
import 'api_service.dart';
import 'storage_service.dart';

class UserService {
  static late Dio _dio;

  static void initialize() {
    ApiService.initialize();
    _dio = Dio(BaseOptions(
      baseUrl: ApiService.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token to requests
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
      ),
    );
  }

  /// Get current user profile
  static Future<Result<User>> getProfile() async {
    try {
      final response = await _dio.get('/users/profile');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final user = User.fromJson(apiResponse.data!);
          
          // Update local storage
          await StorageService.saveUserData(apiResponse.data!);
          
          return Success(user);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch profile');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Update user profile
  static Future<Result<User>> updateProfile({
    String? name,
    String? email,
    String? phone,
    String? profileImage,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (name != null) data['name'] = name;
      if (email != null) data['email'] = email;
      if (phone != null) data['phone'] = phone;
      if (profileImage != null) data['profile_image'] = profileImage;

      final response = await _dio.put('/users/profile', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final user = User.fromJson(apiResponse.data!);
          
          // Update local storage
          await StorageService.saveUserData(apiResponse.data!);
          
          return Success(user);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to update profile');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Search users by name, email, or phone
  static Future<Result<List<User>>> searchUsers({
    required String query,
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get('/users/search', queryParameters: {
        'query': query,
        'limit': limit,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final users = apiResponse.data!
              .map((userJson) => User.fromJson(userJson as Map<String, dynamic>))
              .toList();
          return Success(users);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to search users');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get user by ID
  static Future<Result<User>> getUserById(String userId) async {
    try {
      final response = await _dio.get('/users/$userId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final user = User.fromJson(apiResponse.data!);
          return Success(user);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch user');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Upload profile image
  static Future<Result<String>> uploadProfileImage(String imagePath) async {
    try {
      final formData = FormData.fromMap({
        'profile_image': await MultipartFile.fromFile(imagePath),
      });

      final response = await _dio.post('/files/upload-profile-image', data: formData);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final imageUrl = apiResponse.data!['image_url'] as String;
          return Success(imageUrl);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to upload image');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Delete account
  static Future<Result<void>> deleteAccount() async {
    try {
      final response = await _dio.delete('/users/account');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          // Clear local storage
          await StorageService.clearAllData();
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to delete account');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get user statistics
  static Future<Result<Map<String, dynamic>>> getUserStatistics() async {
    try {
      final response = await _dio.get('/users/statistics');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          return Success(apiResponse.data!);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch statistics');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Update notification preferences
  static Future<Result<void>> updateNotificationPreferences({
    bool? emailNotifications,
    bool? pushNotifications,
    bool? smsNotifications,
    bool? reminderNotifications,
    bool? biddingNotifications,
    bool? paymentNotifications,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (emailNotifications != null) data['email_notifications'] = emailNotifications;
      if (pushNotifications != null) data['push_notifications'] = pushNotifications;
      if (smsNotifications != null) data['sms_notifications'] = smsNotifications;
      if (reminderNotifications != null) data['reminder_notifications'] = reminderNotifications;
      if (biddingNotifications != null) data['bidding_notifications'] = biddingNotifications;
      if (paymentNotifications != null) data['payment_notifications'] = paymentNotifications;

      final response = await _dio.put('/users/notification-preferences', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to update notification preferences');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get notification preferences
  static Future<Result<Map<String, dynamic>>> getNotificationPreferences() async {
    try {
      final response = await _dio.get('/users/notification-preferences');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          return Success(apiResponse.data!);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch notification preferences');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle Dio errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Response timeout. Please try again.';
      case DioExceptionType.badResponse:
        if (error.response?.data is Map<String, dynamic>) {
          final data = error.response?.data as Map<String, dynamic>;
          return data['message'] ?? 'Server error occurred';
        } else {
          return 'Server error: ${error.response?.statusCode}';
        }
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      default:
        return 'Network error: ${error.message}';
    }
  }
}
