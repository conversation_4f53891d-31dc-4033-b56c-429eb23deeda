-- Sample Data for Chit Fund Management System
-- This file contains sample data for testing and development purposes

USE chit_fund_db;

-- Sample users (password for all: password123)
INSERT INTO users (name, email, phone, password_hash, role, is_active, email_verified) VALUES
('<PERSON><PERSON>', 'r<PERSON><PERSON>@example.com', '+919876543210', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'organizer', TRUE, TRUE),
('<PERSON><PERSON>', '<EMAIL>', '+919876543211', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'member', TRUE, TRUE),
('Amit Patel', '<EMAIL>', '+919876543212', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'member', TRUE, TRUE),
('<PERSON><PERSON>', '<EMAIL>', '+919876543213', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'member', TRUE, TRUE),
('Vikram Singh', '<EMAIL>', '+919876543214', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'member', TRUE, TRUE),
('Meera Joshi', '<EMAIL>', '+919876543215', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'member', TRUE, TRUE),
('Ravi Agarwal', '<EMAIL>', '+919876543216', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'organizer', TRUE, TRUE),
('Kavita Reddy', '<EMAIL>', '+919876543217', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'member', TRUE, TRUE),
('Suresh Yadav', '<EMAIL>', '+919876543218', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'member', TRUE, TRUE),
('Anita Verma', '<EMAIL>', '+919876543219', '$2y$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/UnxPr.3UNzbaK4.6G', 'member', TRUE, TRUE);

-- Sample chits
INSERT INTO chits (name, description, total_amount, number_of_members, frequency, start_date, status, owner_id, commission_percentage) VALUES
('Monthly Savings Chit', 'A monthly chit fund for regular savings', 50000.00, 10, 'monthly', '2025-01-01', 'active', 2, 2.00),
('Quarterly Investment Chit', 'Quarterly chit fund for larger investments', 200000.00, 8, 'quarterly', '2025-01-01', 'active', 8, 2.50),
('Festival Special Chit', 'Special chit fund for festival expenses', 100000.00, 12, 'monthly', '2025-02-01', 'draft', 2, 1.50),
('Business Growth Chit', 'Chit fund for business expansion', 500000.00, 20, 'monthly', '2025-03-01', 'draft', 8, 3.00);

-- Sample chit members for Monthly Savings Chit (chit_id = 1)
INSERT INTO chit_members (chit_id, user_id, member_number, status) VALUES
(1, 2, 1, 'active'),  -- Rajesh (owner)
(1, 3, 2, 'active'),  -- Priya
(1, 4, 3, 'active'),  -- Amit
(1, 5, 4, 'active'),  -- Sunita
(1, 6, 5, 'active'),  -- Vikram
(1, 7, 6, 'active'),  -- Meera
(1, 9, 7, 'active'),  -- Kavita
(1, 10, 8, 'active'), -- Suresh
(1, 11, 9, 'active'), -- Anita
(1, 1, 10, 'active'); -- Admin (for testing)

-- Sample chit members for Quarterly Investment Chit (chit_id = 2)
INSERT INTO chit_members (chit_id, user_id, member_number, status) VALUES
(2, 8, 1, 'active'),  -- Ravi (owner)
(2, 2, 2, 'active'),  -- Rajesh
(2, 3, 3, 'active'),  -- Priya
(2, 4, 4, 'active'),  -- Amit
(2, 5, 5, 'active'),  -- Sunita
(2, 6, 6, 'active'),  -- Vikram
(2, 7, 7, 'active'),  -- Meera
(2, 9, 8, 'active');  -- Kavita

-- Sample bidding rounds for Monthly Savings Chit
INSERT INTO bidding_rounds (chit_id, round_number, meeting_date, starting_bid, winner_id, winning_bid, final_amount, commission_amount, status) VALUES
(1, 1, '2025-01-15', 2000.00, 3, 1500.00, 48500.00, 1000.00, 'completed'),
(1, 2, '2025-02-15', 2000.00, 5, 1800.00, 48200.00, 1000.00, 'completed'),
(1, 3, '2025-03-15', 2000.00, NULL, NULL, NULL, NULL, 'pending');

-- Sample bids for completed rounds
INSERT INTO bids (bidding_round_id, member_id, bid_amount, bid_sequence, is_winning_bid) VALUES
-- Round 1 bids
(1, 3, 1500.00, 1, TRUE),   -- Priya's winning bid
(1, 4, 1600.00, 2, FALSE),  -- Amit's bid
(1, 5, 1700.00, 3, FALSE),  -- Sunita's bid

-- Round 2 bids
(2, 5, 1800.00, 1, TRUE),   -- Sunita's winning bid
(2, 6, 1900.00, 2, FALSE),  -- Vikram's bid
(2, 7, 2000.00, 3, FALSE);  -- Meera's bid

-- Sample payments for completed rounds
INSERT INTO payments (chit_id, bidding_round_id, member_id, amount, payment_type, payment_status, due_date, paid_date) VALUES
-- Round 1 payments (all members pay contribution except winner)
(1, 1, 1, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-15'),
(1, 1, 2, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-15'),
(1, 1, 4, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-16'),
(1, 1, 5, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-15'),
(1, 1, 6, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-17'),
(1, 1, 7, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-15'),
(1, 1, 8, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-18'),
(1, 1, 9, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-15'),
(1, 1, 10, 4850.00, 'contribution', 'paid', '2025-01-15', '2025-01-15'),
-- Winner receives the amount
(1, 1, 3, 48500.00, 'winning_amount', 'paid', '2025-01-15', '2025-01-15'),
-- Commission to organizer
(1, 1, 2, 1000.00, 'commission', 'paid', '2025-01-15', '2025-01-15'),

-- Round 2 payments
(1, 2, 1, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-15'),
(1, 2, 2, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-15'),
(1, 2, 3, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-16'),
(1, 2, 4, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-15'),
(1, 2, 6, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-17'),
(1, 2, 7, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-15'),
(1, 2, 8, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-18'),
(1, 2, 9, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-15'),
(1, 2, 10, 4820.00, 'contribution', 'paid', '2025-02-15', '2025-02-15'),
-- Winner receives the amount
(1, 2, 5, 48200.00, 'winning_amount', 'paid', '2025-02-15', '2025-02-15'),
-- Commission to organizer
(1, 2, 2, 1000.00, 'commission', 'paid', '2025-02-15', '2025-02-15');

-- Sample notifications
INSERT INTO notifications (user_id, chit_id, title, message, type, is_read, created_at) VALUES
(3, 1, 'Chit Meeting Reminder', 'Monthly Savings Chit meeting is scheduled for tomorrow at 10:00 AM', 'reminder', TRUE, '2025-03-14 10:00:00'),
(4, 1, 'Chit Meeting Reminder', 'Monthly Savings Chit meeting is scheduled for tomorrow at 10:00 AM', 'reminder', FALSE, '2025-03-14 10:00:00'),
(5, 1, 'Payment Due', 'Your contribution for Monthly Savings Chit is due on 15th March', 'payment', FALSE, '2025-03-13 09:00:00'),
(2, 1, 'Bidding Started', 'Bidding has started for Monthly Savings Chit Round 3', 'bidding', TRUE, '2025-03-15 10:00:00');

-- Sample activity logs
INSERT INTO activity_logs (user_id, chit_id, action, details, ip_address, created_at) VALUES
(2, 1, 'chit_created', '{"chit_name": "Monthly Savings Chit", "amount": 50000}', '*************', '2024-12-15 10:00:00'),
(3, 1, 'member_joined', '{"member_number": 2}', '*************', '2024-12-16 11:00:00'),
(2, 1, 'bidding_started', '{"round": 1, "starting_bid": 2000}', '*************', '2025-01-15 10:00:00'),
(3, 1, 'bid_placed', '{"round": 1, "bid_amount": 1500}', '*************', '2025-01-15 10:15:00'),
(2, 1, 'bidding_completed', '{"round": 1, "winner": "Priya Sharma", "winning_bid": 1500}', '*************', '2025-01-15 10:30:00');

-- Update some members as winners
UPDATE chit_members SET has_won = TRUE, winning_round = 1, winning_amount = 48500.00 WHERE chit_id = 1 AND user_id = 3;
UPDATE chit_members SET has_won = TRUE, winning_round = 2, winning_amount = 48200.00 WHERE chit_id = 1 AND user_id = 5;
