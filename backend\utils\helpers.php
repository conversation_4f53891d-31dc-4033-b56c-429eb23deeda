<?php
/**
 * Helper Functions
 *
 * Common utility functions used throughout the application
 */

// Import required dependencies
require_once __DIR__ . '/JWT.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/User.php';

/**
 * Send JSON response
 * 
 * @param mixed $data Response data
 * @param int $statusCode HTTP status code
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Send success response
 * 
 * @param mixed $data Response data
 * @param string $message Success message
 * @param int $statusCode HTTP status code
 */
function sendSuccess($data = null, $message = 'Success', $statusCode = 200) {
    $response = [
        'success' => true,
        'message' => $message,
        'data' => $data
    ];
    
    sendResponse($response, $statusCode);
}

/**
 * Send error response
 * 
 * @param string $message Error message
 * @param int $statusCode HTTP status code
 * @param mixed $errors Additional error details
 */
function sendError($message = 'An error occurred', $statusCode = 400, $errors = null) {
    $response = [
        'success' => false,
        'message' => $message
    ];
    
    if ($errors !== null) {
        $response['errors'] = $errors;
    }
    
    sendResponse($response, $statusCode);
}

/**
 * Get request data (JSON or form data)
 * 
 * @return array Request data
 */
function getRequestData() {
    $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);
        return $data ?? [];
    }
    
    return array_merge($_GET, $_POST);
}

/**
 * Validate required fields
 * 
 * @param array $data Input data
 * @param array $required Required field names
 * @return array Missing field names
 */
function validateRequired($data, $required) {
    $missing = [];
    
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    
    return $missing;
}

/**
 * Sanitize input data
 * 
 * @param string $input Input string
 * @return string Sanitized string
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 * 
 * @param string $email Email address
 * @return bool True if valid, false otherwise
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Indian format)
 * 
 * @param string $phone Phone number
 * @return bool True if valid, false otherwise
 */
function validatePhone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Check if it's a valid Indian mobile number
    return preg_match('/^[6-9]\d{9}$/', $phone) || 
           preg_match('/^91[6-9]\d{9}$/', $phone) ||
           preg_match('/^\+91[6-9]\d{9}$/', $phone);
}

/**
 * Generate random string
 * 
 * @param int $length String length
 * @param string $characters Character set to use
 * @return string Random string
 */
function generateRandomString($length = 10, $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ') {
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * Hash password securely with enhanced security
 *
 * @param string $password Plain text password
 * @return string Hashed password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
}

/**
 * Verify password
 * 
 * @param string $password Plain text password
 * @param string $hash Hashed password
 * @return bool True if password matches, false otherwise
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Log activity
 * 
 * @param int $userId User ID
 * @param string $action Action performed
 * @param array $details Additional details
 * @param int $chitId Chit ID (optional)
 */
function logActivity($userId, $action, $details = [], $chitId = null) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        if (!$conn) {
            return;
        }
        
        $sql = "INSERT INTO activity_logs (user_id, chit_id, action, details, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $userId,
            $chitId,
            $action,
            json_encode($details),
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
    } catch (Exception $e) {
        error_log("Activity logging failed: " . $e->getMessage());
    }
}

/**
 * Format currency amount
 * 
 * @param float $amount Amount to format
 * @param string $currency Currency symbol
 * @return string Formatted amount
 */
function formatCurrency($amount, $currency = '₹') {
    return $currency . number_format($amount, 2);
}

/**
 * Format date
 * 
 * @param string $date Date string
 * @param string $format Output format
 * @return string Formatted date
 */
function formatDate($date, $format = 'd/m/Y') {
    return date($format, strtotime($date));
}

/**
 * Calculate age from date of birth
 * 
 * @param string $dob Date of birth
 * @return int Age in years
 */
function calculateAge($dob) {
    $birthDate = new DateTime($dob);
    $today = new DateTime('today');
    return $birthDate->diff($today)->y;
}

/**
 * Generate unique filename
 * 
 * @param string $originalName Original filename
 * @return string Unique filename
 */
function generateUniqueFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $filename = pathinfo($originalName, PATHINFO_FILENAME);
    $timestamp = time();
    $random = generateRandomString(8);
    
    return $filename . '_' . $timestamp . '_' . $random . '.' . $extension;
}

/**
 * Validate file upload
 * 
 * @param array $file $_FILES array element
 * @param array $allowedTypes Allowed MIME types
 * @param int $maxSize Maximum file size in bytes
 * @return array Validation result
 */
function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880) { // 5MB default
    $errors = [];
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors[] = 'File upload failed';
        return ['valid' => false, 'errors' => $errors];
    }
    
    if ($file['size'] > $maxSize) {
        $errors[] = 'File size exceeds maximum allowed size';
    }
    
    if (!empty($allowedTypes) && !in_array($file['type'], $allowedTypes)) {
        $errors[] = 'File type not allowed';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * Send email notification
 * 
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $message Email message
 * @param array $headers Additional headers
 * @return bool True if sent successfully, false otherwise
 */
function sendEmail($to, $subject, $message, $headers = []) {
    // Basic email implementation
    // In production, use a proper email service like PHPMailer or SwiftMailer
    
    $defaultHeaders = [
        'From: <EMAIL>',
        'Reply-To: <EMAIL>',
        'Content-Type: text/html; charset=UTF-8'
    ];
    
    $allHeaders = array_merge($defaultHeaders, $headers);
    
    return mail($to, $subject, $message, implode("\r\n", $allHeaders));
}

/**
 * Send SMS notification
 * 
 * @param string $phone Phone number
 * @param string $message SMS message
 * @return bool True if sent successfully, false otherwise
 */
function sendSMS($phone, $message) {
    // SMS implementation would go here
    // Integrate with SMS service provider like Twilio, MSG91, etc.
    
    // For now, just log the SMS
    error_log("SMS to $phone: $message");
    
    return true;
}

/**
 * Get client IP address
 * 
 * @return string Client IP address
 */
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Check if request is from mobile device
 * 
 * @return bool True if mobile, false otherwise
 */
function isMobileDevice() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    return preg_match('/(android|iphone|ipad|mobile)/i', $userAgent);
}

/**
 * Rate limiting check
 * 
 * @param string $key Rate limit key
 * @param int $limit Maximum requests
 * @param int $window Time window in seconds
 * @return bool True if within limit, false otherwise
 */
function checkRateLimit($key, $limit = 60, $window = 3600) {
    // Simple file-based rate limiting
    // In production, use Redis or Memcached
    
    $filename = sys_get_temp_dir() . '/rate_limit_' . md5($key);
    $now = time();
    
    if (file_exists($filename)) {
        $data = json_decode(file_get_contents($filename), true);
        
        // Clean old entries
        $data = array_filter($data, function($timestamp) use ($now, $window) {
            return ($now - $timestamp) < $window;
        });
        
        if (count($data) >= $limit) {
            return false;
        }
        
        $data[] = $now;
    } else {
        $data = [$now];
    }
    
    file_put_contents($filename, json_encode($data));

    return true;
}

/**
 * Authenticate user from JWT token with enhanced security
 *
 * @return User|false User object or false if not authenticated
 */
function authenticateUser() {
    $token = JWT::getTokenFromHeader();

    if (!$token) {
        return false;
    }

    $payload = JWT::decode($token);

    if (!$payload || !isset($payload['user_id'])) {
        return false;
    }

    // Validate token type
    if (!isset($payload['type']) || $payload['type'] !== 'access_token') {
        return false;
    }

    // Get user from database
    $user = new User();
    if (!$user->findById($payload['user_id'])) {
        return false;
    }

    // Check if user is active
    if ($user->status !== 'active') {
        return false;
    }

    // Check for token blacklist (if implemented)
    if (isTokenBlacklisted($token)) {
        return false;
    }

    // Rate limiting check per user
    if (!checkUserRateLimit($user->id)) {
        return false;
    }

    return $user;
}

/**
 * Check if token is blacklisted
 */
function isTokenBlacklisted($token) {
    try {
        $db = new Database(); // Unified database with smart LiteSpeed detection
        $conn = $db->getConnection();

        $sql = "SELECT id FROM token_blacklist WHERE token_hash = ? AND expires_at > NOW()";
        $stmt = $conn->prepare($sql);
        $stmt->execute([hash('sha256', $token)]);

        return $stmt->fetch() !== false;
    } catch (Exception $e) {
        error_log("Token blacklist check error: " . $e->getMessage());
        return false;
    }
}

/**
 * Add token to blacklist
 */
function blacklistToken($token, $expiresAt = null) {
    try {
        $db = new Database(); // Unified database with smart LiteSpeed detection
        $conn = $db->getConnection();

        $expiresAt = $expiresAt ?? date('Y-m-d H:i:s', time() + 86400); // 24 hours default

        $sql = "INSERT INTO token_blacklist (token_hash, expires_at) VALUES (?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([hash('sha256', $token), $expiresAt]);

        return true;
    } catch (Exception $e) {
        error_log("Token blacklist error: " . $e->getMessage());
        return false;
    }
}

/**
 * Check user-specific rate limiting
 */
function checkUserRateLimit($userId) {
    try {
        $cacheKey = "user_rate_limit_{$userId}";
        $cacheFile = __DIR__ . "/../cache/{$cacheKey}.txt";
        $currentTime = time();
        $limit = 100; // 100 requests per minute per user

        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data && $currentTime - $data['timestamp'] < 60) {
                if ($data['count'] >= $limit) {
                    return false;
                }
                $data['count']++;
            } else {
                $data = ['timestamp' => $currentTime, 'count' => 1];
            }
        } else {
            $data = ['timestamp' => $currentTime, 'count' => 1];
        }

        file_put_contents($cacheFile, json_encode($data));
        return true;
    } catch (Exception $e) {
        error_log("User rate limit check error: " . $e->getMessage());
        return true; // Allow on error to prevent blocking legitimate users
    }
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION)) {
        session_start();
    }

    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    $_SESSION['csrf_token_time'] = time();

    return $token;
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    if (!isset($_SESSION)) {
        session_start();
    }

    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }

    // Check if token is expired (30 minutes)
    if (time() - $_SESSION['csrf_token_time'] > 1800) {
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
        return false;
    }

    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Enhanced input sanitization with XSS protection
 */
function sanitizeInputAdvanced($input, $allowHtml = false) {
    if (is_array($input)) {
        return array_map(function($item) use ($allowHtml) {
            return sanitizeInputAdvanced($item, $allowHtml);
        }, $input);
    }

    // Remove null bytes
    $input = str_replace(chr(0), '', $input);

    // Trim whitespace
    $input = trim($input);

    if (!$allowHtml) {
        // Remove HTML tags and encode special characters
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    } else {
        // Allow specific HTML tags but sanitize
        $input = strip_tags($input, '<p><br><strong><em><ul><ol><li>');
    }

    return $input;
}

/**
 * Detect SQL injection patterns
 */
function detectSQLInjection($input) {
    $patterns = [
        '/(\bunion\b.*\bselect\b)/i',
        '/(\bselect\b.*\bfrom\b)/i',
        '/(\binsert\b.*\binto\b)/i',
        '/(\bupdate\b.*\bset\b)/i',
        '/(\bdelete\b.*\bfrom\b)/i',
        '/(\bdrop\b.*\btable\b)/i',
        '/(\bor\b.*=.*)/i',
        '/(\band\b.*=.*)/i',
        '/(\'.*or.*\'.*=.*\')/i',
        '/(\".*or.*\".*=.*\")/i'
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $input)) {
            return true;
        }
    }

    return false;
}

?>
