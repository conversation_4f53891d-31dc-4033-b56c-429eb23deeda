<?php
/**
 * Security Middleware - Comprehensive API Security
 * 
 * Provides multiple layers of security protection:
 * - Rate limiting
 * - SQL injection detection
 * - XSS protection
 * - CSRF protection
 * - Input validation
 * - Suspicious activity monitoring
 */

require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../config/database.php';

class SecurityMiddleware {
    
    private static $rateLimits = [
        'auth' => ['requests' => 10, 'window' => 60], // 10 requests per minute for auth
        'api' => ['requests' => 60, 'window' => 60],  // 60 requests per minute for API
        'upload' => ['requests' => 5, 'window' => 60] // 5 uploads per minute
    ];
    
    /**
     * Apply all security checks
     */
    public static function apply($endpoint = 'api') {
        // 1. Rate limiting
        if (!self::checkRateLimit($endpoint)) {
            self::logSecurityEvent(null, 'rate_limit_exceeded', [
                'endpoint' => $endpoint,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            http_response_code(429);
            echo json_encode([
                'success' => false,
                'message' => 'Rate limit exceeded. Please try again later.',
                'error_code' => 'RATE_LIMIT_EXCEEDED'
            ]);
            exit;
        }
        
        // 2. Input validation and sanitization
        self::validateAndSanitizeInput();
        
        // 3. SQL injection detection
        if (self::detectSQLInjection()) {
            self::logSecurityEvent(null, 'sql_injection_attempt', [
                'request_data' => $_REQUEST,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Invalid request detected.',
                'error_code' => 'INVALID_REQUEST'
            ]);
            exit;
        }
        
        // 4. XSS protection
        if (self::detectXSS()) {
            self::logSecurityEvent(null, 'xss_attempt', [
                'request_data' => $_REQUEST,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Invalid request detected.',
                'error_code' => 'INVALID_REQUEST'
            ]);
            exit;
        }
        
        // 5. Set security headers
        self::setSecurityHeaders();
        
        // 6. CSRF protection for state-changing operations
        if (in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT', 'DELETE'])) {
            // Skip CSRF for API endpoints using JWT (they have their own protection)
            if (!self::isAPIEndpoint()) {
                if (!self::validateCSRFToken()) {
                    http_response_code(403);
                    echo json_encode([
                        'success' => false,
                        'message' => 'CSRF token validation failed.',
                        'error_code' => 'CSRF_VALIDATION_FAILED'
                    ]);
                    exit;
                }
            }
        }
    }
    
    /**
     * Check rate limiting
     */
    private static function checkRateLimit($endpoint) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $identifier = $ip;
        
        // Use user ID if authenticated
        $user = authenticateUser();
        if ($user) {
            $identifier = "user_{$user->id}";
        }
        
        $limits = self::$rateLimits[$endpoint] ?? self::$rateLimits['api'];
        
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            // Check if database connection is available
            if (!$conn) {
                error_log("Database connection failed in SecurityMiddleware");
                return true; // Allow request to proceed if DB is down
            }

            $windowStart = date('Y-m-d H:i:s', time() - $limits['window']);

            // Clean old entries
            $sql = "DELETE FROM rate_limits WHERE window_start < ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$windowStart]);
            
            // Check current rate
            $sql = "SELECT request_count FROM rate_limits 
                    WHERE identifier = ? AND endpoint = ? AND window_start >= ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$identifier, $endpoint, $windowStart]);
            
            $currentCount = 0;
            while ($row = $stmt->fetch()) {
                $currentCount += $row['request_count'];
            }
            
            if ($currentCount >= $limits['requests']) {
                return false;
            }
            
            // Record this request
            $currentWindow = date('Y-m-d H:i:00'); // Round to minute
            $sql = "INSERT INTO rate_limits (identifier, endpoint, request_count, window_start) 
                    VALUES (?, ?, 1, ?) 
                    ON DUPLICATE KEY UPDATE request_count = request_count + 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$identifier, $endpoint, $currentWindow]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Rate limit check error: " . $e->getMessage());
            return true; // Allow on error to prevent blocking legitimate users
        }
    }
    
    /**
     * Validate and sanitize all input
     */
    private static function validateAndSanitizeInput() {
        // Sanitize GET parameters
        foreach ($_GET as $key => $value) {
            $_GET[$key] = sanitizeInput($value);
        }
        
        // Sanitize POST parameters
        foreach ($_POST as $key => $value) {
            $_POST[$key] = sanitizeInput($value);
        }
        
        // Sanitize JSON input
        $input = file_get_contents('php://input');
        if (!empty($input)) {
            $json = json_decode($input, true);
            if ($json) {
                foreach ($json as $key => $value) {
                    $json[$key] = sanitizeInput($value);
                }
                // Store sanitized JSON back
                $_POST = array_merge($_POST, $json);
            }
        }
    }
    
    /**
     * Detect SQL injection attempts
     */
    private static function detectSQLInjection() {
        $allInput = array_merge($_GET, $_POST);
        $input = file_get_contents('php://input');
        if (!empty($input)) {
            $allInput['_raw_input'] = $input;
        }
        
        $patterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bupdate\b.*\bset\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\bor\b.*=.*)/i',
            '/(\band\b.*=.*)/i',
            '/(\'.*or.*\'.*=.*\')/i',
            '/(\".*or.*\".*=.*\")/i',
            '/(\bexec\b|\bexecute\b)/i',
            '/(\bsp_\w+)/i',
            '/(\bxp_\w+)/i'
        ];
        
        foreach ($allInput as $value) {
            if (is_string($value)) {
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Detect XSS attempts
     */
    private static function detectXSS() {
        $allInput = array_merge($_GET, $_POST);
        $input = file_get_contents('php://input');
        if (!empty($input)) {
            $allInput['_raw_input'] = $input;
        }
        
        $patterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe\b/i',
            '/<object\b/i',
            '/<embed\b/i',
            '/<link\b/i',
            '/<meta\b/i',
            '/expression\s*\(/i',
            '/vbscript:/i',
            '/data:text\/html/i'
        ];
        
        foreach ($allInput as $value) {
            if (is_string($value)) {
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Set security headers
     */
    private static function setSecurityHeaders() {
        // Prevent XSS attacks
        header('X-XSS-Protection: 1; mode=block');
        
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Prevent clickjacking
        header('X-Frame-Options: DENY');
        
        // Referrer policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Content Security Policy
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self'");
        
        // HSTS (only if HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
        
        // Remove server information
        header_remove('X-Powered-By');
        header_remove('Server');
    }
    
    /**
     * Validate CSRF token
     */
    private static function validateCSRFToken() {
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? null;
        
        if (!$token) {
            return false;
        }
        
        return verifyCSRFToken($token);
    }
    
    /**
     * Check if this is an API endpoint
     */
    private static function isAPIEndpoint() {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($uri, '/api/') !== false;
    }
    
    /**
     * Log security events
     */
    private static function logSecurityEvent($userId, $event, $details = []) {
        try {
            $db = new Database(); // Unified database with smart LiteSpeed detection
            $conn = $db->getConnection();

            // Check if database connection is available
            if (!$conn) {
                error_log("Database connection failed in logSecurityEvent");
                return; // Skip logging if DB is down
            }

            $severity = 'medium';
            if (in_array($event, ['sql_injection_attempt', 'xss_attempt'])) {
                $severity = 'high';
            } elseif ($event === 'rate_limit_exceeded') {
                $severity = 'low';
            }
            
            $securityDetails = [
                'event' => $event,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? null,
                'request_uri' => $_SERVER['REQUEST_URI'] ?? null,
                'timestamp' => date('Y-m-d H:i:s'),
                'details' => $details
            ];
            
            $sql = "INSERT INTO security_logs (user_id, event_type, details, ip_address, user_agent, severity) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $userId,
                $event,
                json_encode($securityDetails),
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null,
                $severity
            ]);
            
            // Log critical events to file as well
            if ($severity === 'high') {
                error_log("SECURITY ALERT: {$event} from IP " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
            }
            
        } catch (Exception $e) {
            error_log("Security logging failed: " . $e->getMessage());
        }
    }
}

?>
