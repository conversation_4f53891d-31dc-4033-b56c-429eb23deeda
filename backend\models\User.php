<?php
/**
 * User Model
 *
 * Handles user-related database operations
 */

// Import required dependencies
require_once __DIR__ . '/../config/database.php';

class User {
    private $db;
    private $conn;
    
    // User properties
    public $id;
    public $name;
    public $email;
    public $phone;
    public $password_hash;
    public $profile_image;
    public $role;
    public $is_active;
    public $email_verified;
    public $phone_verified;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * Create a new user
     * 
     * @return bool True on success, false on failure
     */
    public function create() {
        $sql = "INSERT INTO users (name, email, phone, password_hash, role, is_active) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            // Hash password
            $hashedPassword = password_hash($this->password_hash, PASSWORD_BCRYPT, ['cost' => 12]);
            
            $result = $stmt->execute([
                $this->name,
                $this->email,
                $this->phone,
                $hashedPassword,
                $this->role ?? 'member',
                $this->is_active ?? true
            ]);
            
            if ($result) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("User creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find user by ID
     * 
     * @param int $id User ID
     * @return bool True if found, false otherwise
     */
    public function findById($id) {
        $sql = "SELECT * FROM users WHERE id = ? AND is_active = 1";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find user by ID failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find user by email
     * 
     * @param string $email User email
     * @return bool True if found, false otherwise
     */
    public function findByEmail($email) {
        $sql = "SELECT * FROM users WHERE email = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$email]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find user by email failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find user by phone
     * 
     * @param string $phone User phone
     * @return bool True if found, false otherwise
     */
    public function findByPhone($phone) {
        $sql = "SELECT * FROM users WHERE phone = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$phone]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find user by phone failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update user
     * 
     * @return bool True on success, false on failure
     */
    public function update() {
        $sql = "UPDATE users SET name = ?, email = ?, phone = ?, profile_image = ?, 
                updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            return $stmt->execute([
                $this->name,
                $this->email,
                $this->phone,
                $this->profile_image,
                $this->id
            ]);
        } catch (PDOException $e) {
            error_log("User update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update password
     * 
     * @param string $newPassword New password
     * @return bool True on success, false on failure
     */
    public function updatePassword($newPassword) {
        $sql = "UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => 12]);
            
            return $stmt->execute([$hashedPassword, $this->id]);
        } catch (PDOException $e) {
            error_log("Password update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Verify password
     * 
     * @param string $password Password to verify
     * @return bool True if password is correct, false otherwise
     */
    public function verifyPassword($password) {
        return password_verify($password, $this->password_hash);
    }
    
    /**
     * Deactivate user
     * 
     * @return bool True on success, false on failure
     */
    public function deactivate() {
        $sql = "UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("User deactivation failed: " . $e->getMessage());
            return false;
        }
    }
    

    

    
    /**
     * Check if email exists
     * 
     * @param string $email Email to check
     * @param int $excludeId User ID to exclude from check
     * @return bool True if exists, false otherwise
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT id FROM users WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Email exists check failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if phone exists
     * 
     * @param string $phone Phone to check
     * @param int $excludeId User ID to exclude from check
     * @return bool True if exists, false otherwise
     */
    public function phoneExists($phone, $excludeId = null) {
        $sql = "SELECT id FROM users WHERE phone = ?";
        $params = [$phone];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Phone exists check failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user's public data (without sensitive information)
     * 
     * @return array Public user data
     */
    public function getPublicData() {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'profile_image' => $this->profile_image,
            'role' => $this->role,
            'is_active' => $this->is_active,
            'email_verified' => $this->email_verified,
            'phone_verified' => $this->phone_verified,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
    
    /**
     * Set object properties from database row
     * 
     * @param array $row Database row
     */
    private function setProperties($row) {
        $this->id = $row['id'];
        $this->name = $row['name'];
        $this->email = $row['email'];
        $this->phone = $row['phone'];
        $this->password_hash = $row['password_hash'];
        $this->profile_image = $row['profile_image'];
        $this->role = $row['role'];
        $this->is_active = $row['is_active'];
        $this->email_verified = $row['email_verified'];
        $this->phone_verified = $row['phone_verified'];
        $this->created_at = $row['created_at'];
        $this->updated_at = $row['updated_at'];
    }

    /**
     * Get all users with pagination and filters
     */
    public function getAll($page = 1, $limit = 20, $search = '', $role = null, $status = null) {
        try {
            $offset = ($page - 1) * $limit;

            $whereConditions = [];
            $params = [];

            if (!empty($search)) {
                $whereConditions[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }

            if ($role) {
                $whereConditions[] = "role = ?";
                $params[] = $role;
            }

            if ($status) {
                $whereConditions[] = "status = ?";
                $params[] = $status;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get total count
            $countSql = "SELECT COUNT(*) FROM users $whereClause";
            $stmt = $this->db->prepare($countSql);
            $stmt->execute($params);
            $total = (int)$stmt->fetchColumn();

            // Get users
            $sql = "SELECT id, name, email, phone, role, status, created_at, updated_at
                    FROM users $whereClause
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $users = $stmt->fetchAll();

            return [
                'users' => $users,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ];

        } catch (Exception $e) {
            error_log("Get all users error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Search users
     */
    public function search($query, $limit = 10) {
        try {
            $sql = "SELECT id, name, email, phone, role
                    FROM users
                    WHERE (name LIKE ? OR email LIKE ? OR phone LIKE ?)
                    AND status = 'active'
                    ORDER BY name ASC
                    LIMIT ?";

            $searchTerm = "%$query%";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $limit]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Search users error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get user's chits
     */
    public function getChits() {
        try {
            $sql = "SELECT c.*, cm.member_number, cm.status as member_status, cm.joined_date
                    FROM chits c
                    JOIN chit_members cm ON c.id = cm.chit_id
                    WHERE cm.user_id = ?
                    ORDER BY c.created_at DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Get user chits error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get user's payments
     */
    public function getPayments() {
        try {
            $sql = "SELECT p.*, c.name as chit_name
                    FROM payments p
                    JOIN chit_members cm ON p.member_id = cm.id
                    JOIN chits c ON cm.chit_id = c.id
                    WHERE cm.user_id = ?
                    ORDER BY p.due_date DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Get user payments error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get user activity
     */
    public function getActivity() {
        try {
            $sql = "SELECT action, details, created_at
                    FROM activity_logs
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                    LIMIT 50";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Get user activity error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get user summary
     */
    public function getSummary() {
        try {
            // Get chits count
            $sql = "SELECT COUNT(*) FROM chit_members WHERE user_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);
            $chitsCount = (int)$stmt->fetchColumn();

            // Get total payments
            $sql = "SELECT COUNT(*), COALESCE(SUM(amount), 0)
                    FROM payments p
                    JOIN chit_members cm ON p.member_id = cm.id
                    WHERE cm.user_id = ? AND p.status = 'paid'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);
            $paymentData = $stmt->fetch();

            // Get pending payments
            $sql = "SELECT COUNT(*), COALESCE(SUM(amount), 0)
                    FROM payments p
                    JOIN chit_members cm ON p.member_id = cm.id
                    WHERE cm.user_id = ? AND p.status IN ('pending', 'overdue')";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);
            $pendingData = $stmt->fetch();

            return [
                'chits_joined' => $chitsCount,
                'total_payments' => (int)$paymentData[0],
                'total_paid_amount' => (float)$paymentData[1],
                'pending_payments' => (int)$pendingData[0],
                'pending_amount' => (float)$pendingData[1],
                'member_since' => $this->created_at
            ];

        } catch (Exception $e) {
            error_log("Get user summary error: " . $e->getMessage());
            return [];
        }
    }
}
?>
