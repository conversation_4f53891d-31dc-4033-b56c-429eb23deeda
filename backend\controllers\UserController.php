<?php
/**
 * User Controller - LiteSpeed Optimized
 * 
 * Handles user management operations
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../utils/helpers.php';

class UserController {
    
    /**
     * Get all users
     */
    public function getAllUsers() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - only admin can view all users
            if ($user->role !== 'admin') {
                sendError('Insufficient permissions', 403);
            }
            
            // Get query parameters
            $page = (int)($_GET['page'] ?? 1);
            $limit = min((int)($_GET['limit'] ?? 20), 100);
            $search = $_GET['search'] ?? '';
            $role = $_GET['role'] ?? null;
            $status = $_GET['status'] ?? null;
            
            $userModel = new User();
            $result = $userModel->getAll($page, $limit, $search, $role, $status);
            
            sendSuccess($result, 'Users retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get all users error: " . $e->getMessage());
            sendError('Failed to retrieve users', 500);
        }
    }
    
    /**
     * Get specific user
     */
    public function getUser($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - admin can view any user, others can only view themselves
            if ($user->role !== 'admin' && $user->id != $userId) {
                sendError('Insufficient permissions', 403);
            }
            
            $userModel = new User();
            if ($userModel->findById($userId)) {
                sendSuccess($userModel->getPublicData(), 'User retrieved successfully');
            } else {
                sendError('User not found', 404);
            }
            
        } catch (Exception $e) {
            error_log("Get user error: " . $e->getMessage());
            sendError('Failed to retrieve user', 500);
        }
    }
    
    /**
     * Create new user
     */
    public function createUser() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - only admin can create users
            if ($user->role !== 'admin') {
                sendError('Insufficient permissions', 403);
            }
            
            $data = getRequestData();
            
            // Validate required fields
            $required = ['name', 'email', 'phone', 'password'];
            $missing = validateRequired($data, $required);
            
            if (!empty($missing)) {
                sendError('Missing required fields: ' . implode(', ', $missing), 400);
            }
            
            // Validate input
            $name = sanitizeInput($data['name']);
            $email = sanitizeInput($data['email']);
            $phone = sanitizeInput($data['phone']);
            $password = $data['password'];
            $role = sanitizeInput($data['role'] ?? 'member');
            
            if (strlen($name) < 2) {
                sendError('Name must be at least 2 characters long', 400);
            }
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                sendError('Invalid email format', 400);
            }
            
            if (strlen($password) < 6) {
                sendError('Password must be at least 6 characters long', 400);
            }
            
            if (!preg_match('/^[6-9]\d{9}$/', $phone)) {
                sendError('Invalid phone number format', 400);
            }
            
            if (!in_array($role, ['admin', 'organizer', 'member'])) {
                sendError('Invalid role', 400);
            }
            
            $userModel = new User();
            
            // Check if email already exists
            if ($userModel->emailExists($email)) {
                sendError('Email already registered', 409);
            }
            
            // Check if phone already exists
            if ($userModel->phoneExists($phone)) {
                sendError('Phone number already registered', 409);
            }
            
            // Create new user
            $userModel->name = $name;
            $userModel->email = $email;
            $userModel->phone = $phone;
            $userModel->password_hash = $password; // Will be hashed in create method
            $userModel->role = $role;
            $userModel->status = 'active';
            
            if ($userModel->create()) {
                logActivity($user->id, 'user_created', [
                    'created_user_id' => $userModel->id,
                    'email' => $email,
                    'role' => $role
                ]);
                
                sendSuccess($userModel->getPublicData(), 'User created successfully', 201);
            } else {
                sendError('Failed to create user', 500);
            }
            
        } catch (Exception $e) {
            error_log("Create user error: " . $e->getMessage());
            sendError('Failed to create user', 500);
        }
    }
    
    /**
     * Update user
     */
    public function updateUser($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - admin can update any user, others can only update themselves
            if ($user->role !== 'admin' && $user->id != $userId) {
                sendError('Insufficient permissions', 403);
            }
            
            $data = getRequestData();
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            // Update allowed fields
            if (isset($data['name'])) {
                $name = sanitizeInput($data['name']);
                if (strlen($name) < 2) {
                    sendError('Name must be at least 2 characters long', 400);
                }
                $userModel->name = $name;
            }
            
            if (isset($data['phone'])) {
                $phone = sanitizeInput($data['phone']);
                if (!preg_match('/^[6-9]\d{9}$/', $phone)) {
                    sendError('Invalid phone number format', 400);
                }
                
                // Check if phone already exists for another user
                if ($userModel->phoneExists($phone) && $userModel->phone !== $phone) {
                    sendError('Phone number already registered', 409);
                }
                
                $userModel->phone = $phone;
            }
            
            // Only admin can update role and status
            if ($user->role === 'admin') {
                if (isset($data['role'])) {
                    $role = sanitizeInput($data['role']);
                    if (!in_array($role, ['admin', 'organizer', 'member'])) {
                        sendError('Invalid role', 400);
                    }
                    $userModel->role = $role;
                }
                
                if (isset($data['status'])) {
                    $status = sanitizeInput($data['status']);
                    if (!in_array($status, ['active', 'inactive', 'suspended'])) {
                        sendError('Invalid status', 400);
                    }
                    $userModel->status = $status;
                }
            }
            
            if ($userModel->update()) {
                logActivity($user->id, 'user_updated', [
                    'updated_user_id' => $userId,
                    'changes' => array_keys($data)
                ]);
                
                sendSuccess($userModel->getPublicData(), 'User updated successfully');
            } else {
                sendError('Failed to update user', 500);
            }
            
        } catch (Exception $e) {
            error_log("Update user error: " . $e->getMessage());
            sendError('Failed to update user', 500);
        }
    }
    
    /**
     * Delete user
     */
    public function deleteUser($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - only admin can delete users
            if ($user->role !== 'admin') {
                sendError('Insufficient permissions', 403);
            }
            
            // Prevent self-deletion
            if ($user->id == $userId) {
                sendError('Cannot delete your own account', 400);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            if ($userModel->delete()) {
                logActivity($user->id, 'user_deleted', [
                    'deleted_user_id' => $userId,
                    'email' => $userModel->email
                ]);
                
                sendSuccess(null, 'User deleted successfully');
            } else {
                sendError('Failed to delete user', 500);
            }
            
        } catch (Exception $e) {
            error_log("Delete user error: " . $e->getMessage());
            sendError('Failed to delete user', 500);
        }
    }
    
    /**
     * Search users
     */
    public function searchUsers() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $query = $_GET['q'] ?? '';
            $limit = min((int)($_GET['limit'] ?? 10), 50);
            
            if (strlen($query) < 2) {
                sendError('Search query must be at least 2 characters', 400);
            }
            
            $userModel = new User();
            $results = $userModel->search($query, $limit);
            
            sendSuccess($results, 'Search completed successfully');
            
        } catch (Exception $e) {
            error_log("Search users error: " . $e->getMessage());
            sendError('Failed to search users', 500);
        }
    }
    
    /**
     * Get user's chits
     */
    public function getUserChits($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if ($user->role !== 'admin' && $user->id != $userId) {
                sendError('Insufficient permissions', 403);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            $chits = $userModel->getChits();
            sendSuccess($chits, 'User chits retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get user chits error: " . $e->getMessage());
            sendError('Failed to retrieve user chits', 500);
        }
    }
    
    /**
     * Get user's payments
     */
    public function getUserPayments($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if ($user->role !== 'admin' && $user->id != $userId) {
                sendError('Insufficient permissions', 403);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            $payments = $userModel->getPayments();
            sendSuccess($payments, 'User payments retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get user payments error: " . $e->getMessage());
            sendError('Failed to retrieve user payments', 500);
        }
    }
    
    /**
     * Get user activity
     */
    public function getUserActivity($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if ($user->role !== 'admin' && $user->id != $userId) {
                sendError('Insufficient permissions', 403);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            $activity = $userModel->getActivity();
            sendSuccess($activity, 'User activity retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get user activity error: " . $e->getMessage());
            sendError('Failed to retrieve user activity', 500);
        }
    }
    
    /**
     * Get user summary
     */
    public function getUserSummary($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if ($user->role !== 'admin' && $user->id != $userId) {
                sendError('Insufficient permissions', 403);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            $summary = $userModel->getSummary();
            sendSuccess($summary, 'User summary retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get user summary error: " . $e->getMessage());
            sendError('Failed to retrieve user summary', 500);
        }
    }
    
    /**
     * Activate user
     */
    public function activateUser($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - only admin
            if ($user->role !== 'admin') {
                sendError('Insufficient permissions', 403);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            $userModel->status = 'active';
            
            if ($userModel->update()) {
                logActivity($user->id, 'user_activated', [
                    'activated_user_id' => $userId
                ]);
                
                sendSuccess($userModel->getPublicData(), 'User activated successfully');
            } else {
                sendError('Failed to activate user', 500);
            }
            
        } catch (Exception $e) {
            error_log("Activate user error: " . $e->getMessage());
            sendError('Failed to activate user', 500);
        }
    }
    
    /**
     * Deactivate user
     */
    public function deactivateUser($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - only admin
            if ($user->role !== 'admin') {
                sendError('Insufficient permissions', 403);
            }
            
            // Prevent self-deactivation
            if ($user->id == $userId) {
                sendError('Cannot deactivate your own account', 400);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            $userModel->status = 'inactive';
            
            if ($userModel->update()) {
                logActivity($user->id, 'user_deactivated', [
                    'deactivated_user_id' => $userId
                ]);
                
                sendSuccess($userModel->getPublicData(), 'User deactivated successfully');
            } else {
                sendError('Failed to deactivate user', 500);
            }
            
        } catch (Exception $e) {
            error_log("Deactivate user error: " . $e->getMessage());
            sendError('Failed to deactivate user', 500);
        }
    }
    
    /**
     * Update user role
     */
    public function updateUserRole($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - only admin
            if ($user->role !== 'admin') {
                sendError('Insufficient permissions', 403);
            }
            
            $data = getRequestData();
            
            if (!isset($data['role'])) {
                sendError('Role is required', 400);
            }
            
            $role = sanitizeInput($data['role']);
            if (!in_array($role, ['admin', 'organizer', 'member'])) {
                sendError('Invalid role', 400);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            $userModel->role = $role;
            
            if ($userModel->update()) {
                logActivity($user->id, 'user_role_updated', [
                    'updated_user_id' => $userId,
                    'new_role' => $role,
                    'old_role' => $userModel->role
                ]);
                
                sendSuccess($userModel->getPublicData(), 'User role updated successfully');
            } else {
                sendError('Failed to update user role', 500);
            }
            
        } catch (Exception $e) {
            error_log("Update user role error: " . $e->getMessage());
            sendError('Failed to update user role', 500);
        }
    }
    
    /**
     * Reset user password
     */
    public function resetUserPassword($userId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions - only admin
            if ($user->role !== 'admin') {
                sendError('Insufficient permissions', 403);
            }
            
            $data = getRequestData();
            
            if (!isset($data['new_password'])) {
                sendError('New password is required', 400);
            }
            
            $newPassword = $data['new_password'];
            if (strlen($newPassword) < 6) {
                sendError('Password must be at least 6 characters long', 400);
            }
            
            $userModel = new User();
            if (!$userModel->findById($userId)) {
                sendError('User not found', 404);
            }
            
            if ($userModel->updatePassword($newPassword)) {
                logActivity($user->id, 'user_password_reset', [
                    'reset_user_id' => $userId
                ]);
                
                sendSuccess(null, 'User password reset successfully');
            } else {
                sendError('Failed to reset user password', 500);
            }
            
        } catch (Exception $e) {
            error_log("Reset user password error: " . $e->getMessage());
            sendError('Failed to reset user password', 500);
        }
    }
}

?>
