// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Payment _$PaymentFromJson(Map<String, dynamic> json) => Payment(
      id: json['id'] as String,
      chitId: json['chitId'] as String,
      biddingRoundId: json['biddingRoundId'] as String,
      memberId: json['memberId'] as String,
      memberName: json['memberName'] as String,
      amount: (json['amount'] as num?)?.toDouble(),
      paymentType: $enumDecode(_$PaymentTypeEnumMap, json['paymentType']),
      paymentMethod:
          $enumDecodeNullable(_$PaymentMethodEnumMap, json['paymentMethod']),
      paymentStatus:
          $enumDecodeNullable(_$PaymentStatusEnumMap, json['paymentStatus']) ??
              PaymentStatus.pending,
      dueDate: DateTime.parse(json['dueDate'] as String),
      paidDate: json['paidDate'] == null
          ? null
          : DateTime.parse(json['paidDate'] as String),
      transactionReference: json['transactionReference'] as String?,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PaymentToJson(Payment instance) => <String, dynamic>{
      'id': instance.id,
      'chitId': instance.chitId,
      'biddingRoundId': instance.biddingRoundId,
      'memberId': instance.memberId,
      'memberName': instance.memberName,
      'amount': instance.amount,
      'paymentType': _$PaymentTypeEnumMap[instance.paymentType]!,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod],
      'paymentStatus': _$PaymentStatusEnumMap[instance.paymentStatus]!,
      'dueDate': instance.dueDate.toIso8601String(),
      'paidDate': instance.paidDate?.toIso8601String(),
      'transactionReference': instance.transactionReference,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$PaymentTypeEnumMap = {
  PaymentType.contribution: 'contribution',
  PaymentType.winningAmount: 'winning_amount',
  PaymentType.commission: 'commission',
};

const _$PaymentMethodEnumMap = {
  PaymentMethod.cash: 'cash',
  PaymentMethod.bankTransfer: 'bank_transfer',
  PaymentMethod.upi: 'upi',
  PaymentMethod.cheque: 'cheque',
  PaymentMethod.other: 'other',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.paid: 'paid',
  PaymentStatus.overdue: 'overdue',
  PaymentStatus.cancelled: 'cancelled',
};
