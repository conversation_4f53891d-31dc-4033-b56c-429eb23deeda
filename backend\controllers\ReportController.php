<?php
/**
 * Report Controller - LiteSpeed Optimized
 * 
 * Handles report generation and analytics
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../models/Chit.php';
require_once __DIR__ . '/../models/Payment.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/ChitMember.php';
require_once __DIR__ . '/../utils/helpers.php';

class ReportController {
    
    /**
     * Get available reports
     */
    public function getAvailableReports() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $reports = [
                'dashboard' => [
                    'name' => 'Dashboard Summary',
                    'description' => 'Overview of all key metrics',
                    'endpoint' => '/api/reports/dashboard'
                ],
                'financial' => [
                    'name' => 'Financial Reports',
                    'description' => 'Financial summaries and detailed reports',
                    'endpoints' => [
                        'summary' => '/api/reports/financial/summary',
                        'detailed' => '/api/reports/financial/detailed'
                    ]
                ],
                'chits' => [
                    'name' => 'Chit Reports',
                    'description' => 'Chit performance and status reports',
                    'endpoints' => [
                        'performance' => '/api/reports/chits/performance',
                        'status' => '/api/reports/chits/status'
                    ]
                ],
                'members' => [
                    'name' => 'Member Reports',
                    'description' => 'Member activity and payment reports',
                    'endpoints' => [
                        'activity' => '/api/reports/members/activity',
                        'payments' => '/api/reports/members/payments'
                    ]
                ],
                'payments' => [
                    'name' => 'Payment Reports',
                    'description' => 'Payment tracking and analysis',
                    'endpoints' => [
                        'pending' => '/api/reports/payments/pending',
                        'overdue' => '/api/reports/payments/overdue',
                        'summary' => '/api/reports/payments/summary'
                    ]
                ],
                'bidding' => [
                    'name' => 'Bidding Reports',
                    'description' => 'Bidding history and analytics',
                    'endpoints' => [
                        'history' => '/api/reports/bidding/history',
                        'analytics' => '/api/reports/bidding/analytics'
                    ]
                ]
            ];
            
            sendSuccess($reports, 'Available reports retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get available reports error: " . $e->getMessage());
            sendError('Failed to retrieve available reports', 500);
        }
    }
    
    /**
     * Get dashboard report
     */
    public function getDashboardReport() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            // Get summary statistics for the authenticated user
            $dashboard = [
                'summary' => [
                    'total_chits' => $this->getTotalChits($conn, $user['id']),
                    'active_chits' => $this->getActiveChits($conn, $user['id']),
                    'total_members' => $this->getTotalMembers($conn, $user['id']),
                    'total_amount' => $this->getTotalAmount($conn, $user['id']),
                    'pending_payments' => $this->getPendingPayments($conn, $user['id']),
                    'overdue_payments' => $this->getOverduePayments($conn, $user['id'])
                ],
                'recent_activity' => $this->getRecentActivity($conn, $user['id']),
                'monthly_trends' => $this->getMonthlyTrends($conn, $user['id']),
                'top_performers' => $this->getTopPerformers($conn, $user['id']),
                'alerts' => $this->getAlerts($conn, $user['id'])
            ];
            
            sendSuccess($dashboard, 'Dashboard report generated successfully');
            
        } catch (Exception $e) {
            error_log("Get dashboard report error: " . $e->getMessage());
            sendError('Failed to generate dashboard report', 500);
        }
    }
    
    /**
     * Get financial summary
     */
    public function getFinancialSummary() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $dateFrom = $_GET['from'] ?? date('Y-m-01'); // First day of current month
            $dateTo = $_GET['to'] ?? date('Y-m-d'); // Today
            
            $summary = [
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo
                ],
                'totals' => [
                    'collections' => $this->getTotalCollections($conn, $user['id'], $dateFrom, $dateTo),
                    'disbursements' => $this->getTotalDisbursements($conn, $user['id'], $dateFrom, $dateTo),
                    'commissions' => $this->getTotalCommissions($conn, $user['id'], $dateFrom, $dateTo),
                    'pending_amount' => $this->getPendingAmount($conn, $user['id'], $dateFrom, $dateTo)
                ],
                'breakdown' => [
                    'by_chit' => $this->getFinancialBreakdownByChit($conn, $user['id'], $dateFrom, $dateTo),
                    'by_month' => $this->getFinancialBreakdownByMonth($conn, $user['id'], $dateFrom, $dateTo)
                ]
            ];
            
            sendSuccess($summary, 'Financial summary generated successfully');
            
        } catch (Exception $e) {
            error_log("Get financial summary error: " . $e->getMessage());
            sendError('Failed to generate financial summary', 500);
        }
    }
    
    /**
     * Get detailed financial report
     */
    public function getDetailedFinancialReport() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $dateFrom = $_GET['from'] ?? date('Y-m-01');
            $dateTo = $_GET['to'] ?? date('Y-m-d');
            $chitId = $_GET['chit_id'] ?? null;
            
            $report = [
                'period' => [
                    'from' => $dateFrom,
                    'to' => $dateTo
                ],
                'transactions' => $this->getDetailedTransactions($conn, $dateFrom, $dateTo, $chitId),
                'analysis' => [
                    'payment_patterns' => $this->getPaymentPatterns($conn, $dateFrom, $dateTo),
                    'collection_efficiency' => $this->getCollectionEfficiency($conn, $dateFrom, $dateTo),
                    'profit_analysis' => $this->getProfitAnalysis($conn, $dateFrom, $dateTo)
                ]
            ];
            
            sendSuccess($report, 'Detailed financial report generated successfully');
            
        } catch (Exception $e) {
            error_log("Get detailed financial report error: " . $e->getMessage());
            sendError('Failed to generate detailed financial report', 500);
        }
    }
    
    /**
     * Get chit performance report
     */
    public function getChitPerformanceReport() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $sql = "SELECT 
                        c.id,
                        c.name,
                        c.total_amount,
                        c.monthly_amount,
                        c.duration_months,
                        c.status,
                        COUNT(cm.id) as member_count,
                        COUNT(CASE WHEN cm.status = 'active' THEN 1 END) as active_members,
                        COALESCE(SUM(p.amount), 0) as total_collected,
                        ROUND((COALESCE(SUM(p.amount), 0) / c.total_amount) * 100, 2) as collection_percentage
                    FROM chits c
                    LEFT JOIN chit_members cm ON c.id = cm.chit_id
                    LEFT JOIN payments p ON cm.id = p.member_id AND p.status = 'paid'
                    GROUP BY c.id
                    ORDER BY collection_percentage DESC";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $chits = $stmt->fetchAll();
            
            $performance = [
                'chits' => $chits,
                'summary' => [
                    'total_chits' => count($chits),
                    'avg_collection_rate' => round(array_sum(array_column($chits, 'collection_percentage')) / count($chits), 2),
                    'best_performer' => $chits[0] ?? null,
                    'needs_attention' => array_filter($chits, function($chit) {
                        return $chit['collection_percentage'] < 50;
                    })
                ]
            ];
            
            sendSuccess($performance, 'Chit performance report generated successfully');
            
        } catch (Exception $e) {
            error_log("Get chit performance report error: " . $e->getMessage());
            sendError('Failed to generate chit performance report', 500);
        }
    }
    
    /**
     * Get member activity report
     */
    public function getMemberActivityReport() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $sql = "SELECT 
                        u.id,
                        u.name,
                        u.email,
                        u.phone,
                        COUNT(cm.id) as chits_joined,
                        COUNT(CASE WHEN cm.status = 'active' THEN 1 END) as active_chits,
                        COUNT(p.id) as total_payments,
                        COUNT(CASE WHEN p.status = 'paid' THEN 1 END) as paid_payments,
                        COALESCE(SUM(CASE WHEN p.status = 'paid' THEN p.amount END), 0) as total_paid,
                        ROUND((COUNT(CASE WHEN p.status = 'paid' THEN 1 END) / NULLIF(COUNT(p.id), 0)) * 100, 2) as payment_rate
                    FROM users u
                    LEFT JOIN chit_members cm ON u.id = cm.user_id
                    LEFT JOIN payments p ON cm.id = p.member_id
                    WHERE u.role IN ('member', 'organizer')
                    GROUP BY u.id
                    ORDER BY payment_rate DESC, total_paid DESC";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $members = $stmt->fetchAll();
            
            $activity = [
                'members' => $members,
                'summary' => [
                    'total_members' => count($members),
                    'avg_payment_rate' => round(array_sum(array_column($members, 'payment_rate')) / count($members), 2),
                    'top_performers' => array_slice($members, 0, 5),
                    'defaulters' => array_filter($members, function($member) {
                        return $member['payment_rate'] < 80;
                    })
                ]
            ];
            
            sendSuccess($activity, 'Member activity report generated successfully');
            
        } catch (Exception $e) {
            error_log("Get member activity report error: " . $e->getMessage());
            sendError('Failed to generate member activity report', 500);
        }
    }
    
    /**
     * Get pending payments report
     */
    public function getPendingPaymentsReport() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $sql = "SELECT 
                        p.id,
                        p.amount,
                        p.due_date,
                        p.type,
                        p.status,
                        u.name as member_name,
                        u.phone as member_phone,
                        c.name as chit_name,
                        DATEDIFF(CURDATE(), p.due_date) as days_overdue
                    FROM payments p
                    JOIN chit_members cm ON p.member_id = cm.id
                    JOIN users u ON cm.user_id = u.id
                    JOIN chits c ON cm.chit_id = c.id
                    WHERE p.status IN ('pending', 'overdue')
                    ORDER BY p.due_date ASC";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $payments = $stmt->fetchAll();
            
            $pending = [
                'payments' => $payments,
                'summary' => [
                    'total_pending' => count($payments),
                    'total_amount' => array_sum(array_column($payments, 'amount')),
                    'overdue_count' => count(array_filter($payments, function($p) { return $p['days_overdue'] > 0; })),
                    'overdue_amount' => array_sum(array_column(array_filter($payments, function($p) { return $p['days_overdue'] > 0; }), 'amount'))
                ]
            ];
            
            sendSuccess($pending, 'Pending payments report generated successfully');
            
        } catch (Exception $e) {
            error_log("Get pending payments report error: " . $e->getMessage());
            sendError('Failed to generate pending payments report', 500);
        }
    }
    
    /**
     * Export to PDF
     */
    public function exportToPDF() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $reportType = $_GET['type'] ?? 'dashboard';
            
            // Generate PDF content (simplified implementation)
            $pdfContent = $this->generatePDFContent($reportType);
            
            // Set headers for PDF download
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="chit_fund_report_' . date('Y-m-d') . '.pdf"');
            header('Content-Length: ' . strlen($pdfContent));
            
            echo $pdfContent;
            
        } catch (Exception $e) {
            error_log("Export to PDF error: " . $e->getMessage());
            sendError('Failed to export PDF', 500);
        }
    }
    
    /**
     * Export to Excel
     */
    public function exportToExcel() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $reportType = $_GET['type'] ?? 'dashboard';
            
            // Generate Excel content (simplified implementation)
            $excelContent = $this->generateExcelContent($reportType);
            
            // Set headers for Excel download
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment; filename="chit_fund_report_' . date('Y-m-d') . '.xls"');
            header('Content-Length: ' . strlen($excelContent));
            
            echo $excelContent;
            
        } catch (Exception $e) {
            error_log("Export to Excel error: " . $e->getMessage());
            sendError('Failed to export Excel', 500);
        }
    }
    
    /**
     * Export to CSV
     */
    public function exportToCSV() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $reportType = $_GET['type'] ?? 'dashboard';
            
            // Generate CSV content
            $csvContent = $this->generateCSVContent($reportType);
            
            // Set headers for CSV download
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="chit_fund_report_' . date('Y-m-d') . '.csv"');
            header('Content-Length: ' . strlen($csvContent));
            
            echo $csvContent;
            
        } catch (Exception $e) {
            error_log("Export to CSV error: " . $e->getMessage());
            sendError('Failed to export CSV', 500);
        }
    }
    
    // Helper methods for database queries
    private function getTotalChits($conn, $userId) {
        $stmt = $conn->prepare("SELECT COUNT(DISTINCT c.id) FROM chits c
                               LEFT JOIN chit_members cm ON c.id = cm.chit_id
                               WHERE c.organizer_id = ? OR cm.user_id = ?");
        $stmt->execute([$userId, $userId]);
        return (int)$stmt->fetchColumn();
    }
    
    private function getActiveChits($conn, $userId) {
        $stmt = $conn->prepare("SELECT COUNT(DISTINCT c.id) FROM chits c
                               LEFT JOIN chit_members cm ON c.id = cm.chit_id
                               WHERE (c.organizer_id = ? OR cm.user_id = ?) AND c.status = 'active'");
        $stmt->execute([$userId, $userId]);
        return (int)$stmt->fetchColumn();
    }
    
    private function getTotalMembers($conn, $userId) {
        $stmt = $conn->prepare("SELECT COUNT(DISTINCT cm.id) FROM chit_members cm
                               JOIN chits c ON cm.chit_id = c.id
                               WHERE (c.organizer_id = ? OR cm.user_id = ?) AND cm.status = 'active'");
        $stmt->execute([$userId, $userId]);
        return (int)$stmt->fetchColumn();
    }
    
    private function getTotalAmount($conn, $userId) {
        $stmt = $conn->prepare("SELECT COALESCE(SUM(DISTINCT c.total_amount), 0) FROM chits c
                               LEFT JOIN chit_members cm ON c.id = cm.chit_id
                               WHERE (c.organizer_id = ? OR cm.user_id = ?) AND c.status IN ('active', 'completed')");
        $stmt->execute([$userId, $userId]);
        return (float)$stmt->fetchColumn();
    }
    
    private function getPendingPayments($conn, $userId) {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM payments p
                               JOIN chits c ON p.chit_id = c.id
                               WHERE (c.organizer_id = ? OR p.user_id = ?) AND p.status = 'pending'");
        $stmt->execute([$userId, $userId]);
        return (int)$stmt->fetchColumn();
    }
    
    private function getOverduePayments($conn, $userId) {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM payments p
                               JOIN chits c ON p.chit_id = c.id
                               WHERE (c.organizer_id = ? OR p.user_id = ?) AND (p.status = 'overdue' OR (p.status = 'pending' AND p.due_date < CURDATE()))");
        $stmt->execute([$userId, $userId]);
        return (int)$stmt->fetchColumn();
    }
    
    private function getRecentActivity($conn, $userId) {
        $sql = "SELECT
                    'payment' as type,
                    CONCAT(u.name, ' made a payment of ₹', p.amount) as description,
                    p.created_at as timestamp
                FROM payments p
                JOIN chits c ON p.chit_id = c.id
                JOIN users u ON p.user_id = u.id
                WHERE (c.organizer_id = ? OR p.user_id = ?)
                AND p.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                ORDER BY p.created_at DESC
                LIMIT 10";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$userId, $userId]);
        return $stmt->fetchAll();
    }
    
    private function getMonthlyTrends($conn, $userId) {
        $sql = "SELECT
                    DATE_FORMAT(p.created_at, '%Y-%m') as month,
                    COUNT(*) as payment_count,
                    SUM(p.amount) as total_amount
                FROM payments p
                JOIN chits c ON p.chit_id = c.id
                WHERE (c.organizer_id = ? OR p.user_id = ?)
                AND p.status = 'paid' AND p.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(p.created_at, '%Y-%m')
                ORDER BY month";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$userId, $userId]);
        return $stmt->fetchAll();
    }
    
    private function getTopPerformers($conn, $userId) {
        $sql = "SELECT
                    u.name,
                    COUNT(p.id) as payment_count,
                    SUM(p.amount) as total_paid
                FROM users u
                JOIN payments p ON u.id = p.user_id
                JOIN chits c ON p.chit_id = c.id
                WHERE (c.organizer_id = ? OR p.user_id = ?) AND p.status = 'paid'
                GROUP BY u.id
                ORDER BY total_paid DESC
                LIMIT 5";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$userId, $userId]);
        return $stmt->fetchAll();
    }
    
    private function getAlerts($conn, $userId) {
        $alerts = [];

        // Overdue payments alert for user's chits
        $stmt = $conn->prepare("SELECT COUNT(*) FROM payments p
                               JOIN chits c ON p.chit_id = c.id
                               WHERE (c.organizer_id = ? OR p.user_id = ?)
                               AND (p.status = 'overdue' OR (p.status = 'pending' AND p.due_date < CURDATE()))");
        $stmt->execute([$userId, $userId]);
        $overdueCount = (int)$stmt->fetchColumn();

        if ($overdueCount > 0) {
            $alerts[] = [
                'type' => 'warning',
                'message' => "$overdueCount overdue payments require attention",
                'action' => '/api/reports/payments/overdue'
            ];
        }

        return $alerts;
    }
    
    private function generatePDFContent($reportType) {
        // Simplified PDF generation - in production, use a proper PDF library
        return "PDF Report Content for $reportType - Generated on " . date('Y-m-d H:i:s');
    }
    
    private function generateExcelContent($reportType) {
        // Simplified Excel generation - in production, use a proper Excel library
        return "Excel Report Content for $reportType - Generated on " . date('Y-m-d H:i:s');
    }
    
    private function generateCSVContent($reportType) {
        // Simplified CSV generation
        $csv = "Report Type,Generated Date\n";
        $csv .= "$reportType," . date('Y-m-d H:i:s') . "\n";
        return $csv;
    }

    // Financial helper methods - User-specific data only
    private function getTotalCollections($conn, $userId, $dateFrom, $dateTo) {
        $stmt = $conn->prepare("SELECT COALESCE(SUM(p.amount), 0) FROM payments p
                               JOIN chits c ON p.chit_id = c.id
                               WHERE (c.organizer_id = ? OR p.user_id = ?)
                               AND p.status = 'paid' AND p.created_at BETWEEN ? AND ?");
        $stmt->execute([$userId, $userId, $dateFrom, $dateTo]);
        return (float)$stmt->fetchColumn();
    }

    private function getTotalDisbursements($conn, $userId, $dateFrom, $dateTo) {
        $stmt = $conn->prepare("SELECT COALESCE(SUM(b.winning_amount), 0) FROM bidding_rounds b
                               JOIN chits c ON b.chit_id = c.id
                               WHERE (c.organizer_id = ? OR b.winner_id = ?)
                               AND b.status = 'completed' AND b.created_at BETWEEN ? AND ?");
        $stmt->execute([$userId, $userId, $dateFrom, $dateTo]);
        return (float)$stmt->fetchColumn();
    }

    private function getTotalCommissions($conn, $userId, $dateFrom, $dateTo) {
        $stmt = $conn->prepare("SELECT COALESCE(SUM(c.total_amount * c.commission_percentage / 100), 0) FROM chits c
                               WHERE c.organizer_id = ? AND c.status = 'completed'
                               AND c.created_at BETWEEN ? AND ?");
        $stmt->execute([$userId, $dateFrom, $dateTo]);
        return (float)$stmt->fetchColumn();
    }

    private function getPendingAmount($conn, $userId, $dateFrom, $dateTo) {
        $stmt = $conn->prepare("SELECT COALESCE(SUM(p.amount), 0) FROM payments p
                               JOIN chits c ON p.chit_id = c.id
                               WHERE (c.organizer_id = ? OR p.user_id = ?)
                               AND p.status = 'pending' AND p.due_date BETWEEN ? AND ?");
        $stmt->execute([$userId, $userId, $dateFrom, $dateTo]);
        return (float)$stmt->fetchColumn();
    }

    private function getFinancialBreakdownByChit($conn, $userId, $dateFrom, $dateTo) {
        $stmt = $conn->prepare("SELECT c.name, c.total_amount,
                               COALESCE(SUM(CASE WHEN p.status = 'paid' THEN p.amount ELSE 0 END), 0) as collected,
                               COALESCE(SUM(CASE WHEN p.status = 'pending' THEN p.amount ELSE 0 END), 0) as pending
                               FROM chits c
                               LEFT JOIN payments p ON c.id = p.chit_id AND p.created_at BETWEEN ? AND ?
                               WHERE (c.organizer_id = ? OR EXISTS(SELECT 1 FROM chit_members cm WHERE cm.chit_id = c.id AND cm.user_id = ?))
                               GROUP BY c.id, c.name, c.total_amount");
        $stmt->execute([$dateFrom, $dateTo, $userId, $userId]);
        return $stmt->fetchAll();
    }

    private function getFinancialBreakdownByMonth($conn, $userId, $dateFrom, $dateTo) {
        $stmt = $conn->prepare("SELECT DATE_FORMAT(p.created_at, '%Y-%m') as month,
                               COALESCE(SUM(CASE WHEN p.status = 'paid' THEN p.amount ELSE 0 END), 0) as collected,
                               COALESCE(SUM(CASE WHEN p.status = 'pending' THEN p.amount ELSE 0 END), 0) as pending
                               FROM payments p
                               JOIN chits c ON p.chit_id = c.id
                               WHERE (c.organizer_id = ? OR p.user_id = ?)
                               AND p.created_at BETWEEN ? AND ?
                               GROUP BY DATE_FORMAT(p.created_at, '%Y-%m')
                               ORDER BY month");
        $stmt->execute([$userId, $userId, $dateFrom, $dateTo]);
        return $stmt->fetchAll();
    }
}

?>
