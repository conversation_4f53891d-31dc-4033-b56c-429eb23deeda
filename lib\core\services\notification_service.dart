import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/notification_model.dart';
import 'api_service.dart';
import 'storage_service.dart';

class NotificationService {
  static late FlutterLocalNotificationsPlugin _localNotifications;
  static late FirebaseMessaging _firebaseMessaging;
  static late Dio _dio;

  static Future<void> initialize() async {
    // Initialize local notifications
    _localNotifications = FlutterLocalNotificationsPlugin();
    
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Initialize Firebase messaging
    _firebaseMessaging = FirebaseMessaging.instance;

    // Request permissions
    await _requestPermissions();

    // Set up message handlers
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    debugPrint('Firebase messaging initialized successfully');
    
    // Initialize Dio for API calls
    ApiService.initialize();
    _dio = Dio(BaseOptions(
      baseUrl: ApiService.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
      ),
    );
  }

  /// Request notification permissions (public method)
  static Future<bool> requestPermissions() async {
    return await _requestPermissions();
  }

  /// Request notification permissions
  static Future<bool> _requestPermissions() async {
    // Request local notification permissions
    final localPermission = await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();

    // Request Firebase messaging permissions
    final firebaseSettings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    return (localPermission ?? false) && 
           firebaseSettings.authorizationStatus == AuthorizationStatus.authorized;
  }

  /// Get FCM token
  static Future<String?> getFCMToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
      return null;
    }
  }

  /// Subscribe to FCM topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
    } catch (e) {
      debugPrint('Error subscribing to topic: $e');
    }
  }

  /// Unsubscribe from FCM topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
    } catch (e) {
      debugPrint('Error unsubscribing from topic: $e');
    }
  }

  /// Show local notification
  static Future<void> showLocalNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'chit_fund_channel',
      'Chit Fund Notifications',
      channelDescription: 'Notifications for chit fund activities',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  /// Schedule local notification
  /// Note: This is a placeholder. In production, use timezone package for proper scheduling
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    // For now, just show immediate notification
    // TODO: Implement proper scheduling with timezone package
    await showLocalNotification(
      id: id,
      title: title,
      body: body,
      payload: payload,
    );
  }

  /// Cancel notification
  static Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// Get notifications from server
  static Future<Result<List<NotificationModel>>> getNotifications({
    int page = 1,
    int limit = 20,
    bool? isRead,
    String? type,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (isRead != null) queryParams['is_read'] = isRead;
      if (type != null) queryParams['type'] = type;

      final response = await _dio.get('/notifications', queryParameters: queryParams);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final notifications = apiResponse.data!
              .map((notificationJson) => 
                  NotificationModel.fromJson(notificationJson as Map<String, dynamic>))
              .toList();
          return Success(notifications);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch notifications');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Mark notification as read
  static Future<Result<void>> markAsRead(String notificationId) async {
    try {
      final response = await _dio.put('/notifications/$notificationId/read');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to mark notification as read');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Mark all notifications as read
  static Future<Result<void>> markAllAsRead() async {
    try {
      final response = await _dio.put('/notifications/mark-all-read');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to mark all notifications as read');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Delete notification
  static Future<Result<void>> deleteNotification(String notificationId) async {
    try {
      final response = await _dio.delete('/notifications/$notificationId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to delete notification');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle foreground messages
  static void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Received foreground message: ${message.messageId}');
    
    // Show local notification for foreground messages
    showLocalNotification(
      id: message.hashCode,
      title: message.notification?.title ?? 'Chit Fund',
      body: message.notification?.body ?? 'You have a new notification',
      payload: message.data.toString(),
    );
  }

  /// Handle message opened app
  static void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('Message opened app: ${message.messageId}');
    // Handle navigation based on message data
    _handleNotificationNavigation(message.data);
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    // Handle navigation based on payload
    if (response.payload != null) {
      // Parse payload and navigate accordingly
    }
  }

  /// Handle notification navigation
  static void _handleNotificationNavigation(Map<String, dynamic> data) {
    // Implement navigation logic based on notification data
    final type = data['type'];
    final chitId = data['chit_id'];

    debugPrint('Handling notification navigation: type=$type, chitId=$chitId');

    switch (type) {
      case 'bidding':
        // Navigate to bidding screen
        debugPrint('Should navigate to bidding screen for chit: $chitId');
        // Note: Navigation requires context from main app
        break;
      case 'payment':
        // Navigate to payment screen
        debugPrint('Should navigate to payment screen');
        // Note: Navigation requires context from main app
        break;
      case 'reminder':
        // Navigate to chit details
        debugPrint('Should navigate to chit details for chit: $chitId');
        // Note: Navigation requires context from main app
        break;
      default:
        // Navigate to notifications screen
        debugPrint('Should navigate to notifications screen');
        // Note: Navigation requires context from main app
        break;
    }
  }

  /// Handle Dio errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Response timeout. Please try again.';
      case DioExceptionType.badResponse:
        if (error.response?.data is Map<String, dynamic>) {
          final data = error.response?.data as Map<String, dynamic>;
          return data['message'] ?? 'Server error occurred';
        } else {
          return 'Server error: ${error.response?.statusCode}';
        }
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      default:
        return 'Network error: ${error.message}';
    }
  }
}
