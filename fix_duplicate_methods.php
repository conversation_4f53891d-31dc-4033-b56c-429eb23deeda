<?php
/**
 * Fix Duplicate Methods Script
 * 
 * Removes duplicate methods from ChitController.php
 */

echo "🔧 FIXING DUPLICATE METHODS IN PHP CONTROLLERS\n";
echo "==============================================\n\n";

// Read the ChitController file
$chitControllerPath = 'backend/controllers/ChitController.php';
$content = file_get_contents($chitControllerPath);

if (!$content) {
    echo "❌ Could not read ChitController.php\n";
    exit(1);
}

echo "📁 Processing: $chitControllerPath\n\n";

// ================================================================
// FIX 1: REMOVE FIRST addMember METHOD (lines 321-388)
// ================================================================
echo "🔧 Fix 1: Removing first addMember method (JWT auth version)\n";

$firstAddMemberPattern = '/\/\*\*\s*\n\s*\*\s*Add member to chit\s*\n\s*\*\/\s*\n\s*public function addMember\(\$chitId\) \{\s*\n\s*\$userId = JWT::getUserIdFromToken\(\);.*?^\s*\}\s*$/ms';

if (preg_match($firstAddMemberPattern, $content)) {
    $content = preg_replace($firstAddMemberPattern, '', $content, 1);
    echo "✅ First addMember method removed\n";
} else {
    echo "⚠️ First addMember method pattern not found\n";
}

// ================================================================
// FIX 2: REMOVE FIRST removeMember METHOD (lines 393-448)
// ================================================================
echo "🔧 Fix 2: Removing first removeMember method (JWT auth version)\n";

$firstRemoveMemberPattern = '/\/\*\*\s*\n\s*\*\s*Remove member from chit\s*\n\s*\*\/\s*\n\s*public function removeMember\(\$chitId, \$memberId\) \{\s*\n\s*\$userId = JWT::getUserIdFromToken\(\);.*?^\s*\}\s*$/ms';

if (preg_match($firstRemoveMemberPattern, $content)) {
    $content = preg_replace($firstRemoveMemberPattern, '', $content, 1);
    echo "✅ First removeMember method removed\n";
} else {
    echo "⚠️ First removeMember method pattern not found\n";
}

// ================================================================
// FIX 3: REMOVE getMembers METHOD (keep getChitMembers)
// ================================================================
echo "🔧 Fix 3: Removing getMembers method (keeping getChitMembers)\n";

$getMembersPattern = '/\/\*\*\s*\n\s*\*\s*Get chit members\s*\n\s*\*\/\s*\n\s*public function getMembers\(\$chitId\) \{\s*\n\s*\$userId = JWT::getUserIdFromToken\(\);.*?^\s*\}\s*$/ms';

if (preg_match($getMembersPattern, $content)) {
    $content = preg_replace($getMembersPattern, '', $content, 1);
    echo "✅ getMembers method removed (keeping getChitMembers)\n";
} else {
    echo "⚠️ getMembers method pattern not found\n";
}

// ================================================================
// CLEAN UP EXTRA WHITESPACE
// ================================================================
echo "🧹 Cleaning up extra whitespace\n";

// Remove multiple consecutive empty lines
$content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);

// ================================================================
// WRITE FIXED FILE
// ================================================================
$backupPath = $chitControllerPath . '.backup.' . date('Y-m-d-H-i-s');
copy($chitControllerPath, $backupPath);
echo "💾 Backup created: $backupPath\n";

if (file_put_contents($chitControllerPath, $content)) {
    echo "✅ Fixed ChitController.php written successfully\n";
} else {
    echo "❌ Failed to write fixed file\n";
    exit(1);
}

// ================================================================
// VALIDATION
// ================================================================
echo "\n🔍 VALIDATION:\n";
echo "==============\n";

// Check for remaining duplicates
$methodCounts = [];
preg_match_all('/public function (\w+)\s*\(/', $content, $matches);

foreach ($matches[1] as $method) {
    $methodCounts[$method] = ($methodCounts[$method] ?? 0) + 1;
}

$duplicates = array_filter($methodCounts, function($count) { return $count > 1; });

if (empty($duplicates)) {
    echo "✅ No duplicate methods found\n";
} else {
    echo "❌ Remaining duplicates:\n";
    foreach ($duplicates as $method => $count) {
        echo "   - $method: $count occurrences\n";
    }
}

// Count total methods
$totalMethods = count($matches[1]);
echo "📊 Total methods in ChitController: $totalMethods\n";

// ================================================================
// SUMMARY
// ================================================================
echo "\n🎯 DUPLICATE METHODS FIX SUMMARY:\n";
echo "=================================\n\n";

echo "✅ REMOVED METHODS:\n";
echo "   1. addMember() - First version (JWT auth)\n";
echo "   2. removeMember() - First version (JWT auth)\n";
echo "   3. getMembers() - Keeping getChitMembers()\n\n";

echo "✅ KEPT METHODS (Better versions):\n";
echo "   1. addMember() - Enhanced version with authenticateUser()\n";
echo "   2. removeMember() - Enhanced version with authenticateUser()\n";
echo "   3. getChitMembers() - Better naming and functionality\n\n";

echo "🔧 IMPROVEMENTS:\n";
echo "   ✅ Consistent authentication (authenticateUser())\n";
echo "   ✅ Better authorization (admin + organizer roles)\n";
echo "   ✅ Enhanced error handling\n";
echo "   ✅ Richer response data (getDetailedInfo())\n";
echo "   ✅ Optional member_number support\n";
echo "   ✅ Cleaner code structure\n\n";

echo "📋 NEXT STEPS:\n";
echo "   1. Test API endpoints to ensure they work\n";
echo "   2. Update any route files if needed\n";
echo "   3. Test Flutter app connectivity\n";
echo "   4. Monitor logs for any issues\n\n";

echo "🚀 ChitController.php is now clean and optimized!\n";

// ================================================================
// CREATE VERIFICATION SCRIPT
// ================================================================
$verificationScript = '<?php
/**
 * Verification Script for ChitController
 */

echo "🔍 CHITCONTROLLER VERIFICATION\n";
echo "==============================\n\n";

$file = "backend/controllers/ChitController.php";
$content = file_get_contents($file);

// Check for method declarations
preg_match_all("/public function (\w+)\s*\(/", $content, $matches);
$methods = $matches[1];

echo "📊 Methods found: " . count($methods) . "\n\n";

// Check for duplicates
$methodCounts = array_count_values($methods);
$duplicates = array_filter($methodCounts, function($count) { return $count > 1; });

if (empty($duplicates)) {
    echo "✅ No duplicate methods found\n";
} else {
    echo "❌ Duplicate methods:\n";
    foreach ($duplicates as $method => $count) {
        echo "   - $method: $count times\n";
    }
}

echo "\n📋 All methods:\n";
foreach (array_unique($methods) as $method) {
    echo "   - $method()\n";
}

echo "\n🎯 Status: " . (empty($duplicates) ? "CLEAN ✅" : "NEEDS FIXING ❌") . "\n";
?>';

file_put_contents('verify_chitcontroller.php', $verificationScript);
echo "📋 Verification script created: verify_chitcontroller.php\n";
echo "   Run: php verify_chitcontroller.php\n\n";

echo "🎉 DUPLICATE METHODS FIX COMPLETED!\n";
echo "===================================\n";
echo "Your ChitController.php is now optimized and ready for production!\n";

?>
