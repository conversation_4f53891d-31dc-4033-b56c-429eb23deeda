<?php
/**
 * Payment Controller
 *
 * Handles payment operations
 */

// Import required dependencies
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../utils/JWT.php';
require_once __DIR__ . '/../models/Payment.php';
require_once __DIR__ . '/../models/ChitMember.php';
require_once __DIR__ . '/../models/User.php';

class PaymentController {
    
    /**
     * Get payments for authenticated user
     */
    public function getUserPayments() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $payment = new Payment();
            $payments = $payment->getUserPayments($userId);
            
            sendSuccess($payments, 'Payments retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get user payments error: " . $e->getMessage());
            sendError('Failed to retrieve payments', 500);
        }
    }
    
    /**
     * Get pending payments for authenticated user
     */
    public function getPendingPayments() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $payment = new Payment();
            $pendingPayments = $payment->getUserPendingPayments($userId);
            
            sendSuccess($pendingPayments, 'Pending payments retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get pending payments error: " . $e->getMessage());
            sendError('Failed to retrieve pending payments', 500);
        }
    }
    
    /**
     * Get overdue payments for authenticated user
     */
    public function getOverduePayments() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $payment = new Payment();
            $overduePayments = $payment->getUserOverduePayments($userId);
            
            sendSuccess($overduePayments, 'Overdue payments retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get overdue payments error: " . $e->getMessage());
            sendError('Failed to retrieve overdue payments', 500);
        }
    }
    
    /**
     * Get payments for a specific chit
     */
    public function getChitPayments($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user has access to this chit
            if (!$chit->hasUserAccess($userId)) {
                sendError('Access denied', 403);
            }
            
            $payment = new Payment();
            $payments = $payment->getChitPayments($chitId);
            
            sendSuccess($payments, 'Chit payments retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get chit payments error: " . $e->getMessage());
            sendError('Failed to retrieve chit payments', 500);
        }
    }
    
    /**
     * Record a payment
     */
    public function recordPayment() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['payment_id', 'payment_method'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        try {
            $payment = new Payment();
            
            if (!$payment->findById($data['payment_id'])) {
                sendError('Payment not found', 404);
            }
            
            // Check if user owns this payment or is the chit organizer
            $chit = new Chit();
            $chit->findById($payment->chit_id);
            
            if ($payment->user_id != $userId && $chit->organizer_id != $userId) {
                sendError('Access denied', 403);
            }
            
            // Check if payment is still pending
            if ($payment->status !== 'pending') {
                sendError('Payment is not pending', 400);
            }
            
            // Mark payment as paid
            $transactionId = $data['transaction_id'] ?? null;
            
            if ($payment->markAsPaid($data['payment_method'], $transactionId)) {
                // Log activity
                logActivity($userId, 'payment_recorded', [
                    'payment_id' => $payment->id,
                    'chit_id' => $payment->chit_id,
                    'amount' => $payment->amount,
                    'payment_method' => $data['payment_method']
                ]);
                
                sendSuccess($payment->getDetails(), 'Payment recorded successfully');
            } else {
                sendError('Failed to record payment', 500);
            }
            
        } catch (Exception $e) {
            error_log("Record payment error: " . $e->getMessage());
            sendError('Failed to record payment', 500);
        }
    }
    
    /**
     * Create a manual payment entry (organizer only)
     */
    public function createPayment() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['chit_id', 'user_id', 'amount', 'due_date', 'payment_type'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        // Validate data
        if ($data['amount'] <= 0) {
            sendError('Amount must be greater than 0', 400);
        }
        
        if (!strtotime($data['due_date'])) {
            sendError('Invalid due date format', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($data['chit_id'])) {
                sendError('Chit not found', 404);
            }
            
            // Check if user is organizer
            if ($chit->organizer_id != $userId) {
                sendError('Only organizer can create payments', 403);
            }
            
            // Check if target user is a member
            $chitMember = new ChitMember();
            if (!$chitMember->isMember($data['chit_id'], $data['user_id'])) {
                sendError('User is not a member of this chit', 400);
            }
            
            // Create payment
            $payment = new Payment();
            $payment->chit_id = $data['chit_id'];
            $payment->user_id = $data['user_id'];
            $payment->round_number = $data['round_number'] ?? null;
            $payment->amount = $data['amount'];
            $payment->due_date = $data['due_date'];
            $payment->status = 'pending';
            $payment->payment_type = $data['payment_type'];
            $payment->notes = sanitizeInput($data['notes'] ?? '');
            
            if ($payment->create()) {
                // Log activity
                logActivity($userId, 'payment_created', [
                    'payment_id' => $payment->id,
                    'chit_id' => $data['chit_id'],
                    'target_user_id' => $data['user_id'],
                    'amount' => $data['amount']
                ]);
                
                sendSuccess($payment->getDetails(), 'Payment created successfully');
            } else {
                sendError('Failed to create payment', 500);
            }
            
        } catch (Exception $e) {
            error_log("Create payment error: " . $e->getMessage());
            sendError('Failed to create payment', 500);
        }
    }
    
    /**
     * Update payment
     */
    public function updatePayment($paymentId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$paymentId) {
            sendError('Payment ID is required', 400);
        }
        
        $data = getRequestData();
        
        try {
            $payment = new Payment();
            
            if (!$payment->findById($paymentId)) {
                sendError('Payment not found', 404);
            }
            
            // Check if user is organizer
            $chit = new Chit();
            $chit->findById($payment->chit_id);
            
            if ($chit->organizer_id != $userId) {
                sendError('Only organizer can update payments', 403);
            }
            
            // Update allowed fields
            if (isset($data['amount']) && $data['amount'] > 0) {
                $payment->amount = $data['amount'];
            }
            
            if (isset($data['due_date']) && strtotime($data['due_date'])) {
                $payment->due_date = $data['due_date'];
            }
            
            if (isset($data['status'])) {
                $allowedStatuses = ['pending', 'paid', 'cancelled'];
                if (in_array($data['status'], $allowedStatuses)) {
                    $payment->status = $data['status'];
                    
                    if ($data['status'] === 'paid' && !$payment->paid_date) {
                        $payment->paid_date = date('Y-m-d');
                    }
                }
            }
            
            if (isset($data['notes'])) {
                $payment->notes = sanitizeInput($data['notes']);
            }
            
            if ($payment->update()) {
                // Log activity
                logActivity($userId, 'payment_updated', [
                    'payment_id' => $payment->id,
                    'chit_id' => $payment->chit_id
                ]);
                
                sendSuccess($payment->getDetails(), 'Payment updated successfully');
            } else {
                sendError('Failed to update payment', 500);
            }
            
        } catch (Exception $e) {
            error_log("Update payment error: " . $e->getMessage());
            sendError('Failed to update payment', 500);
        }
    }
    
    /**
     * Get payment statistics for a chit
     */
    public function getChitPaymentStats($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user has access to this chit
            if (!$chit->hasUserAccess($userId)) {
                sendError('Access denied', 403);
            }
            
            $payment = new Payment();
            $stats = $payment->getChitPaymentStats($chitId);
            
            sendSuccess($stats, 'Payment statistics retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get payment stats error: " . $e->getMessage());
            sendError('Failed to retrieve payment statistics', 500);
        }
    }
    
    /**
     * Get payment statistics for authenticated user
     */
    public function getUserPaymentStats() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $payment = new Payment();
            $stats = $payment->getUserPaymentStats($userId);
            
            sendSuccess($stats, 'User payment statistics retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get user payment stats error: " . $e->getMessage());
            sendError('Failed to retrieve user payment statistics', 500);
        }
    }
}
?>
