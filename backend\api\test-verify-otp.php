<?php
/**
 * Test Verify OTP Endpoint - Direct test
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Include configuration
    if (file_exists(__DIR__ . '/../config/env.php')) {
        require_once __DIR__ . '/../config/env.php';
    }
    
    // Include required files
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../utils/helpers.php';
    require_once __DIR__ . '/../controllers/AuthController.php';
    
    $debug = [];
    $debug['step'] = 'Starting verify OTP test';
    
    // Test database connection
    $db = new Database();
    $conn = $db->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    $debug['database'] = 'connected';
    
    // Get test email and OTP from query parameters or use defaults
    $testEmail = $_GET['email'] ?? '<EMAIL>';
    $testOtp = $_GET['otp'] ?? '';
    
    if (empty($testOtp)) {
        // Get the most recent OTP for this email
        $sql = "SELECT otp, type, expires_at, verified FROM otps WHERE email = ? ORDER BY created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$testEmail]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $testOtp = $result['otp'];
            $debug['found_otp'] = $result;
        } else {
            throw new Exception("No OTP found for email: $testEmail");
        }
    }
    
    $debug['test_data'] = [
        'email' => $testEmail,
        'otp' => $testOtp,
        'type' => 'registration'
    ];
    
    // Set up POST data for the controller
    $_POST = [
        'email' => $testEmail,
        'otp' => $testOtp,
        'type' => 'registration'
    ];
    
    $debug['post_data'] = 'set';
    
    // Test the actual AuthController verifyOTP method
    try {
        $debug['controller_test'] = 'starting';
        
        // Capture output from the controller
        ob_start();
        
        $authController = new AuthController();
        $authController->verifyOTP();
        
        $output = ob_get_clean();
        
        $debug['controller_test'] = 'completed';
        $debug['controller_output'] = $output;
        
        // Try to decode the output as JSON
        $decodedOutput = json_decode($output, true);
        if ($decodedOutput) {
            $debug['controller_response'] = $decodedOutput;
        }
        
        // Check OTP status after verification
        $sql = "SELECT otp, verified, verified_at FROM otps WHERE email = ? AND otp = ? ORDER BY created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$testEmail, $testOtp]);
        $afterResult = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $debug['otp_after_verification'] = $afterResult;
        
    } catch (Exception $e) {
        $debug['controller_test'] = 'error: ' . $e->getMessage();
        $debug['controller_error_file'] = $e->getFile();
        $debug['controller_error_line'] = $e->getLine();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Verify OTP Test Results',
        'debug' => $debug,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Test failed',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'debug' => $debug ?? []
    ], JSON_PRETTY_PRINT);
}

?>
