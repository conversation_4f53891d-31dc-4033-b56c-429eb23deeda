<?php
/**
 * Test Send OTP Endpoint - Direct test without routing
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $debug = [];
    $debug['step'] = 'Starting test';
    
    // Include configuration
    if (file_exists(__DIR__ . '/../config/env.php')) {
        require_once __DIR__ . '/../config/env.php';
        $debug['config'] = 'loaded';
    } else {
        $debug['config'] = 'missing';
    }
    
    // Include required files
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../utils/helpers.php';
    require_once __DIR__ . '/../controllers/AuthController.php';
    
    $debug['includes'] = 'loaded';
    
    // Test database connection
    $db = new Database();
    $conn = $db->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    $debug['database'] = 'connected';
    
    // Check if otps table exists
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'otps'");
        $result = $stmt->fetch();
        $debug['otps_table'] = $result ? 'exists' : 'missing';
    } catch (Exception $e) {
        $debug['otps_table'] = 'error: ' . $e->getMessage();
    }
    
    // Simulate the send OTP request
    $testEmail = '<EMAIL>';
    $testType = 'registration';
    
    $debug['test_data'] = [
        'email' => $testEmail,
        'type' => $testType
    ];
    
    // Set up POST data for the controller
    $_POST = [
        'email' => $testEmail,
        'type' => $testType
    ];
    
    $debug['post_data'] = 'set';
    
    // Test OTP generation and storage manually
    try {
        $otp = sprintf('%06d', mt_rand(0, 999999));
        $expires_at = date('Y-m-d H:i:s', time() + 600);
        
        // Delete any existing OTPs for this test email
        $sql = "DELETE FROM otps WHERE email = ? AND type = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$testEmail, $testType]);
        
        // Insert test OTP
        $sql = "INSERT INTO otps (email, otp, type, expires_at, created_at) VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([$testEmail, $otp, $testType, $expires_at]);
        
        if ($result) {
            $debug['manual_otp_insert'] = 'success';
            $debug['generated_otp'] = $otp;
            
            // Clean up test data
            $sql = "DELETE FROM otps WHERE email = ? AND type = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$testEmail, $testType]);
        } else {
            $debug['manual_otp_insert'] = 'failed';
        }
    } catch (Exception $e) {
        $debug['manual_otp_insert'] = 'error: ' . $e->getMessage();
    }
    
    // Test the actual AuthController method
    try {
        $debug['controller_test'] = 'starting';
        
        // Capture output from the controller
        ob_start();
        
        $authController = new AuthController();
        $authController->sendOTP();
        
        $output = ob_get_clean();
        
        $debug['controller_test'] = 'completed';
        $debug['controller_output'] = $output;
        
        // Try to decode the output as JSON
        $decodedOutput = json_decode($output, true);
        if ($decodedOutput) {
            $debug['controller_response'] = $decodedOutput;
        }
        
    } catch (Exception $e) {
        $debug['controller_test'] = 'error: ' . $e->getMessage();
        $debug['controller_error_file'] = $e->getFile();
        $debug['controller_error_line'] = $e->getLine();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Send OTP Test Results',
        'debug' => $debug,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Test failed',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'debug' => $debug ?? []
    ], JSON_PRETTY_PRINT);
}

?>
