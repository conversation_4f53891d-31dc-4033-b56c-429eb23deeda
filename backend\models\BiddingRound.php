<?php
/**
 * BiddingRound Model
 * 
 * Handles bidding round database operations
 */

class BiddingRound {
    private $db;
    public $conn;
    
    // BiddingRound properties
    public $id;
    public $chit_id;
    public $round_number;
    public $status;
    public $start_time;
    public $end_time;
    public $actual_end_time;
    public $winner_id;
    public $winning_amount;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * Create a new bidding round
     * 
     * @return bool True on success, false on failure
     */
    public function create() {
        $sql = "INSERT INTO bidding_rounds (chit_id, round_number, status, start_time, end_time) 
                VALUES (?, ?, ?, ?, ?)";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            $result = $stmt->execute([
                $this->chit_id,
                $this->round_number,
                $this->status ?? 'active',
                $this->start_time,
                $this->end_time
            ]);
            
            if ($result) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("BiddingRound creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find bidding round by ID
     * 
     * @param int $id BiddingRound ID
     * @return bool True if found, false otherwise
     */
    public function findById($id) {
        $sql = "SELECT * FROM bidding_rounds WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find bidding round by ID failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update bidding round
     * 
     * @return bool True on success, false on failure
     */
    public function update() {
        $sql = "UPDATE bidding_rounds SET status = ?, actual_end_time = ?, 
                winner_id = ?, winning_amount = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            return $stmt->execute([
                $this->status,
                $this->actual_end_time,
                $this->winner_id,
                $this->winning_amount,
                $this->id
            ]);
        } catch (PDOException $e) {
            error_log("BiddingRound update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if chit has active bidding
     * 
     * @param int $chitId Chit ID
     * @return bool True if has active bidding, false otherwise
     */
    public function hasActiveBidding($chitId) {
        $sql = "SELECT id FROM bidding_rounds WHERE chit_id = ? AND status = 'active'";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Check active bidding failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get current active bidding for a chit
     * 
     * @param int $chitId Chit ID
     * @return array|null Current bidding details or null
     */
    public function getCurrentBidding($chitId) {
        $sql = "SELECT br.*, u.name as winner_name
                FROM bidding_rounds br
                LEFT JOIN users u ON br.winner_id = u.id
                WHERE br.chit_id = ? AND br.status = 'active'
                ORDER BY br.created_at DESC LIMIT 1";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get current bidding failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get all bidding rounds for a chit
     * 
     * @param int $chitId Chit ID
     * @return array Array of bidding rounds
     */
    public function getChitRounds($chitId) {
        $sql = "SELECT br.*, u.name as winner_name
                FROM bidding_rounds br
                LEFT JOIN users u ON br.winner_id = u.id
                WHERE br.chit_id = ?
                ORDER BY br.round_number ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get chit rounds failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get bidding history for a chit
     * 
     * @param int $chitId Chit ID
     * @return array Array of completed bidding rounds
     */
    public function getChitHistory($chitId) {
        $sql = "SELECT br.*, u.name as winner_name, u.email as winner_email
                FROM bidding_rounds br
                LEFT JOIN users u ON br.winner_id = u.id
                WHERE br.chit_id = ? AND br.status = 'completed'
                ORDER BY br.round_number DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            $rounds = $stmt->fetchAll();
            
            // Get bids for each round
            foreach ($rounds as &$round) {
                $bid = new Bid();
                $round['bids'] = $bid->getRoundBids($round['id']);
            }
            
            return $rounds;
        } catch (PDOException $e) {
            error_log("Get bidding history failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get bidding round details with bids
     * 
     * @return array Bidding round details
     */
    public function getDetails() {
        $details = [
            'id' => $this->id,
            'chit_id' => $this->chit_id,
            'round_number' => $this->round_number,
            'status' => $this->status,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
            'actual_end_time' => $this->actual_end_time,
            'winner_id' => $this->winner_id,
            'winning_amount' => $this->winning_amount,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
        
        // Get winner details
        if ($this->winner_id) {
            $user = new User();
            if ($user->findById($this->winner_id)) {
                $details['winner'] = $user->getPublicData();
            }
        }
        
        // Get bids
        $bid = new Bid();
        $details['bids'] = $bid->getRoundBids($this->id);
        
        return $details;
    }
    
    /**
     * End expired bidding rounds
     * 
     * @return int Number of rounds ended
     */
    public function endExpiredRounds() {
        $sql = "UPDATE bidding_rounds SET status = 'expired', 
                actual_end_time = CURRENT_TIMESTAMP 
                WHERE status = 'active' AND end_time < CURRENT_TIMESTAMP";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("End expired rounds failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get upcoming bidding rounds
     * 
     * @param int $limit Limit number of results
     * @return array Array of upcoming rounds
     */
    public function getUpcomingRounds($limit = 10) {
        $sql = "SELECT br.*, c.name as chit_name, u.name as organizer_name
                FROM bidding_rounds br
                LEFT JOIN chits c ON br.chit_id = c.id
                LEFT JOIN users u ON c.organizer_id = u.id
                WHERE br.status = 'active' AND br.start_time > CURRENT_TIMESTAMP
                ORDER BY br.start_time ASC LIMIT ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get upcoming rounds failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get active bidding rounds
     * 
     * @param int $limit Limit number of results
     * @return array Array of active rounds
     */
    public function getActiveRounds($limit = 10) {
        $sql = "SELECT br.*, c.name as chit_name, u.name as organizer_name,
                (SELECT COUNT(*) FROM bids b WHERE b.bidding_round_id = br.id) as bid_count
                FROM bidding_rounds br
                LEFT JOIN chits c ON br.chit_id = c.id
                LEFT JOIN users u ON c.organizer_id = u.id
                WHERE br.status = 'active' AND br.start_time <= CURRENT_TIMESTAMP 
                AND br.end_time > CURRENT_TIMESTAMP
                ORDER BY br.end_time ASC LIMIT ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get active rounds failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Set object properties from database row
     * 
     * @param array $row Database row
     */
    private function setProperties($row) {
        $this->id = $row['id'];
        $this->chit_id = $row['chit_id'];
        $this->round_number = $row['round_number'];
        $this->status = $row['status'];
        $this->start_time = $row['start_time'];
        $this->end_time = $row['end_time'];
        $this->actual_end_time = $row['actual_end_time'];
        $this->winner_id = $row['winner_id'];
        $this->winning_amount = $row['winning_amount'];
        $this->created_at = $row['created_at'];
        $this->updated_at = $row['updated_at'];
    }
}
?>
