<?php
/**
 * Debug Script for Deployment Issues
 * 
 * Simple diagnostic script to identify deployment problems
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 DEPLOYMENT DEBUG SCRIPT\n";
echo "==========================\n\n";

// 1. Check PHP version
echo "1. PHP Version: " . PHP_VERSION . "\n";

// 2. Check if files exist
echo "\n2. File Existence Check:\n";
$files = [
    'config/database.php',
    'utils/helpers.php',
    'utils/JWT.php',
    'middleware/SecurityMiddleware.php',
    'controllers/AuthController.php'
];

foreach ($files as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "✅ $file - EXISTS\n";
    } else {
        echo "❌ $file - MISSING\n";
    }
}

// 3. Test basic PHP syntax
echo "\n3. Testing helpers.php syntax:\n";
$output = [];
$returnCode = 0;
exec("php -l " . __DIR__ . "/utils/helpers.php 2>&1", $output, $returnCode);

if ($returnCode === 0) {
    echo "✅ helpers.php syntax is valid\n";
} else {
    echo "❌ helpers.php syntax error:\n";
    foreach ($output as $line) {
        echo "   $line\n";
    }
}

// 4. Test database configuration
echo "\n4. Testing database configuration:\n";
try {
    if (file_exists(__DIR__ . '/config/database.php')) {
        require_once __DIR__ . '/config/database.php';
        echo "✅ Database config loaded\n";
        
        // Check if Database class exists
        if (class_exists('Database')) {
            echo "✅ Database class available\n";
            
            // Try to create instance
            $db = new Database();
            echo "✅ Database instance created\n";
            
            // Try to get connection
            $conn = $db->getConnection();
            if ($conn) {
                echo "✅ Database connection successful\n";
            } else {
                echo "❌ Database connection failed\n";
            }
        } else {
            echo "❌ Database class not found\n";
        }
    } else {
        echo "❌ Database config file not found\n";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// 5. Test helpers.php functions
echo "\n5. Testing helper functions:\n";
try {
    if (file_exists(__DIR__ . '/utils/helpers.php')) {
        require_once __DIR__ . '/utils/helpers.php';
        echo "✅ helpers.php loaded\n";
        
        // Check critical functions
        $functions = ['hashPassword', 'verifyPassword', 'sendResponse', 'getRequestData'];
        foreach ($functions as $func) {
            if (function_exists($func)) {
                echo "✅ Function $func available\n";
            } else {
                echo "❌ Function $func missing\n";
            }
        }
        
        // Test a simple function
        if (function_exists('hashPassword')) {
            $hash = hashPassword('test123');
            if ($hash) {
                echo "✅ hashPassword function working\n";
            } else {
                echo "❌ hashPassword function failed\n";
            }
        }
        
    } else {
        echo "❌ helpers.php file not found\n";
    }
} catch (Exception $e) {
    echo "❌ helpers.php error: " . $e->getMessage() . "\n";
}

// 6. Test JWT class
echo "\n6. Testing JWT class:\n";
try {
    if (file_exists(__DIR__ . '/utils/JWT.php')) {
        require_once __DIR__ . '/utils/JWT.php';
        echo "✅ JWT.php loaded\n";
        
        if (class_exists('JWT')) {
            echo "✅ JWT class available\n";
        } else {
            echo "❌ JWT class not found\n";
        }
    } else {
        echo "❌ JWT.php file not found\n";
    }
} catch (Exception $e) {
    echo "❌ JWT error: " . $e->getMessage() . "\n";
}

// 7. Test basic API response
echo "\n7. Testing API response functions:\n";
try {
    if (function_exists('sendResponse')) {
        // Capture output
        ob_start();
        sendResponse(['test' => 'success'], 200);
        $output = ob_get_clean();
        
        if (!empty($output)) {
            echo "✅ sendResponse function working\n";
            echo "   Output: " . substr($output, 0, 100) . "...\n";
        } else {
            echo "❌ sendResponse function not producing output\n";
        }
    }
} catch (Exception $e) {
    echo "❌ API response error: " . $e->getMessage() . "\n";
}

// 8. Environment information
echo "\n8. Environment Information:\n";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
echo "Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "\n";
echo "Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "\n";

// 9. Check for common issues
echo "\n9. Common Issues Check:\n";

// Check for BOM in files
$bomFiles = [];
foreach ($files as $file) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        $content = file_get_contents($fullPath);
        if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
            $bomFiles[] = $file;
        }
    }
}

if (empty($bomFiles)) {
    echo "✅ No BOM issues found\n";
} else {
    echo "❌ BOM found in files: " . implode(', ', $bomFiles) . "\n";
}

// Check for short open tags
$shortTagFiles = [];
foreach ($files as $file) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        $content = file_get_contents($fullPath);
        if (strpos($content, '<?') !== false && strpos($content, '<?php') === false) {
            $shortTagFiles[] = $file;
        }
    }
}

if (empty($shortTagFiles)) {
    echo "✅ No short tag issues found\n";
} else {
    echo "❌ Short tags found in files: " . implode(', ', $shortTagFiles) . "\n";
}

echo "\n🎯 DEBUG COMPLETE\n";
echo "=================\n";
echo "Check the results above to identify any remaining issues.\n";
echo "If all checks pass, the issue might be in the API routing or .htaccess configuration.\n";

?>
