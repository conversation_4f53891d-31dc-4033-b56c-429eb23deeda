// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chit_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Chit _$ChitFromJson(Map<String, dynamic> json) => Chit(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      numberOfMembers: (json['numberOfMembers'] as num).toInt(),
      frequency: $enumDecode(_$ChitFrequencyEnumMap, json['frequency']),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      status: $enumDecode(_$ChitStatusEnumMap, json['status']),
      ownerId: json['ownerId'] as String,
      ownerName: json['ownerName'] as String,
      commissionPercentage: (json['commissionPercentage'] as num?)?.toDouble(),
      members: (json['members'] as List<dynamic>)
          .map((e) => ChitMember.fromJson(e as Map<String, dynamic>))
          .toList(),
      biddingRounds: (json['biddingRounds'] as List<dynamic>)
          .map((e) => BiddingRound.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isOwner: json['isOwner'] as bool? ?? false,
    );

Map<String, dynamic> _$ChitToJson(Chit instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'totalAmount': instance.totalAmount,
      'numberOfMembers': instance.numberOfMembers,
      'frequency': _$ChitFrequencyEnumMap[instance.frequency]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'status': _$ChitStatusEnumMap[instance.status]!,
      'ownerId': instance.ownerId,
      'ownerName': instance.ownerName,
      'commissionPercentage': instance.commissionPercentage,
      'members': instance.members,
      'biddingRounds': instance.biddingRounds,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isOwner': instance.isOwner,
    };

const _$ChitFrequencyEnumMap = {
  ChitFrequency.monthly: 'monthly',
  ChitFrequency.quarterly: 'quarterly',
  ChitFrequency.halfYearly: 'half_yearly',
  ChitFrequency.yearly: 'yearly',
};

const _$ChitStatusEnumMap = {
  ChitStatus.draft: 'draft',
  ChitStatus.active: 'active',
  ChitStatus.completed: 'completed',
  ChitStatus.cancelled: 'cancelled',
};

ChitMember _$ChitMemberFromJson(Map<String, dynamic> json) => ChitMember(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String,
      hasWon: json['hasWon'] as bool? ?? false,
      winningRound: (json['winningRound'] as num?)?.toInt(),
      winningAmount: (json['winningAmount'] as num?)?.toDouble(),
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      status: $enumDecodeNullable(_$MemberStatusEnumMap, json['status']) ??
          MemberStatus.active,
      role: $enumDecodeNullable(_$MemberRoleEnumMap, json['role']) ??
          MemberRole.member,
    );

Map<String, dynamic> _$ChitMemberToJson(ChitMember instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'phone': instance.phone,
      'email': instance.email,
      'hasWon': instance.hasWon,
      'winningRound': instance.winningRound,
      'winningAmount': instance.winningAmount,
      'joinedAt': instance.joinedAt.toIso8601String(),
      'status': _$MemberStatusEnumMap[instance.status]!,
      'role': _$MemberRoleEnumMap[instance.role]!,
    };

const _$MemberStatusEnumMap = {
  MemberStatus.active: 'active',
  MemberStatus.inactive: 'inactive',
  MemberStatus.removed: 'removed',
};

const _$MemberRoleEnumMap = {
  MemberRole.organizer: 'organizer',
  MemberRole.member: 'member',
};

BiddingRound _$BiddingRoundFromJson(Map<String, dynamic> json) => BiddingRound(
      id: json['id'] as String,
      roundNumber: (json['roundNumber'] as num).toInt(),
      date: DateTime.parse(json['date'] as String),
      startingBid: (json['startingBid'] as num).toDouble(),
      winnerId: json['winnerId'] as String?,
      winnerName: json['winnerName'] as String?,
      winningBid: (json['winningBid'] as num?)?.toDouble(),
      finalAmount: (json['finalAmount'] as num?)?.toDouble(),
      bids: (json['bids'] as List<dynamic>)
          .map((e) => Bid.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: $enumDecode(_$BiddingStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      endTime: json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
    );

Map<String, dynamic> _$BiddingRoundToJson(BiddingRound instance) =>
    <String, dynamic>{
      'id': instance.id,
      'roundNumber': instance.roundNumber,
      'date': instance.date.toIso8601String(),
      'startingBid': instance.startingBid,
      'winnerId': instance.winnerId,
      'winnerName': instance.winnerName,
      'winningBid': instance.winningBid,
      'finalAmount': instance.finalAmount,
      'bids': instance.bids,
      'status': _$BiddingStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
    };

const _$BiddingStatusEnumMap = {
  BiddingStatus.pending: 'pending',
  BiddingStatus.inProgress: 'in_progress',
  BiddingStatus.completed: 'completed',
  BiddingStatus.cancelled: 'cancelled',
};

Bid _$BidFromJson(Map<String, dynamic> json) => Bid(
      id: json['id'] as String,
      memberId: json['memberId'] as String,
      memberName: json['memberName'] as String,
      amount: (json['amount'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$BidToJson(Bid instance) => <String, dynamic>{
      'id': instance.id,
      'memberId': instance.memberId,
      'memberName': instance.memberName,
      'amount': instance.amount,
      'timestamp': instance.timestamp.toIso8601String(),
    };
