import 'package:json_annotation/json_annotation.dart';

part 'chit_model.g.dart';

@JsonSerializable()
class Chit {
  final String id;
  final String name;
  final String description;
  @JsonKey(name: 'chit_code')
  final String? chitCode;
  final double totalAmount;
  final int numberOfMembers;
  final ChitFrequency frequency;
  final DateTime startDate;
  final DateTime? endDate;
  final ChitStatus status;
  final String ownerId;
  final String ownerName;
  final double? commissionPercentage;
  final List<ChitMember> members;
  final List<BiddingRound> biddingRounds;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isOwner;

  const Chit({
    required this.id,
    required this.name,
    required this.description,
    this.chitCode,
    required this.totalAmount,
    required this.numberOfMembers,
    required this.frequency,
    required this.startDate,
    this.endDate,
    required this.status,
    required this.ownerId,
    required this.ownerName,
    this.commissionPercentage,
    required this.members,
    required this.biddingRounds,
    required this.createdAt,
    required this.updatedAt,
    this.isOwner = false,
  });

  factory Chit.fromJson(Map<String, dynamic> json) => _$ChitFromJson(json);
  Map<String, dynamic> toJson() => _$ChitToJson(this);

  // Calculate individual member contribution per round
  double get memberContribution => totalAmount / numberOfMembers;

  // Calculate commission amount
  double get commissionAmount => 
      commissionPercentage != null ? (totalAmount * commissionPercentage! / 100) : 0;

  // Get current round number
  int get currentRound => biddingRounds.length + 1;

  // Check if chit is completed
  bool get isCompleted => status == ChitStatus.completed || currentRound > numberOfMembers;

  // Get next meeting date
  DateTime? getNextMeetingDate() {
    if (isCompleted) return null;

    DateTime nextDate = startDate;
    for (int i = 0; i < currentRound - 1; i++) {
      nextDate = _addFrequencyToDate(nextDate);
    }
    return nextDate;
  }

  // Get remaining rounds
  int get remainingRounds => numberOfMembers - biddingRounds.length;

  // Get duration in months
  int get durationMonths {
    switch (frequency) {
      case ChitFrequency.monthly:
        return numberOfMembers;
      case ChitFrequency.quarterly:
        return numberOfMembers * 3;
      case ChitFrequency.halfYearly:
        return numberOfMembers * 6;
      case ChitFrequency.yearly:
        return numberOfMembers * 12;
    }
  }

  // Get active members (who haven't won yet)
  List<ChitMember> get activeMembers =>
      members.where((member) => !member.hasWon).toList();

  // Get winners
  List<ChitMember> get winners =>
      members.where((member) => member.hasWon).toList();

  DateTime _addFrequencyToDate(DateTime date) {
    switch (frequency) {
      case ChitFrequency.monthly:
        return DateTime(date.year, date.month + 1, date.day);
      case ChitFrequency.quarterly:
        return DateTime(date.year, date.month + 3, date.day);
      case ChitFrequency.halfYearly:
        return DateTime(date.year, date.month + 6, date.day);
      case ChitFrequency.yearly:
        return DateTime(date.year + 1, date.month, date.day);
    }
  }

  Chit copyWith({
    String? id,
    String? name,
    String? description,
    double? totalAmount,
    int? numberOfMembers,
    ChitFrequency? frequency,
    DateTime? startDate,
    DateTime? endDate,
    ChitStatus? status,
    String? ownerId,
    String? ownerName,
    double? commissionPercentage,
    List<ChitMember>? members,
    List<BiddingRound>? biddingRounds,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isOwner,
  }) {
    return Chit(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      totalAmount: totalAmount ?? this.totalAmount,
      numberOfMembers: numberOfMembers ?? this.numberOfMembers,
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      ownerId: ownerId ?? this.ownerId,
      ownerName: ownerName ?? this.ownerName,
      commissionPercentage: commissionPercentage ?? this.commissionPercentage,
      members: members ?? this.members,
      biddingRounds: biddingRounds ?? this.biddingRounds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isOwner: isOwner ?? this.isOwner,
    );
  }
}

@JsonSerializable()
class ChitMember {
  final String id;
  final String userId;
  final String name;
  final String phone;
  final String email;
  final bool hasWon;
  final int? winningRound;
  final double? winningAmount;
  final DateTime joinedAt;
  final MemberStatus status;
  final MemberRole role;

  const ChitMember({
    required this.id,
    required this.userId,
    required this.name,
    required this.phone,
    required this.email,
    this.hasWon = false,
    this.winningRound,
    this.winningAmount,
    required this.joinedAt,
    this.status = MemberStatus.active,
    this.role = MemberRole.member,
  });

  // Convenience getters
  bool get isActive => status == MemberStatus.active;
  DateTime get joinedDate => joinedAt;

  factory ChitMember.fromJson(Map<String, dynamic> json) => _$ChitMemberFromJson(json);
  Map<String, dynamic> toJson() => _$ChitMemberToJson(this);
}

@JsonSerializable()
class BiddingRound {
  final String id;
  final int roundNumber;
  final DateTime date;
  final double startingBid;
  final String? winnerId;
  final String? winnerName;
  final double? winningBid;
  final double? finalAmount;
  final List<Bid> bids;
  final BiddingStatus status;
  final DateTime createdAt;
  final DateTime? endTime;

  const BiddingRound({
    required this.id,
    required this.roundNumber,
    required this.date,
    required this.startingBid,
    this.winnerId,
    this.winnerName,
    this.winningBid,
    this.finalAmount,
    required this.bids,
    required this.status,
    required this.createdAt,
    this.endTime,
  });

  factory BiddingRound.fromJson(Map<String, dynamic> json) => _$BiddingRoundFromJson(json);
  Map<String, dynamic> toJson() => _$BiddingRoundToJson(this);
}

@JsonSerializable()
class Bid {
  final String id;
  final String memberId;
  final String memberName;
  final double amount;
  final DateTime timestamp;

  const Bid({
    required this.id,
    required this.memberId,
    required this.memberName,
    required this.amount,
    required this.timestamp,
  });

  factory Bid.fromJson(Map<String, dynamic> json) => _$BidFromJson(json);
  Map<String, dynamic> toJson() => _$BidToJson(this);
}

enum ChitFrequency {
  @JsonValue('monthly')
  monthly,
  @JsonValue('quarterly')
  quarterly,
  @JsonValue('half_yearly')
  halfYearly,
  @JsonValue('yearly')
  yearly,
}

enum ChitStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('active')
  active,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
}

enum MemberStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('removed')
  removed,
}

enum MemberRole {
  @JsonValue('organizer')
  organizer,
  @JsonValue('member')
  member,
}

enum BiddingStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
}

// Extensions for better display
extension ChitFrequencyExtension on ChitFrequency {
  String get displayName {
    switch (this) {
      case ChitFrequency.monthly:
        return 'Monthly';
      case ChitFrequency.quarterly:
        return 'Quarterly';
      case ChitFrequency.halfYearly:
        return 'Half Yearly';
      case ChitFrequency.yearly:
        return 'Yearly';
    }
  }
}

extension ChitStatusExtension on ChitStatus {
  String get displayName {
    switch (this) {
      case ChitStatus.draft:
        return 'Draft';
      case ChitStatus.active:
        return 'Active';
      case ChitStatus.completed:
        return 'Completed';
      case ChitStatus.cancelled:
        return 'Cancelled';
    }
  }
}

extension MemberRoleExtension on MemberRole {
  String get displayName {
    switch (this) {
      case MemberRole.organizer:
        return 'Organizer';
      case MemberRole.member:
        return 'Member';
    }
  }
}
