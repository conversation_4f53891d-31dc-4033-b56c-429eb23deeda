import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/models/chit_model.dart';
import '../../../core/router/app_router.dart';
import '../widgets/chit_card.dart';

class ChitListScreen extends StatefulWidget {
  const ChitListScreen({super.key});

  @override
  State<ChitListScreen> createState() => _ChitListScreenState();
}

class _ChitListScreenState extends State<ChitListScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  ChitStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadChits();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadChits() async {
    final chitProvider = Provider.of<ChitProvider>(context, listen: false);
    await chitProvider.refreshChits();
  }

  Future<void> _handleRefresh() async {
    await _loadChits();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('My Chits'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.home),
          onPressed: () => AppNavigation.goToDashboard(context),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Active'),
            Tab(text: 'Completed'),
            Tab(text: 'Draft'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Bar
          if (_searchQuery.isNotEmpty || _selectedStatus != null)
            _buildActiveFilters(),

          // Chit List
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildChitList(null), // All chits
                _buildChitList(ChitStatus.active),
                _buildChitList(ChitStatus.completed),
                _buildChitList(ChitStatus.draft),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          AppNavigation.goToCreateChit(context);
        },
        icon: const Icon(Icons.add),
        label: const Text('New Chit'),
      ),
    );
  }

  Widget _buildActiveFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).colorScheme.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Active Filters',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              if (_searchQuery.isNotEmpty)
                Chip(
                  label: Text('Search: $_searchQuery'),
                  onDeleted: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                ),
              if (_selectedStatus != null)
                Chip(
                  label: Text('Status: ${_selectedStatus!.displayName}'),
                  onDeleted: () {
                    setState(() {
                      _selectedStatus = null;
                    });
                  },
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChitList(ChitStatus? statusFilter) {
    return Consumer<ChitProvider>(
      builder: (context, chitProvider, child) {
        if (chitProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        List<Chit> chits = chitProvider.chits;

        // Apply status filter
        if (statusFilter != null) {
          chits = chits.where((chit) => chit.status == statusFilter).toList();
        }

        // Apply search filter
        if (_searchQuery.isNotEmpty) {
          chits = chits.where((chit) =>
              chit.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              chit.description.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
        }

        // Apply additional status filter
        if (_selectedStatus != null) {
          chits = chits.where((chit) => chit.status == _selectedStatus).toList();
        }

        if (chits.isEmpty) {
          return _buildEmptyState(statusFilter);
        }

        return RefreshIndicator(
          onRefresh: _handleRefresh,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: chits.length,
            itemBuilder: (context, index) {
              final chit = chits[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: ChitCard(
                  chit: chit,
                  onTap: () {
                    AppNavigation.goToChitDetails(context, chit.id);
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(ChitStatus? statusFilter) {
    String title;
    String subtitle;
    IconData icon;

    switch (statusFilter) {
      case ChitStatus.active:
        title = 'No Active Chits';
        subtitle = 'You don\'t have any active chit funds at the moment';
        icon = Icons.play_circle_outline;
        break;
      case ChitStatus.completed:
        title = 'No Completed Chits';
        subtitle = 'You haven\'t completed any chit funds yet';
        icon = Icons.check_circle_outline;
        break;
      case ChitStatus.draft:
        title = 'No Draft Chits';
        subtitle = 'You don\'t have any draft chits';
        icon = Icons.drafts_outlined;
        break;
      default:
        title = 'No Chits Found';
        subtitle = 'Create your first chit fund to get started';
        icon = Icons.account_balance_wallet_outlined;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (statusFilter == null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  AppNavigation.goToCreateChit(context);
                },
                icon: const Icon(Icons.add),
                label: const Text('Create Chit'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Chits'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Enter chit name or description',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _searchQuery = '';
              });
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Chits'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('All'),
                  selected: _selectedStatus == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatus = null;
                    });
                  },
                ),
                ...ChitStatus.values.map((status) => FilterChip(
                  label: Text(status.displayName),
                  selected: _selectedStatus == status,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatus = selected ? status : null;
                    });
                  },
                )),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _selectedStatus = null;
              });
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }
}
