<?php
/**
 * Environment Configuration
 * 
 * Database and application configuration for production
 */

// Application Configuration
define('APP_ENV', 'production');
define('APP_DEBUG', false);
define('APP_NAME', 'Chit Fund Manager');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://chit.mobunite.com');

// Database Configuration - UPDATE THESE WITH YOUR ACTUAL CREDENTIALS
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_NAME', 'mobilego_chitfund'); // Update with your actual database name
define('DB_USER', 'mobilego_chituser'); // Update with your actual database user
define('DB_PASS', 'your_database_password'); // Update with your actual database password
define('DB_CHARSET', 'utf8mb4');

// LiteSpeed Optimizations
define('DB_PERSISTENT', true);
define('DB_TIMEOUT', 10);
define('DB_POOL_SIZE', 5);

// Cache Configuration
define('CACHE_ENABLED', true);
define('CACHE_TTL', 1800);
define('CACHE_PREFIX', 'chitfund_');

// Security Configuration
define('JWT_SECRET', 'your-super-secret-jwt-key-change-this-in-production');
define('ENCRYPTION_KEY', 'your-encryption-key-32-chars-long');
define('OTP_EXPIRY', 300); // 5 minutes
define('SESSION_TIMEOUT', 3600); // 1 hour

// Email Configuration (for OTP)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'Chit Fund Manager');

// API Configuration
define('API_RATE_LIMIT', 100); // requests per minute
define('API_TIMEOUT', 30); // seconds

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'pdf']);
define('UPLOAD_PATH', __DIR__ . '/../uploads/');

// SMS Configuration
define('SMS_ENABLED', false);
define('SMS_PROVIDER', 'textlocal'); // textlocal, twilio, etc.
define('SMS_API_KEY', ''); // Your SMS provider API key
define('SMS_SENDER_ID', 'CHITFUND'); // Your sender ID

// Backup Configuration
define('BACKUP_ENABLED', true);
define('BACKUP_PATH', __DIR__ . '/../backups/');
define('BACKUP_RETENTION_DAYS', 30);

// Notification Configuration
define('PUSH_NOTIFICATIONS_ENABLED', true);
define('FCM_SERVER_KEY', 'your-fcm-server-key');
define('FCM_SENDER_ID', 'your-fcm-sender-id');

// Error Reporting
if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// Timezone
date_default_timezone_set('Asia/Kolkata');

?>
