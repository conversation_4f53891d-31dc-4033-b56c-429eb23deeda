<?php
/**
 * Chit Model
 * 
 * Handles chit fund database operations
 */

class Chit {
    private $db;
    private $conn;
    
    // Chit properties
    public $id;
    public $name;
    public $description;
    public $total_amount;
    public $monthly_amount;
    public $number_of_members;
    public $start_date;
    public $end_date;
    public $organizer_id;
    public $status;
    public $current_round;
    public $commission_percentage;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * Create a new chit
     * 
     * @return bool True on success, false on failure
     */
    public function create() {
        $sql = "INSERT INTO chits (name, description, total_amount, monthly_amount, 
                number_of_members, start_date, organizer_id, status, commission_percentage) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            $result = $stmt->execute([
                $this->name,
                $this->description,
                $this->total_amount,
                $this->monthly_amount,
                $this->number_of_members,
                $this->start_date,
                $this->organizer_id,
                $this->status ?? 'draft',
                $this->commission_percentage ?? 5.0
            ]);
            
            if ($result) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Chit creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find chit by ID
     * 
     * @param int $id Chit ID
     * @return bool True if found, false otherwise
     */
    public function findById($id) {
        $sql = "SELECT * FROM chits WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find chit by ID failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update chit
     * 
     * @return bool True on success, false on failure
     */
    public function update() {
        $sql = "UPDATE chits SET name = ?, description = ?, status = ?, 
                commission_percentage = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            return $stmt->execute([
                $this->name,
                $this->description,
                $this->status,
                $this->commission_percentage,
                $this->id
            ]);
        } catch (PDOException $e) {
            error_log("Chit update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete chit
     * 
     * @return bool True on success, false on failure
     */
    public function delete() {
        $sql = "DELETE FROM chits WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Chit deletion failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get chits for a specific user
     * 
     * @param int $userId User ID
     * @return array Array of chits
     */
    public function getUserChits($userId) {
        $sql = "SELECT c.*, u.name as organizer_name,
                (SELECT COUNT(*) FROM chit_members cm WHERE cm.chit_id = c.id) as member_count
                FROM chits c 
                LEFT JOIN users u ON c.organizer_id = u.id
                WHERE c.id IN (
                    SELECT DISTINCT chit_id FROM chit_members WHERE user_id = ?
                )
                ORDER BY c.created_at DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get user chits failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get full chit details including members and bidding rounds
     * 
     * @return array Chit details
     */
    public function getFullDetails() {
        $chitData = [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'total_amount' => $this->total_amount,
            'monthly_amount' => $this->monthly_amount,
            'number_of_members' => $this->number_of_members,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'organizer_id' => $this->organizer_id,
            'status' => $this->status,
            'current_round' => $this->current_round,
            'commission_percentage' => $this->commission_percentage,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
        
        // Get organizer details
        $user = new User();
        if ($user->findById($this->organizer_id)) {
            $chitData['organizer'] = $user->getPublicData();
        }
        
        // Get members
        $member = new ChitMember();
        $chitData['members'] = $member->getChitMembers($this->id);
        
        // Get bidding rounds
        $biddingRound = new BiddingRound();
        $chitData['bidding_rounds'] = $biddingRound->getChitRounds($this->id);
        
        // Get payments
        $payment = new Payment();
        $chitData['payments'] = $payment->getChitPayments($this->id);
        
        return $chitData;
    }
    
    /**
     * Check if user has access to this chit
     * 
     * @param int $userId User ID
     * @return bool True if user has access, false otherwise
     */
    public function hasUserAccess($userId) {
        $sql = "SELECT id FROM chit_members WHERE chit_id = ? AND user_id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->id, $userId]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Check user access failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get member count for this chit
     * 
     * @return int Member count
     */
    public function getMemberCount() {
        $sql = "SELECT COUNT(*) as count FROM chit_members WHERE chit_id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->id]);
            
            $row = $stmt->fetch();
            return $row['count'] ?? 0;
        } catch (PDOException $e) {
            error_log("Get member count failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Start chit (change status to active)
     * 
     * @return bool True on success, false on failure
     */
    public function start() {
        $sql = "UPDATE chits SET status = 'active', current_round = 1, 
                updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Start chit failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Complete chit
     * 
     * @return bool True on success, false on failure
     */
    public function complete() {
        $sql = "UPDATE chits SET status = 'completed', end_date = CURRENT_DATE, 
                updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Complete chit failed: " . $e->getMessage());
            return false;
        }
    }
    

    
    /**
     * Search chits
     * 
     * @param string $query Search query
     * @param int $limit Limit number of results
     * @return array Array of chits
     */
    public function search($query, $limit = 20) {
        $sql = "SELECT c.*, u.name as organizer_name,
                (SELECT COUNT(*) FROM chit_members cm WHERE cm.chit_id = c.id) as member_count
                FROM chits c 
                LEFT JOIN users u ON c.organizer_id = u.id
                WHERE c.name LIKE ? OR c.description LIKE ?
                ORDER BY c.name ASC LIMIT ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $searchTerm = "%$query%";
            $stmt->execute([$searchTerm, $searchTerm, $limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Chit search failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Set object properties from database row
     * 
     * @param array $row Database row
     */
    private function setProperties($row) {
        $this->id = $row['id'];
        $this->name = $row['name'];
        $this->description = $row['description'];
        $this->total_amount = $row['total_amount'];
        $this->monthly_amount = $row['monthly_amount'];
        $this->number_of_members = $row['number_of_members'];
        $this->start_date = $row['start_date'];
        $this->end_date = $row['end_date'];
        $this->organizer_id = $row['organizer_id'];
        $this->status = $row['status'];
        $this->current_round = $row['current_round'];
        $this->commission_percentage = $row['commission_percentage'];
        $this->created_at = $row['created_at'];
        $this->updated_at = $row['updated_at'];
    }

    /**
     * Get all chits with pagination and filters
     */
    public function getAll($page = 1, $limit = 20, $search = '', $status = null, $organizerId = null) {
        try {
            $offset = ($page - 1) * $limit;

            $whereConditions = [];
            $params = [];

            if (!empty($search)) {
                $whereConditions[] = "c.name LIKE ?";
                $params[] = "%$search%";
            }

            if ($status) {
                $whereConditions[] = "c.status = ?";
                $params[] = $status;
            }

            if ($organizerId) {
                $whereConditions[] = "c.organizer_id = ?";
                $params[] = $organizerId;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get total count
            $countSql = "SELECT COUNT(*) FROM chits c $whereClause";
            $stmt = $this->db->prepare($countSql);
            $stmt->execute($params);
            $total = (int)$stmt->fetchColumn();

            // Get chits with organizer info
            $sql = "SELECT c.*, u.name as organizer_name,
                           COUNT(cm.id) as member_count,
                           COUNT(CASE WHEN cm.status = 'active' THEN 1 END) as active_members
                    FROM chits c
                    LEFT JOIN users u ON c.organizer_id = u.id
                    LEFT JOIN chit_members cm ON c.id = cm.chit_id
                    $whereClause
                    GROUP BY c.id
                    ORDER BY c.created_at DESC
                    LIMIT ? OFFSET ?";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $chits = $stmt->fetchAll();

            return [
                'chits' => $chits,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ];

        } catch (Exception $e) {
            error_log("Get all chits error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user has access to this chit (as organizer or member)
     */
    public function hasUserAccess($userId) {
        try {
            // Check if user is organizer
            if ($this->organizer_id == $userId) {
                return true;
            }

            // Check if user is a member
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM chit_members WHERE chit_id = ? AND user_id = ?");
            $stmt->execute([$this->id, $userId]);
            $count = (int)$stmt->fetchColumn();

            return $count > 0;

        } catch (Exception $e) {
            error_log("Check user access error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get chits for a specific user (as organizer or member)
     */
    public function getUserChits($userId, $page = 1, $limit = 20, $search = '', $status = null) {
        try {
            $offset = ($page - 1) * $limit;

            $whereConditions = [];
            $params = [$userId, $userId];

            if (!empty($search)) {
                $whereConditions[] = "c.name LIKE ?";
                $params[] = "%$search%";
            }

            if ($status) {
                $whereConditions[] = "c.status = ?";
                $params[] = $status;
            }

            $whereClause = !empty($whereConditions) ? 'AND ' . implode(' AND ', $whereConditions) : '';

            // Get total count
            $countSql = "SELECT COUNT(DISTINCT c.id) FROM chits c
                        LEFT JOIN chit_members cm ON c.id = cm.chit_id
                        WHERE (c.organizer_id = ? OR cm.user_id = ?) $whereClause";
            $stmt = $this->db->prepare($countSql);
            $stmt->execute($params);
            $total = (int)$stmt->fetchColumn();

            // Get chits with organizer info
            $sql = "SELECT DISTINCT c.*, u.name as organizer_name,
                           COUNT(DISTINCT cm.id) as member_count,
                           COUNT(DISTINCT CASE WHEN cm.status = 'active' THEN cm.id END) as active_members
                    FROM chits c
                    LEFT JOIN users u ON c.organizer_id = u.id
                    LEFT JOIN chit_members cm ON c.id = cm.chit_id
                    LEFT JOIN chit_members cm2 ON c.id = cm2.chit_id
                    WHERE (c.organizer_id = ? OR cm2.user_id = ?) $whereClause
                    GROUP BY c.id
                    ORDER BY c.created_at DESC
                    LIMIT ? OFFSET ?";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $chits = $stmt->fetchAll();

            return [
                'chits' => $chits,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ];

        } catch (Exception $e) {
            error_log("Get user chits error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get detailed chit information
     */
    public function getDetailedInfo() {
        try {
            $sql = "SELECT c.*, u.name as organizer_name, u.email as organizer_email,
                           COUNT(cm.id) as member_count,
                           COUNT(CASE WHEN cm.status = 'active' THEN 1 END) as active_members,
                           COALESCE(SUM(p.amount), 0) as total_collected
                    FROM chits c
                    LEFT JOIN users u ON c.organizer_id = u.id
                    LEFT JOIN chit_members cm ON c.id = cm.chit_id
                    LEFT JOIN payments p ON cm.id = p.member_id AND p.status = 'paid'
                    WHERE c.id = ?
                    GROUP BY c.id";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetch();

        } catch (Exception $e) {
            error_log("Get detailed chit info error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get chit members
     */
    public function getMembers() {
        try {
            $sql = "SELECT cm.*, u.name, u.email, u.phone
                    FROM chit_members cm
                    JOIN users u ON cm.user_id = u.id
                    WHERE cm.chit_id = ?
                    ORDER BY cm.member_number ASC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Get chit members error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get chit payments
     */
    public function getPayments() {
        try {
            $sql = "SELECT p.*, u.name as member_name, cm.member_number
                    FROM payments p
                    JOIN chit_members cm ON p.member_id = cm.id
                    JOIN users u ON cm.user_id = u.id
                    WHERE cm.chit_id = ?
                    ORDER BY p.due_date DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Get chit payments error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get chit bidding rounds
     */
    public function getBiddingRounds() {
        try {
            $sql = "SELECT br.*, u.name as winner_name
                    FROM bidding_rounds br
                    LEFT JOIN chit_members cm ON br.winner_id = cm.id
                    LEFT JOIN users u ON cm.user_id = u.id
                    WHERE br.chit_id = ?
                    ORDER BY br.round_number ASC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Get chit bidding rounds error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get chit summary
     */
    public function getSummary() {
        try {
            // Get basic chit info
            $chitInfo = $this->getDetailedInfo();

            // Get payment statistics
            $sql = "SELECT
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN p.status = 'paid' THEN 1 END) as paid_payments,
                        COUNT(CASE WHEN p.status = 'pending' THEN 1 END) as pending_payments,
                        COUNT(CASE WHEN p.status = 'overdue' THEN 1 END) as overdue_payments,
                        COALESCE(SUM(CASE WHEN p.status = 'paid' THEN p.amount END), 0) as total_collected,
                        COALESCE(SUM(CASE WHEN p.status IN ('pending', 'overdue') THEN p.amount END), 0) as total_pending
                    FROM payments p
                    JOIN chit_members cm ON p.member_id = cm.id
                    WHERE cm.chit_id = ?";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);
            $paymentStats = $stmt->fetch();

            // Get bidding statistics
            $sql = "SELECT
                        COUNT(*) as total_rounds,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_rounds,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_rounds
                    FROM bidding_rounds
                    WHERE chit_id = ?";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);
            $biddingStats = $stmt->fetch();

            // Calculate progress
            $progress = 0;
            if ($this->duration_months > 0) {
                $progress = round(($biddingStats['completed_rounds'] / $this->duration_months) * 100, 2);
            }

            return [
                'chit_info' => $chitInfo,
                'payment_stats' => $paymentStats,
                'bidding_stats' => $biddingStats,
                'progress' => [
                    'percentage' => $progress,
                    'completed_rounds' => $biddingStats['completed_rounds'],
                    'total_rounds' => $this->duration_months
                ]
            ];

        } catch (Exception $e) {
            error_log("Get chit summary error: " . $e->getMessage());
            return [];
        }
    }
}
?>
