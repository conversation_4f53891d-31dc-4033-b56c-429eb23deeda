<?php
/**
 * Fix Imports Script
 * 
 * Adds missing imports to controller files
 */

echo "🔧 FIXING CONTROLLER IMPORTS\n";
echo "============================\n\n";

$controllers = [
    'MemberController.php' => [
        'models' => ['ChitMember.php', 'User.php', 'Chit.php'],
        'utils' => ['helpers.php']
    ],
    'PaymentController.php' => [
        'models' => ['Payment.php', 'ChitMember.php', 'User.php'],
        'utils' => ['helpers.php']
    ],
    'BiddingController.php' => [
        'models' => ['BiddingRound.php', 'Bid.php', 'ChitMember.php'],
        'utils' => ['helpers.php']
    ],
    'ReportController.php' => [
        'models' => ['User.php', 'Chit.php', 'Payment.php'],
        'utils' => ['helpers.php']
    ],
    'NotificationController.php' => [
        'models' => ['Notification.php', 'User.php'],
        'utils' => ['helpers.php']
    ]
];

foreach ($controllers as $controller => $imports) {
    $filePath = __DIR__ . "/../controllers/$controller";
    
    if (!file_exists($filePath)) {
        echo "⚠️  $controller not found\n";
        continue;
    }
    
    echo "Fixing: $controller\n";
    
    $content = file_get_contents($filePath);
    
    // Check if imports already exist
    if (strpos($content, 'require_once __DIR__') !== false) {
        echo "  ✅ Imports already exist\n";
        continue;
    }
    
    // Find the class declaration
    if (preg_match('/^class\s+\w+\s*\{/m', $content, $matches, PREG_OFFSET_CAPTURE)) {
        $classPos = $matches[0][1];
        
        // Build import statements
        $importStatements = "\n// Import required dependencies\n";
        $importStatements .= "require_once __DIR__ . '/../config/database.php';\n";
        
        foreach ($imports['utils'] as $util) {
            $importStatements .= "require_once __DIR__ . '/../utils/$util';\n";
        }
        
        foreach ($imports['models'] as $model) {
            $importStatements .= "require_once __DIR__ . '/../models/$model';\n";
        }
        
        $importStatements .= "\n";
        
        // Insert imports before class declaration
        $newContent = substr($content, 0, $classPos) . $importStatements . substr($content, $classPos);
        
        if (file_put_contents($filePath, $newContent)) {
            echo "  ✅ Imports added successfully\n";
        } else {
            echo "  ❌ Failed to write file\n";
        }
    } else {
        echo "  ⚠️  Could not find class declaration\n";
    }
    
    echo "\n";
}

echo "🎉 IMPORT FIXING COMPLETE!\n";

?>
