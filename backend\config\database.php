<?php
/**
 * Unified Database System - Smart LiteSpeed Detection
 *
 * Single database class that automatically optimizes for LiteSpeed hosting
 * while providing fallback for standard hosting environments
 */

// Import configuration constants
if (file_exists(__DIR__ . '/env.php')) {
    require_once __DIR__ . '/env.php';
} else {
    // Define default constants if no config file found
    if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
    if (!defined('DB_NAME')) define('DB_NAME', 'chit_fund_db');
    if (!defined('DB_USER')) define('DB_USER', 'root');
    if (!defined('DB_PASS')) define('DB_PASS', '');
    if (!defined('DB_CHARSET')) define('DB_CHARSET', 'utf8mb4');
    if (!defined('DB_PERSISTENT')) define('DB_PERSISTENT', true);
    if (!defined('DB_TIMEOUT')) define('DB_TIMEOUT', 10);
    if (!defined('DB_POOL_SIZE')) define('DB_POOL_SIZE', 5);
    if (!defined('CACHE_ENABLED')) define('CACHE_ENABLED', true);
    if (!defined('CACHE_TTL')) define('CACHE_TTL', 1800);
    if (!defined('CACHE_PREFIX')) define('CACHE_PREFIX', 'cache_');
}

/**
 * Unified Smart Database Class
 *
 * Automatically detects hosting environment and applies appropriate optimizations:
 * - LiteSpeed hosting: Connection pooling, OPcache, advanced caching
 * - Standard hosting: Basic PDO with optimizations
 */
class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $charset;
    private $conn = null;
    private $isLiteSpeed = false;
    private $connectionPool = [];
    private $cache = [];
    private $queryCache = [];

    // LiteSpeed optimization settings
    private $maxConnections;
    private $connectionTimeout;
    private $cacheEnabled;
    private $cacheTTL;
    private $cachePrefix;
    
    public function __construct() {
        $this->host = DB_HOST;
        $this->db_name = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;

        // Detect LiteSpeed environment
        $this->isLiteSpeed = $this->detectLiteSpeedEnvironment();

        // Initialize LiteSpeed optimizations if available
        if ($this->isLiteSpeed) {
            $this->maxConnections = defined('DB_POOL_SIZE') ? DB_POOL_SIZE : 5;
            $this->connectionTimeout = defined('DB_TIMEOUT') ? DB_TIMEOUT : 10;
            $this->cacheEnabled = defined('CACHE_ENABLED') ? CACHE_ENABLED : true;
            $this->cacheTTL = defined('CACHE_TTL') ? CACHE_TTL : 1800;
            $this->cachePrefix = defined('CACHE_PREFIX') ? CACHE_PREFIX : 'cache_';

            // Initialize connection pool
            $this->initializeConnectionPool();
        }
    }
    
    /**
     * Detect LiteSpeed hosting environment
     *
     * @return bool
     */
    private function detectLiteSpeedEnvironment() {
        // Check for LiteSpeed server signature
        $serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? '';
        if (stripos($serverSoftware, 'litespeed') !== false) {
            return true;
        }

        // Check for LiteSpeed-specific environment variables
        if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) ||
            isset($_SERVER['LSWS_EDITION']) ||
            function_exists('litespeed_request_headers')) {
            return true;
        }

        // Check if LiteSpeed cache is available
        if (function_exists('litespeed_purge_all') ||
            defined('LITESPEED_CACHE_ENABLED')) {
            return true;
        }

        return false;
    }

    /**
     * Initialize connection pool for LiteSpeed
     */
    private function initializeConnectionPool() {
        if (!$this->isLiteSpeed) {
            return;
        }

        // Pre-create connections for pool
        for ($i = 0; $i < min($this->maxConnections, 3); $i++) {
            $conn = $this->createConnection();
            if ($conn) {
                $this->connectionPool[] = [
                    'connection' => $conn,
                    'in_use' => false,
                    'created_at' => time()
                ];
            }
        }
    }

    /**
     * Create a new database connection
     *
     * @return PDO|null
     */
    private function createConnection() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => $this->connectionTimeout,
            ];

            // Add LiteSpeed-specific optimizations
            if ($this->isLiteSpeed) {
                $options[PDO::ATTR_PERSISTENT] = defined('DB_PERSISTENT') ? DB_PERSISTENT : true;
                $options[PDO::MYSQL_ATTR_USE_BUFFERED_QUERY] = true;
                $options[PDO::MYSQL_ATTR_INIT_COMMAND] = "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'";
            }

            return new PDO($dsn, $this->username, $this->password, $options);

        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get database connection (smart connection management)
     *
     * @return PDO|null Database connection or null on failure
     */
    public function getConnection() {
        if ($this->isLiteSpeed && !empty($this->connectionPool)) {
            // Use connection pool for LiteSpeed
            return $this->getPooledConnection();
        }

        // Standard connection for non-LiteSpeed environments
        if ($this->conn === null) {
            $this->conn = $this->createConnection();
        }

        return $this->conn;
    }

    /**
     * Get connection from pool (LiteSpeed optimization)
     *
     * @return PDO|null
     */
    private function getPooledConnection() {
        // Find available connection in pool
        foreach ($this->connectionPool as &$poolItem) {
            if (!$poolItem['in_use']) {
                // Check if connection is still valid
                try {
                    $poolItem['connection']->query('SELECT 1');
                    $poolItem['in_use'] = true;
                    return $poolItem['connection'];
                } catch (PDOException $e) {
                    // Connection is stale, create new one
                    $poolItem['connection'] = $this->createConnection();
                    if ($poolItem['connection']) {
                        $poolItem['in_use'] = true;
                        return $poolItem['connection'];
                    }
                }
            }
        }

        // No available connections, create new one if under limit
        if (count($this->connectionPool) < $this->maxConnections) {
            $conn = $this->createConnection();
            if ($conn) {
                $this->connectionPool[] = [
                    'connection' => $conn,
                    'in_use' => true,
                    'created_at' => time()
                ];
                return $conn;
            }
        }

        // Fallback to creating a temporary connection
        return $this->createConnection();
    }
    
    /**
     * Execute a prepared statement
     * 
     * @param string $sql
     * @param array $params
     * @return array|false
     */
    public function executeQuery($sql, $params = []) {
        try {
            $conn = $this->getConnection();
            if (!$conn) {
                return false;
            }
            
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            error_log("Query execution failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute a non-select query (INSERT, UPDATE, DELETE)
     * 
     * @param string $sql
     * @param array $params
     * @return bool|int
     */
    public function executeNonQuery($sql, $params = []) {
        try {
            $conn = $this->getConnection();
            if (!$conn) {
                return false;
            }
            
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute($params);
            
            // Return affected rows for UPDATE/DELETE, last insert ID for INSERT
            if (stripos($sql, 'INSERT') === 0) {
                return $conn->lastInsertId();
            } else {
                return $stmt->rowCount();
            }
            
        } catch (PDOException $e) {
            error_log("Non-query execution failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Begin transaction
     * 
     * @return bool
     */
    public function beginTransaction() {
        $conn = $this->getConnection();
        return $conn ? $conn->beginTransaction() : false;
    }
    
    /**
     * Commit transaction
     * 
     * @return bool
     */
    public function commit() {
        return $this->conn ? $this->conn->commit() : false;
    }
    
    /**
     * Rollback transaction
     * 
     * @return bool
     */
    public function rollback() {
        return $this->conn ? $this->conn->rollback() : false;
    }
    
    /**
     * Close connection
     */
    public function closeConnection() {
        $this->conn = null;
    }
}

/**
 * Database Factory Function
 * 
 * Returns the best available database class for the environment
 * 
 * @return Database|LiteSpeedDatabase
 */
function getDatabaseInstance() {
    // Use LiteSpeed optimized database if available
    if (class_exists('LiteSpeedDatabase')) {
        return new LiteSpeedDatabase();
    }
    
    // Fallback to standard database
    return new Database();
}

/**
 * Get database connection using the best available class
 * 
 * @return PDO|null
 */
function getDBConnection() {
    $db = getDatabaseInstance();
    return $db->getConnection();
}

/**
 * Test database connectivity
 * 
 * @return bool
 */
function testDatabaseConnection() {
    try {
        $db = getDatabaseInstance();
        $conn = $db->getConnection();
        
        if (!$conn) {
            return false;
        }
        
        // Test with a simple query
        $stmt = $conn->query('SELECT 1');
        return $stmt !== false;
        
    } catch (Exception $e) {
        error_log("Database connection test failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get database configuration info
 * 
 * @return array
 */
function getDatabaseConfig() {
    return [
        'host' => DB_HOST,
        'database' => DB_NAME,
        'username' => DB_USER,
        'charset' => DB_CHARSET,
        'litespeed_available' => class_exists('LiteSpeedDatabase'),
        'connection_test' => testDatabaseConnection()
    ];
}

?>
