import 'package:flutter/foundation.dart';
import '../models/activity_log.dart';
import '../services/activity_service.dart';

class ActivityProvider extends ChangeNotifier {
  List<ActivityLog> _recentActivities = [];
  List<ActivityLog> _userActivities = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<ActivityLog> get recentActivities => _recentActivities;
  List<ActivityLog> get userActivities => _userActivities;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Get recent activities (for dashboard)
  Future<void> loadRecentActivities({int limit = 5}) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await ActivityService.getRecentActivities(limit: limit);

      if (result.isSuccess) {
        _recentActivities = result.data!;
      } else {
        _setError(result.error ?? 'Failed to load recent activities');
      }
    } catch (e) {
      _setError('Failed to load recent activities: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get user activities (for activity log screen)
  Future<void> loadUserActivities({
    String? userId,
    int limit = 20,
    int offset = 0,
    bool append = false,
  }) async {
    try {
      if (!append) {
        _setLoading(true);
        _clearError();
      }

      final result = await ActivityService.getUserActivities(
        userId: userId,
        limit: limit,
        offset: offset,
      );

      if (result.isSuccess) {
        if (append) {
          _userActivities.addAll(result.data!);
        } else {
          _userActivities = result.data!;
        }
      } else {
        _setError(result.error ?? 'Failed to load user activities');
      }
    } catch (e) {
      _setError('Failed to load user activities: $e');
    } finally {
      if (!append) {
        _setLoading(false);
      }
    }
  }

  // Get chit activities
  Future<List<ActivityLog>> getChitActivities({
    required String chitId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final result = await ActivityService.getChitActivities(
        chitId: chitId,
        limit: limit,
        offset: offset,
      );

      if (result.isSuccess) {
        return result.data!;
      } else {
        throw Exception(result.error ?? 'Failed to load chit activities');
      }
    } catch (e) {
      throw Exception('Failed to load chit activities: $e');
    }
  }

  // Log activity
  Future<void> logActivity({
    required String action,
    Map<String, dynamic>? details,
    String? chitId,
  }) async {
    try {
      await ActivityService.logActivity(
        action: action,
        details: details,
        chitId: chitId,
      );
      
      // Refresh recent activities after logging
      await loadRecentActivities();
    } catch (e) {
      // Log activity failures shouldn't be shown to user
      // but we can handle them silently
    }
  }

  // Refresh all activities
  Future<void> refreshActivities() async {
    await Future.wait([
      loadRecentActivities(),
      loadUserActivities(),
    ]);
  }

  // Get activities by type
  List<ActivityLog> getActivitiesByType(String type) {
    return _userActivities.where((activity) => activity.action == type).toList();
  }

  // Get recent activities by severity
  List<ActivityLog> getRecentActivitiesBySeverity(ActivitySeverity severity) {
    return _recentActivities.where((activity) => activity.severity == severity).toList();
  }

  // Get today's activities
  List<ActivityLog> getTodaysActivities() {
    final today = DateTime.now();
    return _userActivities.where((activity) {
      return activity.createdAt.year == today.year &&
             activity.createdAt.month == today.month &&
             activity.createdAt.day == today.day;
    }).toList();
  }

  // Clear activities
  void clearActivities() {
    _recentActivities.clear();
    _userActivities.clear();
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
