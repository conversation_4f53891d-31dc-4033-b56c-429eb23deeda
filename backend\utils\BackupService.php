<?php
/**
 * Backup Service - Handles database and file backups
 */

require_once __DIR__ . '/../config/env.php';
require_once __DIR__ . '/../config/database.php';

class BackupService {
    
    /**
     * Create database backup
     */
    public static function createDatabaseBackup($userId = null) {
        if (!BACKUP_ENABLED) {
            return ['success' => false, 'message' => 'Backup is disabled'];
        }
        
        try {
            // Create backup directory if it doesn't exist
            if (!is_dir(BACKUP_PATH)) {
                mkdir(BACKUP_PATH, 0755, true);
            }
            
            $timestamp = date('Y-m-d_H-i-s');
            $backupFile = BACKUP_PATH . "database_backup_$timestamp.sql";
            
            // Create mysqldump command
            $command = sprintf(
                'mysqldump -h %s -u %s -p%s %s > %s',
                DB_HOST,
                DB_USER,
                DB_PASS,
                DB_NAME,
                $backupFile
            );
            
            // Execute backup
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0 && file_exists($backupFile)) {
                // Log backup creation
                self::logBackup($userId, 'database', $backupFile, filesize($backupFile));
                
                // Clean old backups
                self::cleanOldBackups();
                
                return [
                    'success' => true,
                    'message' => 'Database backup created successfully',
                    'file' => basename($backupFile),
                    'size' => filesize($backupFile)
                ];
            } else {
                return ['success' => false, 'message' => 'Failed to create database backup'];
            }
            
        } catch (Exception $e) {
            error_log("Database backup error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Backup failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Create files backup
     */
    public static function createFilesBackup($userId = null) {
        if (!BACKUP_ENABLED) {
            return ['success' => false, 'message' => 'Backup is disabled'];
        }
        
        try {
            // Create backup directory if it doesn't exist
            if (!is_dir(BACKUP_PATH)) {
                mkdir(BACKUP_PATH, 0755, true);
            }
            
            $timestamp = date('Y-m-d_H-i-s');
            $backupFile = BACKUP_PATH . "files_backup_$timestamp.tar.gz";
            
            // Create tar command for uploads directory
            $uploadsPath = UPLOAD_PATH;
            $command = sprintf(
                'tar -czf %s -C %s .',
                $backupFile,
                $uploadsPath
            );
            
            // Execute backup
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0 && file_exists($backupFile)) {
                // Log backup creation
                self::logBackup($userId, 'files', $backupFile, filesize($backupFile));
                
                return [
                    'success' => true,
                    'message' => 'Files backup created successfully',
                    'file' => basename($backupFile),
                    'size' => filesize($backupFile)
                ];
            } else {
                return ['success' => false, 'message' => 'Failed to create files backup'];
            }
            
        } catch (Exception $e) {
            error_log("Files backup error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Backup failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Create complete backup (database + files)
     */
    public static function createCompleteBackup($userId = null) {
        $results = [];
        
        // Create database backup
        $dbResult = self::createDatabaseBackup($userId);
        $results['database'] = $dbResult;
        
        // Create files backup
        $filesResult = self::createFilesBackup($userId);
        $results['files'] = $filesResult;
        
        $success = $dbResult['success'] && $filesResult['success'];
        
        return [
            'success' => $success,
            'message' => $success ? 'Complete backup created successfully' : 'Backup partially failed',
            'results' => $results
        ];
    }
    
    /**
     * List available backups
     */
    public static function listBackups() {
        if (!BACKUP_ENABLED || !is_dir(BACKUP_PATH)) {
            return [];
        }
        
        $backups = [];
        $files = glob(BACKUP_PATH . '*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $backups[] = [
                    'name' => basename($file),
                    'size' => filesize($file),
                    'created_at' => date('Y-m-d H:i:s', filemtime($file)),
                    'type' => self::getBackupType($file)
                ];
            }
        }
        
        // Sort by creation time (newest first)
        usort($backups, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return $backups;
    }
    
    /**
     * Download backup file
     */
    public static function downloadBackup($filename) {
        $filePath = BACKUP_PATH . $filename;
        
        if (!file_exists($filePath)) {
            return ['success' => false, 'message' => 'Backup file not found'];
        }
        
        // Security check - ensure filename doesn't contain path traversal
        if (strpos($filename, '..') !== false || strpos($filename, '/') !== false) {
            return ['success' => false, 'message' => 'Invalid filename'];
        }
        
        return [
            'success' => true,
            'file_path' => $filePath,
            'file_size' => filesize($filePath),
            'mime_type' => self::getBackupMimeType($filename)
        ];
    }
    
    /**
     * Delete backup file
     */
    public static function deleteBackup($filename, $userId = null) {
        $filePath = BACKUP_PATH . $filename;
        
        if (!file_exists($filePath)) {
            return ['success' => false, 'message' => 'Backup file not found'];
        }
        
        // Security check
        if (strpos($filename, '..') !== false || strpos($filename, '/') !== false) {
            return ['success' => false, 'message' => 'Invalid filename'];
        }
        
        if (unlink($filePath)) {
            // Log backup deletion
            self::logBackup($userId, 'delete', $filename, 0);
            
            return ['success' => true, 'message' => 'Backup deleted successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to delete backup'];
        }
    }
    
    /**
     * Clean old backups based on retention policy
     */
    private static function cleanOldBackups() {
        if (!is_dir(BACKUP_PATH)) {
            return;
        }
        
        $retentionDays = BACKUP_RETENTION_DAYS;
        $cutoffTime = time() - ($retentionDays * 24 * 60 * 60);
        
        $files = glob(BACKUP_PATH . '*');
        $deletedCount = 0;
        
        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $deletedCount++;
                    error_log("Deleted old backup: " . basename($file));
                }
            }
        }
        
        if ($deletedCount > 0) {
            error_log("Cleaned up $deletedCount old backup files");
        }
    }
    
    /**
     * Get backup type from filename
     */
    private static function getBackupType($filename) {
        $basename = basename($filename);
        
        if (strpos($basename, 'database_') === 0) {
            return 'database';
        } elseif (strpos($basename, 'files_') === 0) {
            return 'files';
        } else {
            return 'unknown';
        }
    }
    
    /**
     * Get MIME type for backup file
     */
    private static function getBackupMimeType($filename) {
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        
        switch ($extension) {
            case 'sql':
                return 'application/sql';
            case 'gz':
            case 'tar.gz':
                return 'application/gzip';
            case 'zip':
                return 'application/zip';
            default:
                return 'application/octet-stream';
        }
    }
    
    /**
     * Log backup operation
     */
    private static function logBackup($userId, $type, $filename, $fileSize) {
        try {
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $sql = "INSERT INTO backup_logs (user_id, backup_type, filename, file_size, created_at) VALUES (?, ?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$userId, $type, basename($filename), $fileSize]);
            
        } catch (Exception $e) {
            error_log("Backup log error: " . $e->getMessage());
        }
    }
    
    /**
     * Get backup statistics
     */
    public static function getBackupStats() {
        try {
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $sql = "SELECT 
                        backup_type,
                        COUNT(*) as count,
                        SUM(file_size) as total_size,
                        MAX(created_at) as last_backup
                    FROM backup_logs 
                    WHERE created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
                    GROUP BY backup_type";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Backup stats error: " . $e->getMessage());
            return [];
        }
    }
}
