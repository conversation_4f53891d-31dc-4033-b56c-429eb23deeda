<?php
/**
 * Notification Controller
 *
 * Handles notification operations
 */

// Import required dependencies
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../utils/JWT.php';
require_once __DIR__ . '/../models/Notification.php';
require_once __DIR__ . '/../models/User.php';

class NotificationController {
    
    /**
     * Get notifications for authenticated user
     */
    public function getUserNotifications() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $notification = new Notification();
            $notifications = $notification->getUserNotifications($userId);
            
            sendSuccess($notifications, 'Notifications retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get user notifications error: " . $e->getMessage());
            sendError('Failed to retrieve notifications', 500);
        }
    }
    
    /**
     * Get unread notifications for authenticated user
     */
    public function getUnreadNotifications() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $notification = new Notification();
            $notifications = $notification->getUnreadNotifications($userId);
            
            sendSuccess($notifications, 'Unread notifications retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get unread notifications error: " . $e->getMessage());
            sendError('Failed to retrieve unread notifications', 500);
        }
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['notification_id'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        try {
            $notification = new Notification();
            
            if (!$notification->findById($data['notification_id'])) {
                sendError('Notification not found', 404);
            }
            
            // Check if user owns this notification
            if ($notification->user_id != $userId) {
                sendError('Access denied', 403);
            }
            
            if ($notification->markAsRead()) {
                // Log activity
                logActivity($userId, 'notification_read', [
                    'notification_id' => $notification->id
                ]);
                
                sendSuccess(null, 'Notification marked as read');
            } else {
                sendError('Failed to mark notification as read', 500);
            }
            
        } catch (Exception $e) {
            error_log("Mark notification as read error: " . $e->getMessage());
            sendError('Failed to mark notification as read', 500);
        }
    }
    
    /**
     * Mark all notifications as read
     */
    public function markAllAsRead() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $notification = new Notification();
            $count = $notification->markAllAsRead($userId);
            
            // Log activity
            logActivity($userId, 'all_notifications_read', [
                'count' => $count
            ]);
            
            sendSuccess(['count' => $count], 'All notifications marked as read');
            
        } catch (Exception $e) {
            error_log("Mark all notifications as read error: " . $e->getMessage());
            sendError('Failed to mark all notifications as read', 500);
        }
    }
    
    /**
     * Delete notification
     */
    public function deleteNotification($notificationId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$notificationId) {
            sendError('Notification ID is required', 400);
        }
        
        try {
            $notification = new Notification();
            
            if (!$notification->findById($notificationId)) {
                sendError('Notification not found', 404);
            }
            
            // Check if user owns this notification
            if ($notification->user_id != $userId) {
                sendError('Access denied', 403);
            }
            
            if ($notification->delete()) {
                // Log activity
                logActivity($userId, 'notification_deleted', [
                    'notification_id' => $notificationId
                ]);
                
                sendSuccess(null, 'Notification deleted successfully');
            } else {
                sendError('Failed to delete notification', 500);
            }
            
        } catch (Exception $e) {
            error_log("Delete notification error: " . $e->getMessage());
            sendError('Failed to delete notification', 500);
        }
    }
    
    /**
     * Create notification (system use)
     */
    public function createNotification() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['user_id', 'title', 'message', 'type'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        try {
            // Check if current user has permission to create notifications
            $user = new User();
            $user->findById($userId);
            
            if ($user->role !== 'admin' && $user->role !== 'organizer') {
                sendError('Insufficient permissions', 403);
            }
            
            $notification = new Notification();
            $notification->user_id = $data['user_id'];
            $notification->chit_id = $data['chit_id'] ?? null;
            $notification->title = sanitizeInput($data['title']);
            $notification->message = sanitizeInput($data['message']);
            $notification->type = $data['type'];
            $notification->scheduled_at = $data['scheduled_at'] ?? null;
            
            if ($notification->create()) {
                // Log activity
                logActivity($userId, 'notification_created', [
                    'notification_id' => $notification->id,
                    'target_user_id' => $data['user_id'],
                    'type' => $data['type']
                ]);
                
                sendSuccess($notification->getDetails(), 'Notification created successfully');
            } else {
                sendError('Failed to create notification', 500);
            }
            
        } catch (Exception $e) {
            error_log("Create notification error: " . $e->getMessage());
            sendError('Failed to create notification', 500);
        }
    }
    
    /**
     * Get notification statistics
     */
    public function getNotificationStats() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $notification = new Notification();
            $stats = $notification->getUserNotificationStats($userId);
            
            sendSuccess($stats, 'Notification statistics retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get notification stats error: " . $e->getMessage());
            sendError('Failed to retrieve notification statistics', 500);
        }
    }
    
    /**
     * Send bulk notifications (admin/organizer only)
     */
    public function sendBulkNotifications() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['user_ids', 'title', 'message', 'type'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        try {
            // Check permissions
            $user = new User();
            $user->findById($userId);
            
            if ($user->role !== 'admin' && $user->role !== 'organizer') {
                sendError('Insufficient permissions', 403);
            }
            
            $notification = new Notification();
            $count = 0;
            
            foreach ($data['user_ids'] as $targetUserId) {
                $notification = new Notification();
                $notification->user_id = $targetUserId;
                $notification->chit_id = $data['chit_id'] ?? null;
                $notification->title = sanitizeInput($data['title']);
                $notification->message = sanitizeInput($data['message']);
                $notification->type = $data['type'];
                $notification->scheduled_at = $data['scheduled_at'] ?? null;
                
                if ($notification->create()) {
                    $count++;
                }
            }
            
            // Log activity
            logActivity($userId, 'bulk_notifications_sent', [
                'count' => $count,
                'type' => $data['type']
            ]);
            
            sendSuccess(['count' => $count], "Sent $count notifications successfully");
            
        } catch (Exception $e) {
            error_log("Send bulk notifications error: " . $e->getMessage());
            sendError('Failed to send bulk notifications', 500);
        }
    }
}
?>
