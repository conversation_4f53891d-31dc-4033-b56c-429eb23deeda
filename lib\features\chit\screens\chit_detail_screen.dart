import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/models/chit_model.dart';
import '../../../core/services/chit_service.dart';
import '../../../core/router/app_router.dart';

class ChitDetailScreen extends StatefulWidget {
  final String chitId;

  const ChitDetailScreen({super.key, required this.chitId});

  @override
  State<ChitDetailScreen> createState() => _ChitDetailScreenState();
}

class _ChitDetailScreenState extends State<ChitDetailScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  Chit? _chit;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadChitDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadChitDetails() async {
    setState(() {
      _isLoading = true;
    });

    final chitProvider = Provider.of<ChitProvider>(context, listen: false);

    // First try to get from local cache
    _chit = chitProvider.chits.where((chit) => chit.id == widget.chitId).firstOrNull;

    if (_chit != null) {
      setState(() {
        _isLoading = false;
      });
    }

    // Then fetch latest details from server
    await chitProvider.getChitDetails(widget.chitId);
    _chit = chitProvider.chits.where((chit) => chit.id == widget.chitId).firstOrNull;

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _handleRefresh() async {
    await _loadChitDetails();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _chit == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Chit Details'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_chit == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Chit Details'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Chit not found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'The requested chit could not be loaded.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => AppNavigation.goBack(context),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            _buildSliverAppBar(),
          ];
        },
        body: RefreshIndicator(
          onRefresh: _handleRefresh,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildMembersTab(),
              _buildBiddingTab(),
              _buildPaymentsTab(),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          _chit!.name,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 60), // Space for app bar
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _chit!.status.displayName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '₹${_formatAmount(_chit!.totalAmount)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Members'),
          Tab(text: 'Bidding'),
          Tab(text: 'Payments'),
        ],
      ),
      actions: [
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _editChit();
                break;
              case 'share':
                _shareChit();
                break;
              case 'delete':
                _deleteChit();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('Share'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildChitInfoCard(),
          const SizedBox(height: 16),
          _buildPaymentSummaryCard(),
          const SizedBox(height: 16),
          if (_chit!.status == ChitStatus.active)
            _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildMembersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildMemberListCard(),
        ],
      ),
    );
  }

  Widget _buildBiddingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildBiddingHistoryCard(),
        ],
      ),
    );
  }

  Widget _buildPaymentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildPaymentSummaryCard(),
          // Add more payment-related widgets here
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                if (_chit!.status == ChitStatus.draft) ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _activateChit,
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('Activate Chit'),
                    ),
                  ),
                ] else ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _startBiddingRound,
                      icon: const Icon(Icons.gavel),
                      label: const Text('Start Bidding'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        AppNavigation.goToPayments(context);
                      },
                      icon: const Icon(Icons.payment),
                      label: const Text('View Payments'),
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // Add member
                    },
                    icon: const Icon(Icons.person_add),
                    label: const Text('Add Member'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // Send reminder
                    },
                    icon: const Icon(Icons.notifications),
                    label: const Text('Send Reminder'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    if (_chit!.status != ChitStatus.active) return null;

    return FloatingActionButton.extended(
      onPressed: () {
        // Navigate to appropriate action based on chit state
        if (_chit!.currentRound < _chit!.numberOfMembers) {
          // Start next bidding round
          _startBiddingRound();
        } else {
          // Complete chit
          _completeChit();
        }
      },
      icon: Icon(_chit!.currentRound < _chit!.numberOfMembers
          ? Icons.gavel
          : Icons.check_circle),
      label: Text(_chit!.currentRound < _chit!.numberOfMembers
          ? 'Start Bidding'
          : 'Complete Chit'),
    );
  }

  Color _getStatusColor() {
    switch (_chit!.status) {
      case ChitStatus.active:
        return Colors.green;
      case ChitStatus.completed:
        return Colors.blue;
      case ChitStatus.draft:
        return Colors.orange;
      case ChitStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '${(amount / 10000000).toStringAsFixed(1)}Cr';
    } else if (amount >= 100000) {
      return '${(amount / 100000).toStringAsFixed(1)}L';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }

  void _editChit() {
    if (_chit == null) return;

    Navigator.of(context).pushNamed(
      '/chit/edit',
      arguments: _chit!.id,
    ).then((result) {
      if (result == true) {
        // Refresh chit details after edit
        _loadChitDetails();
      }
    });
  }

  void _shareChit() {
    if (_chit == null) return;

    final shareText = '''
🏦 Chit Fund Details
📝 Name: ${_chit!.name}
💰 Total Amount: ₹${_formatAmount(_chit!.totalAmount)}
👥 Members: ${_chit!.numberOfMembers}
📅 Start Date: ${DateFormat('dd MMM yyyy').format(_chit!.startDate)}
🔄 Frequency: ${_chit!.frequency.name.toUpperCase()}
📊 Status: ${_chit!.status.name.toUpperCase()}

Join our chit fund for secure savings and investment!
    ''';

    // Use share_plus package for sharing
    // For now, copy to clipboard
    Clipboard.setData(ClipboardData(text: shareText));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Chit details copied to clipboard!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _deleteChit() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Chit'),
        content: const Text('Are you sure you want to delete this chit? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performDelete();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _startBiddingRound() {
    if (_chit == null) return;

    Navigator.of(context).pushNamed(
      '/bidding/start',
      arguments: _chit!.id,
    ).then((result) {
      if (result == true) {
        // Refresh chit details after starting bidding
        _loadChitDetails();
      }
    });
  }

  void _completeChit() async {
    if (_chit == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Chit'),
        content: const Text('Are you sure you want to complete this chit? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Complete', style: TextStyle(color: Colors.green)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        if (!mounted) return;
        final chitProvider = Provider.of<ChitProvider>(context, listen: false);
        final result = await chitProvider.updateChitStatus(_chit!.id, ChitStatus.completed);

        if (result.isSuccess) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Chit completed successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            _loadChitDetails(); // Refresh data
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(result.error ?? 'Unknown error'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error completing chit: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _performDelete() async {
    if (_chit == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final chitProvider = Provider.of<ChitProvider>(context, listen: false);
      final result = await chitProvider.deleteChit(_chit!.id);

      if (result.isSuccess) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Chit deleted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true); // Return to previous screen
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'Unknown error'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting chit: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Placeholder widget methods
  Widget _buildChitInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Chit Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Description', _chit!.description),
            _buildInfoRow('Total Amount', '₹${_formatAmount(_chit!.totalAmount)}'),
            _buildInfoRow('Members', '${_chit!.numberOfMembers}'),
            _buildInfoRow('Frequency', _chit!.frequency.displayName),
            _buildInfoRow('Duration', '${_chit!.durationMonths} months'),
            _buildInfoRow('Current Round', '${_chit!.currentRound}/${_chit!.numberOfMembers}'),
            if (_chit!.status == ChitStatus.active)
              LinearProgressIndicator(
                value: _chit!.currentRound / _chit!.numberOfMembers,
                backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSummaryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Summary',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem('Total Collected', '₹${_formatAmount(_chit!.totalAmount * 0.6)}', Colors.green),
                ),
                Expanded(
                  child: _buildSummaryItem('Pending', '₹${_formatAmount(_chit!.totalAmount * 0.4)}', Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberListCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Members (${_chit!.numberOfMembers})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton.icon(
                  onPressed: () {
                    _showAddMemberDialog();
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add Member'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Placeholder member list
            ...List.generate(
              _chit!.numberOfMembers.clamp(0, 5),
              (index) => ListTile(
                leading: CircleAvatar(
                  child: Text('${index + 1}'),
                ),
                title: Text('Member ${index + 1}'),
                subtitle: Text('member${index + 1}@example.com'),
                trailing: index == 0
                    ? const Chip(label: Text('Winner'), backgroundColor: Colors.green)
                    : null,
              ),
            ),
            if (_chit!.numberOfMembers > 5)
              TextButton(
                onPressed: () {
                  // Show all members
                },
                child: Text('View all ${_chit!.numberOfMembers} members'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBiddingHistoryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bidding History',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            if (_chit!.currentRound == 0)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.gavel,
                      size: 48,
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No bidding rounds yet',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _startBiddingRound,
                      child: const Text('Start First Round'),
                    ),
                  ],
                ),
              )
            else
              // Placeholder bidding history
              ...List.generate(
                _chit!.currentRound.clamp(0, 3),
                (index) => Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.green.withValues(alpha: 0.1),
                      child: Text('${index + 1}'),
                    ),
                    title: Text('Round ${index + 1}'),
                    subtitle: Text('Winner: Member ${index + 1}'),
                    trailing: Text('₹${_formatAmount(_chit!.totalAmount * 0.8)}'),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  void _activateChit() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Activate Chit'),
        content: const Text(
          'Are you sure you want to activate this chit? Once activated, the chit will start and members can begin bidding.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Activate'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (!mounted) return;
      final result = await ChitService.activateChit(_chit!.id);

      if (!mounted) return;
      if (result.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chit activated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        _loadChitDetails(); // Refresh data
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to activate chit: ${result.error}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showAddMemberDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Member'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const TextField(
              decoration: InputDecoration(
                labelText: 'Member Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Member added successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
              _loadChitDetails(); // Refresh data
            },
            child: const Text('Add Member'),
          ),
        ],
      ),
    );
  }
}
