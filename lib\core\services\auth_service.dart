import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/user_model.dart';
import 'api_service.dart';
import 'storage_service.dart';

class AuthService {
  static late Dio _dio;

  static void initialize() {
    ApiService.initialize();
    _dio = Dio(BaseOptions(
      baseUrl: ApiService.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token to requests
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
        onError: (error, handler) {
          // Handle token expiry
          if (error.response?.statusCode == 401) {
            _handleTokenExpiry();
          }
          handler.next(error);
        },
      ),
    );
  }

  /// Login user with email and password
  static Future<Result<LoginResponse>> login({
    required String email,
    required String password,
  }) async {
    try {
      final loginData = {
        'email': email,
        'password': password,
      };

      final response = await _dio.post('/auth/login', data: loginData);

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        final success = responseData['success'] as bool? ?? false;
        final message = responseData['message'] as String? ?? 'Unknown response';
        final data = responseData['data'] as Map<String, dynamic>?;

        if (success && data != null) {
          try {
            final loginResponse = LoginResponse.fromJson(data);

            // Store tokens and user data
            await StorageService.saveAuthToken(loginResponse.token);
            await StorageService.saveUserData(loginResponse.user);
            await StorageService.setLoggedIn(true);

            return Success(loginResponse);
          } catch (e) {
            return Error('Failed to parse login response: $e');
          }
        } else {
          return Error(message);
        }
      } else {
        return Error('Login failed with status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e);
      return Error(errorMessage);
    } catch (e) {
      return Error('Network error: Please check your connection and try again');
    }
  }

  /// Send OTP for registration
  static Future<Result<String>> sendRegistrationOtp({
    required String email,
  }) async {
    try {
      final response = await _dio.post('/auth/send-otp', data: {
        'email': email,
        'type': 'registration',
      });

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        final success = responseData['success'] as bool? ?? false;
        final message = responseData['message'] as String? ?? 'Unknown response';

        if (success) {
          return Success(message);
        } else {
          return Error(message);
        }
      } else {
        return Error('Server error: ${response.statusCode}');
      }
    } catch (e) {
      return Error('Network error: $e');
    }
  }

  /// Verify OTP
  static Future<Result<String>> verifyOtp({
    required String email,
    required String otp,
    required String type,
  }) async {
    try {
      final response = await _dio.post('/auth/verify-otp', data: {
        'email': email,
        'otp': otp,
        'type': type,
      });

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        final success = responseData['success'] as bool? ?? false;
        final message = responseData['message'] as String? ?? 'Unknown response';

        if (success) {
          return Success(message);
        } else {
          return Error(message);
        }
      } else {
        return Error('Server error: ${response.statusCode}');
      }
    } catch (e) {
      return Error('Network error: $e');
    }
  }

  /// Register new user with OTP verification
  static Future<Result<LoginResponse>> register({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String otp,
  }) async {
    try {
      final response = await _dio.post('/auth/register', data: {
        'name': name,
        'email': email,
        'phone': phone,
        'password': password,
        'otp': otp,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final loginResponse = LoginResponse.fromJson(apiResponse.data!);
          
          // Store tokens and user data
          await StorageService.saveAuthToken(loginResponse.token);
          await StorageService.saveUserData(loginResponse.user);
          await StorageService.setLoggedIn(true);
          
          return Success(loginResponse);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Registration failed with status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Logout user
  static Future<Result<void>> logout() async {
    try {
      await _dio.post('/auth/logout');
      
      // Clear local storage regardless of API response
      await StorageService.clearUserData();
      
      return const Success(null);
    } on DioException catch (e) {
      // Clear local storage even if API call fails
      await StorageService.clearUserData();
      return Error(_handleDioError(e));
    } catch (e) {
      await StorageService.clearUserData();
      return Error('Unexpected error: $e');
    }
  }

  /// Verify token validity
  static Future<Result<User>> verifyToken() async {
    try {
      final response = await _dio.post('/auth/verify-token');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final user = User.fromJson(apiResponse.data!['user']);
          return Success(user);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Token verification failed');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Refresh access token
  static Future<Result<String>> refreshToken(String refreshToken) async {
    try {
      final response = await _dio.post('/auth/refresh-token', data: {
        'refresh_token': refreshToken,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final newToken = apiResponse.data!['token'] as String;
          await StorageService.saveAuthToken(newToken);
          return Success(newToken);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Token refresh failed');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Request password reset
  static Future<Result<void>> forgotPassword(String email) async {
    try {
      final response = await _dio.post('/auth/forgot-password', data: {
        'email': email,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Password reset request failed');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Reset password with token
  static Future<Result<void>> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      final response = await _dio.post('/auth/reset-password', data: {
        'token': token,
        'password': newPassword,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Password reset failed');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Change password
  static Future<Result<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _dio.post('/users/change-password', data: {
        'current_password': currentPassword,
        'new_password': newPassword,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Password change failed');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle token expiry
  static void _handleTokenExpiry() {
    // Clear stored tokens and redirect to login
    StorageService.clearUserData();
    // You might want to emit an event or use a global state manager here
  }

  /// Handle Dio errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Response timeout. Please try again.';
      case DioExceptionType.badResponse:
        if (error.response?.data is Map<String, dynamic>) {
          final data = error.response?.data as Map<String, dynamic>;
          return data['message'] ?? 'Server error occurred';
        } else {
          return 'Server error: ${error.response?.statusCode}';
        }
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      default:
        return 'Network error: ${error.message ?? 'Unknown error occurred'}';
    }
  }

  /// Update user profile
  static Future<Result<User>> updateProfile({
    required String name,
    required String email,
    required String phone,
  }) async {
    try {
      final response = await _dio.put('/users/profile', data: {
        'name': name,
        'email': email,
        'phone': phone,
      });

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final user = User.fromJson(apiResponse.data!);

          // Update stored user data
          await StorageService.saveUserData(apiResponse.data!);

          return Success(user);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to update profile: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Failed to update profile: $e');
    }
  }

}
