<?php
/**
 * Chits API Routes - LiteSpeed Optimized
 */

require_once __DIR__ . '/../../controllers/ChitController.php';

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];

// Remove /api/chits prefix and get the endpoint
$endpoint = str_replace('/api/chits', '', parse_url($path, PHP_URL_PATH));
$endpoint = trim($endpoint, '/');

// Split path into segments
$segments = explode('/', $endpoint);
$chitId = $segments[0] ?? null;
$action = $segments[1] ?? null;

// Initialize controller
$chitController = new ChitController();

// LiteSpeed cache control for chits endpoints
if ($method === 'GET') {
    header('X-LiteSpeed-Cache-Control: public, max-age=900'); // 15 minutes
    header('X-LiteSpeed-Tag: chits');
} else {
    header('X-LiteSpeed-Cache-Control: no-cache');
}

try {
    switch ($method) {
        case 'GET':
            if (empty($chitId)) {
                // GET /api/chits - List all chits
                $chitController->getAllChits();
            } elseif (empty($action)) {
                // GET /api/chits/{id} - Get specific chit
                $chitController->getChit($chitId);
            } elseif ($action === 'members') {
                // GET /api/chits/{id}/members - Get chit members
                $chitController->getChitMembers($chitId);
            } elseif ($action === 'payments') {
                // GET /api/chits/{id}/payments - Get chit payments
                $chitController->getChitPayments($chitId);
            } elseif ($action === 'bidding') {
                // GET /api/chits/{id}/bidding - Get bidding rounds
                $chitController->getChitBidding($chitId);
            } elseif ($action === 'summary') {
                // GET /api/chits/{id}/summary - Get chit summary
                $chitController->getChitSummary($chitId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'POST':
            if (empty($chitId)) {
                // POST /api/chits - Create new chit
                $chitController->createChit();
            } elseif ($action === 'members') {
                // POST /api/chits/{id}/members - Add member to chit
                $chitController->addMember($chitId);
            } elseif ($action === 'start') {
                // POST /api/chits/{id}/start - Start chit
                $chitController->startChit($chitId);
            } elseif ($action === 'close') {
                // POST /api/chits/{id}/close - Close chit
                $chitController->closeChit($chitId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'PUT':
            if (!empty($chitId) && empty($action)) {
                // PUT /api/chits/{id} - Update chit
                $chitController->updateChit($chitId);
            } elseif (!empty($chitId) && $action === 'members' && !empty($segments[2])) {
                // PUT /api/chits/{id}/members/{memberId} - Update member
                $chitController->updateMember($chitId, $segments[2]);
            } elseif (!empty($chitId) && $action === 'activate') {
                // PUT /api/chits/{id}/activate - Activate draft chit
                $chitController->activateChit($chitId);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        case 'DELETE':
            if (!empty($chitId) && empty($action)) {
                // DELETE /api/chits/{id} - Delete chit
                $chitController->deleteChit($chitId);
            } elseif (!empty($chitId) && $action === 'members' && !empty($segments[2])) {
                // DELETE /api/chits/{id}/members/{memberId} - Remove member
                $chitController->removeMember($chitId, $segments[2]);
            } else {
                sendError('Endpoint not found', 404);
            }
            break;
            
        default:
            sendError('Method not allowed', 405);
            break;
    }
} catch (Exception $e) {
    error_log("Chits API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

?>
