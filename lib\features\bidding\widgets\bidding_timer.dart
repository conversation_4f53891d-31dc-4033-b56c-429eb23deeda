import 'dart:async';
import 'package:flutter/material.dart';

class BiddingTimer extends StatefulWidget {
  final DateTime endTime;
  final VoidCallback? onTimeUp;

  const BiddingTimer({
    super.key,
    required this.endTime,
    this.onTimeUp,
  });

  @override
  State<BiddingTimer> createState() => _BiddingTimerState();
}

class _BiddingTimerState extends State<BiddingTimer> {
  Timer? _timer;
  Duration _timeRemaining = Duration.zero;
  bool _isTimeUp = false;

  @override
  void initState() {
    super.initState();
    _updateTimeRemaining();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimeRemaining();
      
      if (_timeRemaining.inSeconds <= 0 && !_isTimeUp) {
        setState(() {
          _isTimeUp = true;
        });
        widget.onTimeUp?.call();
        timer.cancel();
      }
    });
  }

  void _updateTimeRemaining() {
    final now = DateTime.now();
    final remaining = widget.endTime.difference(now);
    
    setState(() {
      _timeRemaining = remaining.isNegative ? Duration.zero : remaining;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _isTimeUp
              ? [Colors.red, Colors.red.shade700]
              : _timeRemaining.inMinutes < 5
                  ? [Colors.orange, Colors.orange.shade700]
                  : [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.primary.withValues(alpha: 0.8)],
        ),
      ),
      child: Column(
        children: [
          Text(
            _isTimeUp ? 'TIME\'S UP!' : 'Time Remaining',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          if (!_isTimeUp)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildTimeUnit(
                  _timeRemaining.inHours.toString().padLeft(2, '0'),
                  'Hours',
                ),
                
                const Text(
                  ':',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                _buildTimeUnit(
                  (_timeRemaining.inMinutes % 60).toString().padLeft(2, '0'),
                  'Minutes',
                ),
                
                const Text(
                  ':',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                _buildTimeUnit(
                  (_timeRemaining.inSeconds % 60).toString().padLeft(2, '0'),
                  'Seconds',
                ),
              ],
            )
          else
            const Icon(
              Icons.timer_off,
              color: Colors.white,
              size: 48,
            ),
          
          const SizedBox(height: 8),
          
          Text(
            _isTimeUp
                ? 'Bidding has ended'
                : 'Ends at ${_formatTime(widget.endTime)}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeUnit(String value, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        const SizedBox(height: 4),
        
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}

// Compact timer for smaller spaces
class CompactBiddingTimer extends StatefulWidget {
  final DateTime endTime;
  final VoidCallback? onTimeUp;

  const CompactBiddingTimer({
    super.key,
    required this.endTime,
    this.onTimeUp,
  });

  @override
  State<CompactBiddingTimer> createState() => _CompactBiddingTimerState();
}

class _CompactBiddingTimerState extends State<CompactBiddingTimer> {
  Timer? _timer;
  Duration _timeRemaining = Duration.zero;
  bool _isTimeUp = false;

  @override
  void initState() {
    super.initState();
    _updateTimeRemaining();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimeRemaining();
      
      if (_timeRemaining.inSeconds <= 0 && !_isTimeUp) {
        setState(() {
          _isTimeUp = true;
        });
        widget.onTimeUp?.call();
        timer.cancel();
      }
    });
  }

  void _updateTimeRemaining() {
    final now = DateTime.now();
    final remaining = widget.endTime.difference(now);
    
    setState(() {
      _timeRemaining = remaining.isNegative ? Duration.zero : remaining;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _isTimeUp
            ? Colors.red
            : _timeRemaining.inMinutes < 5
                ? Colors.orange
                : Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _isTimeUp ? Icons.timer_off : Icons.timer,
            color: Colors.white,
            size: 16,
          ),
          
          const SizedBox(width: 6),
          
          Text(
            _isTimeUp
                ? 'Ended'
                : '${_timeRemaining.inHours.toString().padLeft(2, '0')}:${(_timeRemaining.inMinutes % 60).toString().padLeft(2, '0')}:${(_timeRemaining.inSeconds % 60).toString().padLeft(2, '0')}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

// Timer with progress indicator
class ProgressBiddingTimer extends StatefulWidget {
  final DateTime startTime;
  final DateTime endTime;
  final VoidCallback? onTimeUp;

  const ProgressBiddingTimer({
    super.key,
    required this.startTime,
    required this.endTime,
    this.onTimeUp,
  });

  @override
  State<ProgressBiddingTimer> createState() => _ProgressBiddingTimerState();
}

class _ProgressBiddingTimerState extends State<ProgressBiddingTimer> {
  Timer? _timer;
  Duration _timeRemaining = Duration.zero;
  double _progress = 0.0;
  bool _isTimeUp = false;

  @override
  void initState() {
    super.initState();
    _updateTimeRemaining();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTimeRemaining();
      
      if (_timeRemaining.inSeconds <= 0 && !_isTimeUp) {
        setState(() {
          _isTimeUp = true;
        });
        widget.onTimeUp?.call();
        timer.cancel();
      }
    });
  }

  void _updateTimeRemaining() {
    final now = DateTime.now();
    final remaining = widget.endTime.difference(now);
    final totalDuration = widget.endTime.difference(widget.startTime);
    final elapsed = now.difference(widget.startTime);
    
    setState(() {
      _timeRemaining = remaining.isNegative ? Duration.zero : remaining;
      _progress = totalDuration.inSeconds > 0 
          ? (elapsed.inSeconds / totalDuration.inSeconds).clamp(0.0, 1.0)
          : 1.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _isTimeUp ? 'Bidding Ended' : 'Time Remaining',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                
                Text(
                  _isTimeUp
                      ? 'Completed'
                      : '${_timeRemaining.inHours.toString().padLeft(2, '0')}:${(_timeRemaining.inMinutes % 60).toString().padLeft(2, '0')}:${(_timeRemaining.inSeconds % 60).toString().padLeft(2, '0')}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _isTimeUp
                        ? Colors.red
                        : _timeRemaining.inMinutes < 5
                            ? Colors.orange
                            : Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            LinearProgressIndicator(
              value: _progress,
              backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                _isTimeUp
                    ? Colors.red
                    : _timeRemaining.inMinutes < 5
                        ? Colors.orange
                        : Theme.of(context).colorScheme.primary,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Started: ${_formatTime(widget.startTime)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                
                Text(
                  'Ends: ${_formatTime(widget.endTime)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
