<?php
/**
 * Notification Model
 * 
 * Handles notification database operations
 */

class Notification {
    private $db;
    private $conn;
    
    // Notification properties
    public $id;
    public $user_id;
    public $chit_id;
    public $title;
    public $message;
    public $type;
    public $is_read;
    public $scheduled_at;
    public $sent_at;
    public $created_at;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * Create a new notification
     * 
     * @return bool True on success, false on failure
     */
    public function create() {
        $sql = "INSERT INTO notifications (user_id, chit_id, title, message, type, scheduled_at) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            $result = $stmt->execute([
                $this->user_id,
                $this->chit_id,
                $this->title,
                $this->message,
                $this->type,
                $this->scheduled_at
            ]);
            
            if ($result) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Notification creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find notification by ID
     * 
     * @param int $id Notification ID
     * @return bool True if found, false otherwise
     */
    public function findById($id) {
        $sql = "SELECT * FROM notifications WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find notification by ID failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark notification as read
     * 
     * @return bool True on success, false on failure
     */
    public function markAsRead() {
        $sql = "UPDATE notifications SET is_read = 1 WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Mark notification as read failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete notification
     * 
     * @return bool True on success, false on failure
     */
    public function delete() {
        $sql = "DELETE FROM notifications WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Notification deletion failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get notifications for a specific user
     * 
     * @param int $userId User ID
     * @param int $limit Limit number of results
     * @return array Array of notifications
     */
    public function getUserNotifications($userId, $limit = 50) {
        $sql = "SELECT n.*, c.name as chit_name
                FROM notifications n
                LEFT JOIN chits c ON n.chit_id = c.id
                WHERE n.user_id = ?
                ORDER BY n.created_at DESC LIMIT ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get user notifications failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get unread notifications for a user
     * 
     * @param int $userId User ID
     * @return array Array of unread notifications
     */
    public function getUnreadNotifications($userId) {
        $sql = "SELECT n.*, c.name as chit_name
                FROM notifications n
                LEFT JOIN chits c ON n.chit_id = c.id
                WHERE n.user_id = ? AND n.is_read = 0
                ORDER BY n.created_at DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get unread notifications failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Mark all notifications as read for a user
     * 
     * @param int $userId User ID
     * @return int Number of notifications marked as read
     */
    public function markAllAsRead($userId) {
        $sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Mark all notifications as read failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get notification statistics for a user
     * 
     * @param int $userId User ID
     * @return array Notification statistics
     */
    public function getUserNotificationStats($userId) {
        $sql = "SELECT 
                COUNT(*) as total_notifications,
                SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count,
                SUM(CASE WHEN is_read = 1 THEN 1 ELSE 0 END) as read_count,
                SUM(CASE WHEN type = 'reminder' THEN 1 ELSE 0 END) as reminder_count,
                SUM(CASE WHEN type = 'bidding' THEN 1 ELSE 0 END) as bidding_count,
                SUM(CASE WHEN type = 'payment' THEN 1 ELSE 0 END) as payment_count,
                SUM(CASE WHEN type = 'general' THEN 1 ELSE 0 END) as general_count,
                SUM(CASE WHEN type = 'system' THEN 1 ELSE 0 END) as system_count
                FROM notifications 
                WHERE user_id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get user notification stats failed: " . $e->getMessage());
            return [
                'total_notifications' => 0,
                'unread_count' => 0,
                'read_count' => 0,
                'reminder_count' => 0,
                'bidding_count' => 0,
                'payment_count' => 0,
                'general_count' => 0,
                'system_count' => 0
            ];
        }
    }
    
    /**
     * Get notifications by type
     * 
     * @param int $userId User ID
     * @param string $type Notification type
     * @param int $limit Limit number of results
     * @return array Array of notifications
     */
    public function getNotificationsByType($userId, $type, $limit = 50) {
        $sql = "SELECT n.*, c.name as chit_name
                FROM notifications n
                LEFT JOIN chits c ON n.chit_id = c.id
                WHERE n.user_id = ? AND n.type = ?
                ORDER BY n.created_at DESC LIMIT ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId, $type, $limit]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get notifications by type failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get notification details with related information
     * 
     * @return array Notification details
     */
    public function getDetails() {
        $sql = "SELECT n.*, c.name as chit_name, u.name as user_name
                FROM notifications n
                LEFT JOIN chits c ON n.chit_id = c.id
                LEFT JOIN users u ON n.user_id = u.id
                WHERE n.id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->id]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get notification details failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get scheduled notifications that need to be sent
     * 
     * @return array Array of scheduled notifications
     */
    public function getScheduledNotifications() {
        $sql = "SELECT n.*, c.name as chit_name, u.name as user_name, u.email, u.phone
                FROM notifications n
                LEFT JOIN chits c ON n.chit_id = c.id
                LEFT JOIN users u ON n.user_id = u.id
                WHERE n.scheduled_at <= CURRENT_TIMESTAMP AND n.sent_at IS NULL
                ORDER BY n.scheduled_at ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get scheduled notifications failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Mark notification as sent
     * 
     * @return bool True on success, false on failure
     */
    public function markAsSent() {
        $sql = "UPDATE notifications SET sent_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Mark notification as sent failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create system notification
     * 
     * @param int $userId User ID
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type
     * @param int $chitId Chit ID (optional)
     * @return bool True on success, false on failure
     */
    public static function createSystemNotification($userId, $title, $message, $type = 'system', $chitId = null) {
        try {
            $notification = new self();
            $notification->user_id = $userId;
            $notification->chit_id = $chitId;
            $notification->title = $title;
            $notification->message = $message;
            $notification->type = $type;
            
            return $notification->create();
        } catch (Exception $e) {
            error_log("Create system notification failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Set object properties from database row
     * 
     * @param array $row Database row
     */
    private function setProperties($row) {
        $this->id = $row['id'];
        $this->user_id = $row['user_id'];
        $this->chit_id = $row['chit_id'];
        $this->title = $row['title'];
        $this->message = $row['message'];
        $this->type = $row['type'];
        $this->is_read = $row['is_read'];
        $this->scheduled_at = $row['scheduled_at'];
        $this->sent_at = $row['sent_at'];
        $this->created_at = $row['created_at'];
    }
}
?>
