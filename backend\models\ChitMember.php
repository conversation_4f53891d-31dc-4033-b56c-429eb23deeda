<?php
/**
 * ChitMember Model
 * 
 * Handles chit member database operations
 */

class ChitMember {
    private $db;
    private $conn;
    
    // ChitMember properties
    public $id;
    public $chit_id;
    public $user_id;
    public $role;
    public $status;
    public $joined_at;
    public $has_won;
    public $winning_round;
    public $winning_amount;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * Create a new chit member
     * 
     * @return bool True on success, false on failure
     */
    public function create() {
        $sql = "INSERT INTO chit_members (chit_id, user_id, role, status, joined_at) 
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            $result = $stmt->execute([
                $this->chit_id,
                $this->user_id,
                $this->role ?? 'member',
                $this->status ?? 'active'
            ]);
            
            if ($result) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("ChitMember creation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find chit member by ID
     * 
     * @param int $id ChitMember ID
     * @return bool True if found, false otherwise
     */
    public function findById($id) {
        $sql = "SELECT * FROM chit_members WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            
            if ($row = $stmt->fetch()) {
                $this->setProperties($row);
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Find chit member by ID failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update chit member
     * 
     * @return bool True on success, false on failure
     */
    public function update() {
        $sql = "UPDATE chit_members SET role = ?, status = ?, has_won = ?, 
                winning_round = ?, winning_amount = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            
            return $stmt->execute([
                $this->role,
                $this->status,
                $this->has_won,
                $this->winning_round,
                $this->winning_amount,
                $this->id
            ]);
        } catch (PDOException $e) {
            error_log("ChitMember update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove chit member
     * 
     * @return bool True on success, false on failure
     */
    public function remove() {
        $sql = "DELETE FROM chit_members WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("ChitMember removal failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all members of a chit
     * 
     * @param int $chitId Chit ID
     * @return array Array of chit members
     */
    public function getChitMembers($chitId) {
        $sql = "SELECT cm.*, u.name, u.email, u.phone, u.profile_image
                FROM chit_members cm
                LEFT JOIN users u ON cm.user_id = u.id
                WHERE cm.chit_id = ?
                ORDER BY cm.role DESC, cm.joined_at ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get chit members failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get user's chit memberships
     * 
     * @param int $userId User ID
     * @return array Array of chit memberships
     */
    public function getUserMemberships($userId) {
        $sql = "SELECT cm.*, c.name as chit_name, c.status as chit_status,
                c.total_amount, c.monthly_amount, c.start_date
                FROM chit_members cm
                LEFT JOIN chits c ON cm.chit_id = c.id
                WHERE cm.user_id = ?
                ORDER BY c.start_date DESC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get user memberships failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if user is already a member of a chit
     * 
     * @param int $chitId Chit ID
     * @param int $userId User ID
     * @return bool True if member, false otherwise
     */
    public function isMember($chitId, $userId) {
        $sql = "SELECT id FROM chit_members WHERE chit_id = ? AND user_id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId, $userId]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Check membership failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark member as winner
     * 
     * @param int $round Winning round
     * @param float $amount Winning amount
     * @return bool True on success, false on failure
     */
    public function markAsWinner($round, $amount) {
        $sql = "UPDATE chit_members SET has_won = 1, winning_round = ?, 
                winning_amount = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$round, $amount, $this->id]);
        } catch (PDOException $e) {
            error_log("Mark as winner failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get member details with user information
     * 
     * @return array Member details
     */
    public function getDetails() {
        $sql = "SELECT cm.*, u.name, u.email, u.phone, u.profile_image
                FROM chit_members cm
                LEFT JOIN users u ON cm.user_id = u.id
                WHERE cm.id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->id]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get member details failed: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get active members of a chit
     * 
     * @param int $chitId Chit ID
     * @return array Array of active members
     */
    public function getActiveMembers($chitId) {
        $sql = "SELECT cm.*, u.name, u.email, u.phone, u.profile_image
                FROM chit_members cm
                LEFT JOIN users u ON cm.user_id = u.id
                WHERE cm.chit_id = ? AND cm.status = 'active'
                ORDER BY cm.role DESC, cm.joined_at ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get active members failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get eligible members for bidding (haven't won yet)
     * 
     * @param int $chitId Chit ID
     * @return array Array of eligible members
     */
    public function getEligibleMembers($chitId) {
        $sql = "SELECT cm.*, u.name, u.email, u.phone, u.profile_image
                FROM chit_members cm
                LEFT JOIN users u ON cm.user_id = u.id
                WHERE cm.chit_id = ? AND cm.status = 'active' AND cm.has_won = 0
                ORDER BY cm.joined_at ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get eligible members failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get winners of a chit
     * 
     * @param int $chitId Chit ID
     * @return array Array of winners
     */
    public function getWinners($chitId) {
        $sql = "SELECT cm.*, u.name, u.email, u.phone, u.profile_image
                FROM chit_members cm
                LEFT JOIN users u ON cm.user_id = u.id
                WHERE cm.chit_id = ? AND cm.has_won = 1
                ORDER BY cm.winning_round ASC";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$chitId]);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get winners failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Deactivate member
     * 
     * @return bool True on success, false on failure
     */
    public function deactivate() {
        $sql = "UPDATE chit_members SET status = 'inactive', 
                updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        
        try {
            $stmt = $this->conn->prepare($sql);
            return $stmt->execute([$this->id]);
        } catch (PDOException $e) {
            error_log("Member deactivation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Set object properties from database row
     * 
     * @param array $row Database row
     */
    private function setProperties($row) {
        $this->id = $row['id'];
        $this->chit_id = $row['chit_id'];
        $this->user_id = $row['user_id'];
        $this->role = $row['role'];
        $this->status = $row['status'];
        $this->joined_at = $row['joined_at'];
        $this->has_won = $row['has_won'];
        $this->winning_round = $row['winning_round'];
        $this->winning_amount = $row['winning_amount'];
        $this->created_at = $row['created_at'];
        $this->updated_at = $row['updated_at'];
    }

    /**
     * Get members accessible to the user (only from chits they organize or are members of)
     */
    public function getUserAccessibleMembers($userId, $page = 1, $limit = 20, $search = '', $chitId = null, $status = null) {
        try {
            $offset = ($page - 1) * $limit;

            $whereConditions = [];
            $params = [$userId, $userId];

            // Filter by chit access
            $accessCondition = "(c.organizer_id = ? OR EXISTS(SELECT 1 FROM chit_members cm2 WHERE cm2.chit_id = c.id AND cm2.user_id = ?))";

            if (!empty($search)) {
                $whereConditions[] = "(u.name LIKE ? OR u.email LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }

            if ($chitId) {
                $whereConditions[] = "cm.chit_id = ?";
                $params[] = $chitId;
            }

            if ($status) {
                $whereConditions[] = "cm.status = ?";
                $params[] = $status;
            }

            $whereClause = $accessCondition;
            if (!empty($whereConditions)) {
                $whereClause .= ' AND ' . implode(' AND ', $whereConditions);
            }

            // Get total count
            $countSql = "SELECT COUNT(DISTINCT cm.id) FROM chit_members cm
                        JOIN chits c ON cm.chit_id = c.id
                        JOIN users u ON cm.user_id = u.id
                        WHERE $whereClause";
            $stmt = $this->conn->prepare($countSql);
            $stmt->execute($params);
            $total = (int)$stmt->fetchColumn();

            // Get members
            $sql = "SELECT cm.*, u.name as user_name, u.email as user_email,
                           c.name as chit_name, c.total_amount as chit_amount
                    FROM chit_members cm
                    JOIN chits c ON cm.chit_id = c.id
                    JOIN users u ON cm.user_id = u.id
                    WHERE $whereClause
                    ORDER BY cm.created_at DESC
                    LIMIT ? OFFSET ?";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $members = $stmt->fetchAll();

            return [
                'members' => $members,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ];

        } catch (Exception $e) {
            error_log("Get user accessible members error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all members with pagination and filters
     */
    public function getAll($page = 1, $limit = 20, $search = '', $chitId = null, $status = null) {
        try {
            $offset = ($page - 1) * $limit;

            $whereConditions = [];
            $params = [];

            if (!empty($search)) {
                $whereConditions[] = "u.name LIKE ?";
                $params[] = "%$search%";
            }

            if ($chitId) {
                $whereConditions[] = "cm.chit_id = ?";
                $params[] = $chitId;
            }

            if ($status) {
                $whereConditions[] = "cm.status = ?";
                $params[] = $status;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get total count
            $countSql = "SELECT COUNT(*)
                        FROM chit_members cm
                        JOIN users u ON cm.user_id = u.id
                        JOIN chits c ON cm.chit_id = c.id
                        $whereClause";
            $stmt = $this->db->prepare($countSql);
            $stmt->execute($params);
            $total = (int)$stmt->fetchColumn();

            // Get members
            $sql = "SELECT cm.*, u.name as user_name, u.email, u.phone, c.name as chit_name
                    FROM chit_members cm
                    JOIN users u ON cm.user_id = u.id
                    JOIN chits c ON cm.chit_id = c.id
                    $whereClause
                    ORDER BY cm.created_at DESC
                    LIMIT ? OFFSET ?";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $members = $stmt->fetchAll();

            return [
                'members' => $members,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ];

        } catch (Exception $e) {
            error_log("Get all members error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get detailed member information
     */
    public function getDetailedInfo() {
        try {
            $sql = "SELECT cm.*, u.name as user_name, u.email, u.phone, c.name as chit_name,
                           c.total_amount, c.monthly_amount, c.duration_months
                    FROM chit_members cm
                    JOIN users u ON cm.user_id = u.id
                    JOIN chits c ON cm.chit_id = c.id
                    WHERE cm.id = ?";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetch();

        } catch (Exception $e) {
            error_log("Get detailed member info error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get member's chits
     */
    public function getChits() {
        try {
            $sql = "SELECT c.*, cm.member_number, cm.status as member_status
                    FROM chits c
                    JOIN chit_members cm ON c.id = cm.chit_id
                    WHERE cm.user_id = ?
                    ORDER BY c.created_at DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->user_id]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Get member chits error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get member's payments
     */
    public function getPayments() {
        try {
            $sql = "SELECT p.*, c.name as chit_name
                    FROM payments p
                    JOIN chit_members cm ON p.member_id = cm.id
                    JOIN chits c ON cm.chit_id = c.id
                    WHERE cm.id = ?
                    ORDER BY p.due_date DESC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Get member payments error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get member summary
     */
    public function getSummary() {
        try {
            // Get payment statistics
            $sql = "SELECT
                        COUNT(*) as total_payments,
                        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_payments,
                        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                        COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_payments,
                        COALESCE(SUM(CASE WHEN status = 'paid' THEN amount END), 0) as total_paid,
                        COALESCE(SUM(CASE WHEN status IN ('pending', 'overdue') THEN amount END), 0) as total_pending
                    FROM payments
                    WHERE member_id = ?";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->id]);
            $paymentStats = $stmt->fetch();

            // Get chit information
            $sql = "SELECT c.name, c.total_amount, c.monthly_amount, c.duration_months, c.status
                    FROM chits c
                    WHERE c.id = ?";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->chit_id]);
            $chitInfo = $stmt->fetch();

            return [
                'member_info' => [
                    'id' => $this->id,
                    'member_number' => $this->member_number,
                    'status' => $this->status,
                    'joined_date' => $this->joined_date
                ],
                'chit_info' => $chitInfo,
                'payment_stats' => $paymentStats
            ];

        } catch (Exception $e) {
            error_log("Get member summary error: " . $e->getMessage());
            return [];
        }
    }
}
?>
