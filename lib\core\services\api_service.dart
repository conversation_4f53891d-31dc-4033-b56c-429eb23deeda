import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'storage_service.dart';

class ApiService {
  static late Dio _dio;
  static final Logger _logger = Logger();

  // Getter for dio instance
  static Dio get dio => _dio;

  // Base URL - Update this with your actual backend URL
  static const String baseUrl = 'https://chit.mobunite.com/api';
  
  // API Endpoints
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String logoutEndpoint = '/auth/logout';
  static const String forgotPasswordEndpoint = '/auth/forgot-password';
  static const String resetPasswordEndpoint = '/auth/reset-password';
  static const String verifyTokenEndpoint = '/auth/verify-token';
  static const String profileEndpoint = '/user/profile';
  static const String updateProfileEndpoint = '/user/update-profile';
  static const String changePasswordEndpoint = '/user/change-password';

  static void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      followRedirects: true,
      maxRedirects: 5,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token to requests
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          
          _logger.d('Request: ${options.method} ${options.path}');
          _logger.d('Headers: ${options.headers}');
          _logger.d('Data: ${options.data}');
          
          handler.next(options);
        },
        onResponse: (response, handler) {
          _logger.d('Response: ${response.statusCode} ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) {
          _logger.e('Error: ${error.message}');
          _logger.e('Response: ${error.response?.data}');
          handler.next(error);
        },
      ),
    );
  }

  // Generic request method
  static Future<Map<String, dynamic>> _request(
    String method,
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _dio.get(endpoint, queryParameters: queryParameters);
          break;
        case 'POST':
          response = await _dio.post(endpoint, data: data, queryParameters: queryParameters);
          break;
        case 'PUT':
          response = await _dio.put(endpoint, data: data, queryParameters: queryParameters);
          break;
        case 'DELETE':
          response = await _dio.delete(endpoint, queryParameters: queryParameters);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data is Map<String, dynamic> 
            ? response.data 
            : {'success': true, 'data': response.data};
      } else {
        return {
          'success': false,
          'message': 'Request failed with status: ${response.statusCode}',
        };
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      return {
        'success': false,
        'message': 'Unexpected error: $e',
      };
    }
  }

  // Handle Dio errors
  static Map<String, dynamic> _handleDioError(DioException error) {
    String message = 'An error occurred';
    
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        message = 'Connection timeout. Please check your internet connection.';
        break;
      case DioExceptionType.sendTimeout:
        message = 'Request timeout. Please try again.';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'Response timeout. Please try again.';
        break;
      case DioExceptionType.badResponse:
        if (error.response?.data is Map<String, dynamic>) {
          message = error.response?.data['message'] ?? 'Server error occurred';
        } else {
          message = 'Server error: ${error.response?.statusCode}';
        }
        break;
      case DioExceptionType.cancel:
        message = 'Request was cancelled';
        break;
      case DioExceptionType.connectionError:
        message = 'No internet connection. Please check your network.';
        break;
      default:
        message = 'Network error: ${error.message}';
    }

    return {
      'success': false,
      'message': message,
      'error_code': error.response?.statusCode,
    };
  }

  // Authentication APIs
  static Future<Map<String, dynamic>> login(String email, String password) async {
    return await _request('POST', loginEndpoint, data: {
      'email': email,
      'password': password,
    });
  }

  static Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
    required String phone,
  }) async {
    return await _request('POST', registerEndpoint, data: {
      'name': name,
      'email': email,
      'password': password,
      'phone': phone,
    });
  }

  static Future<Map<String, dynamic>> logout() async {
    return await _request('POST', logoutEndpoint);
  }

  static Future<Map<String, dynamic>> forgotPassword(String email) async {
    return await _request('POST', forgotPasswordEndpoint, data: {
      'email': email,
    });
  }

  static Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    return await _request('POST', resetPasswordEndpoint, data: {
      'token': token,
      'password': newPassword,
    });
  }

  static Future<bool> verifyToken(String token) async {
    try {
      final response = await _request('POST', verifyTokenEndpoint, data: {
        'token': token,
      });
      return response['success'] == true;
    } catch (e) {
      return false;
    }
  }

  // User Profile APIs
  static Future<Map<String, dynamic>> getUserProfile() async {
    return await _request('GET', profileEndpoint);
  }

  static Future<Map<String, dynamic>> updateProfile({
    String? name,
    String? email,
    String? phone,
  }) async {
    final data = <String, dynamic>{};
    if (name != null) data['name'] = name;
    if (email != null) data['email'] = email;
    if (phone != null) data['phone'] = phone;

    return await _request('PUT', updateProfileEndpoint, data: data);
  }

  static Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    return await _request('POST', changePasswordEndpoint, data: {
      'current_password': currentPassword,
      'new_password': newPassword,
    });
  }

  // Utility methods
  static void updateBaseUrl(String newBaseUrl) {
    _dio.options.baseUrl = newBaseUrl;
  }

  static void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  static void removeAuthToken() {
    _dio.options.headers.remove('Authorization');
  }

  // Network connectivity check
  static Future<bool> checkConnectivity() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
