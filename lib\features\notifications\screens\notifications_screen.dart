import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/notification_provider.dart';
import '../../../core/models/notification_model.dart';
import '../../../core/router/app_router.dart';
import '../widgets/notification_card.dart';
import '../widgets/notification_filter_bar.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  NotificationType? _selectedType;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    await notificationProvider.refreshNotifications();
  }

  Future<void> _handleRefresh() async {
    await _loadNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Notifications'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.home),
          onPressed: () => AppNavigation.goToDashboard(context),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Unread'),
            Tab(text: 'Important'),
            Tab(text: 'Archive'),
          ],
        ),
        actions: [
          Consumer<NotificationProvider>(
            builder: (context, notificationProvider, child) {
              final unreadCount = notificationProvider.unreadCount;
              return Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.mark_email_read),
                    onPressed: unreadCount > 0 ? _markAllAsRead : null,
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '$unreadCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'settings':
                  _showNotificationSettings();
                  break;
                case 'clear_all':
                  _clearAllNotifications();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('Clear All'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Bar
          NotificationFilterBar(
            selectedType: _selectedType,
            onTypeChanged: (type) {
              setState(() {
                _selectedType = type;
              });
            },
          ),
          
          // Notifications List
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildNotificationList(null), // All notifications
                _buildNotificationList(null, unreadOnly: true),
                _buildNotificationList(null, importantOnly: true),
                _buildNotificationList(null, archivedOnly: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationList(
    NotificationType? typeFilter, {
    bool unreadOnly = false,
    bool importantOnly = false,
    bool archivedOnly = false,
  }) {
    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        if (notificationProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        List<NotificationModel> notifications = notificationProvider.notifications;

        // Apply filters
        if (typeFilter != null || _selectedType != null) {
          final filterType = typeFilter ?? _selectedType!;
          notifications = notifications.where((n) => n.type == filterType).toList();
        }

        if (unreadOnly) {
          notifications = notifications.where((n) => !n.isRead).toList();
        }

        if (importantOnly) {
          notifications = notifications.where((n) => n.priority == NotificationPriority.high).toList();
        }

        if (archivedOnly) {
          notifications = notifications.where((n) => n.isArchived).toList();
        } else {
          // Exclude archived by default
          notifications = notifications.where((n) => !n.isArchived).toList();
        }

        if (notifications.isEmpty) {
          return _buildEmptyState(unreadOnly, importantOnly, archivedOnly);
        }

        return RefreshIndicator(
          onRefresh: _handleRefresh,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: NotificationCard(
                  notification: notification,
                  onTap: () {
                    _handleNotificationTap(notification);
                  },
                  onMarkAsRead: !notification.isRead
                      ? () => _markAsRead(notification)
                      : null,
                  onArchive: !notification.isArchived
                      ? () => _archiveNotification(notification)
                      : null,
                  onDelete: () => _deleteNotification(notification),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(bool unreadOnly, bool importantOnly, bool archivedOnly) {
    String title;
    String subtitle;
    IconData icon;

    if (unreadOnly) {
      title = 'No Unread Notifications';
      subtitle = 'You\'re all caught up!';
      icon = Icons.mark_email_read;
    } else if (importantOnly) {
      title = 'No Important Notifications';
      subtitle = 'No urgent notifications at the moment';
      icon = Icons.priority_high;
    } else if (archivedOnly) {
      title = 'No Archived Notifications';
      subtitle = 'Archived notifications will appear here';
      icon = Icons.archive;
    } else {
      title = 'No Notifications';
      subtitle = 'You\'ll see notifications here when they arrive';
      icon = Icons.notifications_none;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _handleNotificationTap(NotificationModel notification) {
    // Mark as read if not already read
    if (!notification.isRead) {
      _markAsRead(notification);
    }

    // Handle navigation based on notification type
    switch (notification.type) {
      case NotificationType.payment:
        // Navigate to payment details
        break;
      case NotificationType.bidding:
        // Navigate to bidding screen
        break;
      case NotificationType.general:
        // Navigate to chit details
        break;
      case NotificationType.reminder:
        // Navigate to reminder details
        break;
      case NotificationType.system:
        // Show system notification details
        break;
    }

    // Show notification details
    _showNotificationDetails(notification);
  }

  void _markAsRead(NotificationModel notification) {
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    notificationProvider.markAsRead(notification.id);
  }

  void _markAllAsRead() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Mark All as Read'),
        content: const Text('Are you sure you want to mark all notifications as read?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
              notificationProvider.markAllAsRead();
            },
            child: const Text('Mark All Read'),
          ),
        ],
      ),
    );
  }

  void _archiveNotification(NotificationModel notification) {
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    notificationProvider.archiveNotification(notification.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Notification archived'),
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            notificationProvider.unarchiveNotification(notification.id);
          },
        ),
      ),
    );
  }

  void _deleteNotification(NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notification'),
        content: const Text('Are you sure you want to delete this notification?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
              notificationProvider.deleteNotification(notification.id);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showNotificationDetails(NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(notification.body),
              if (notification.data.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'Additional Information:',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                ...notification.data.entries.map((entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text('${entry.key}: ${entry.value}'),
                )),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    // Show notification settings dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('Push Notifications'),
              subtitle: const Text('Receive push notifications'),
              value: true,
              onChanged: (value) {
                // TODO: Implement notification toggle
              },
            ),
            SwitchListTile(
              title: const Text('Email Notifications'),
              subtitle: const Text('Receive email notifications'),
              value: true,
              onChanged: (value) {
                // TODO: Implement email toggle
              },
            ),
            SwitchListTile(
              title: const Text('Payment Reminders'),
              subtitle: const Text('Get payment due reminders'),
              value: true,
              onChanged: (value) {
                // TODO: Implement reminder toggle
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _clearAllNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to clear all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
              notificationProvider.clearAllNotifications();
            },
            child: const Text('Clear All', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
