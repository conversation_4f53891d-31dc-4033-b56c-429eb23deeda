import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'debug_logger.dart';

/// Network connectivity and API testing utility
class NetworkTest {
  static final Dio _dio = Dio(BaseOptions(
    connectTimeout: const Duration(seconds: 15),
    receiveTimeout: const Duration(seconds: 15),
    sendTimeout: const Duration(seconds: 15),
  ));

  /// Test basic internet connectivity
  static Future<bool> testInternetConnection() async {
    try {
      DebugLogger.logInfo('NetworkTest', '🌐 Testing internet connectivity...');
      
      final response = await _dio.get(
        'https://www.google.com',
        options: Options(
          validateStatus: (status) => status != null && status < 500,
        ),
      );
      
      final isConnected = response.statusCode == 200;
      DebugLogger.logInfo('NetworkTest', 
        isConnected ? '✅ Internet connection: OK' : '❌ Internet connection: FAILED');
      
      return isConnected;
    } catch (e) {
      DebugLogger.logError('NetworkTest', '❌ Internet connection test failed: $e');
      return false;
    }
  }

  /// Test if the backend domain is accessible
  static Future<bool> testBackendDomain(String domain) async {
    try {
      DebugLogger.logInfo('NetworkTest', '🔗 Testing backend domain: $domain');
      
      final response = await _dio.get(
        domain,
        options: Options(
          validateStatus: (status) => status != null && status < 500,
        ),
      );
      
      final isAccessible = response.statusCode != null && response.statusCode! < 500;
      DebugLogger.logInfo('NetworkTest', 
        isAccessible 
          ? '✅ Backend domain accessible: ${response.statusCode}' 
          : '❌ Backend domain not accessible: ${response.statusCode}');
      
      return isAccessible;
    } catch (e) {
      DebugLogger.logError('NetworkTest', '❌ Backend domain test failed: $e');
      return false;
    }
  }

  /// Test if the API endpoint is accessible
  static Future<bool> testApiEndpoint(String apiUrl) async {
    try {
      DebugLogger.logInfo('NetworkTest', '📡 Testing API endpoint: $apiUrl');
      
      final response = await _dio.get(
        apiUrl,
        options: Options(
          validateStatus: (status) => status != null && status < 500,
        ),
      );
      
      final isAccessible = response.statusCode != null && response.statusCode! < 500;
      DebugLogger.logInfo('NetworkTest', 
        isAccessible 
          ? '✅ API endpoint accessible: ${response.statusCode}' 
          : '❌ API endpoint not accessible: ${response.statusCode}');
      
      if (isAccessible && response.data != null) {
        DebugLogger.logInfo('NetworkTest', '📄 API response: ${response.data}');
      }
      
      return isAccessible;
    } catch (e) {
      DebugLogger.logError('NetworkTest', '❌ API endpoint test failed: $e');
      return false;
    }
  }

  /// Test login endpoint specifically
  static Future<Map<String, dynamic>> testLoginEndpoint(String loginUrl) async {
    try {
      DebugLogger.logInfo('NetworkTest', '🔐 Testing login endpoint: $loginUrl');
      
      // Test with invalid credentials to see if endpoint responds
      final response = await _dio.post(
        loginUrl,
        data: {
          'email': '<EMAIL>',
          'password': 'invalid_password_for_testing'
        },
        options: Options(
          validateStatus: (status) => status != null,
        ),
      );
      
      final result = {
        'accessible': true,
        'status_code': response.statusCode,
        'response': response.data,
        'message': 'Login endpoint is responding'
      };
      
      DebugLogger.logInfo('NetworkTest', '✅ Login endpoint test result: $result');
      return result;
      
    } catch (e) {
      final result = {
        'accessible': false,
        'error': e.toString(),
        'message': 'Login endpoint is not accessible'
      };
      
      DebugLogger.logError('NetworkTest', '❌ Login endpoint test failed: $result');
      return result;
    }
  }

  /// Comprehensive network diagnostics
  static Future<Map<String, dynamic>> runComprehensiveDiagnostics() async {
    DebugLogger.logInfo('NetworkTest', '🔍 Running comprehensive network diagnostics...');
    
    final results = <String, dynamic>{};
    
    // Test 1: Internet connectivity
    results['internet_connection'] = await testInternetConnection();
    
    // Test 2: Backend domain
    results['backend_domain'] = await testBackendDomain('https://chit.mobunite.com');

    // Test 3: API endpoint
    results['api_endpoint'] = await testApiEndpoint('https://chit.mobunite.com/api');

    // Test 4: Login endpoint
    results['login_endpoint'] = await testLoginEndpoint('https://chit.mobunite.com/api/auth/login');
    
    // Overall assessment
    final allGood = results['internet_connection'] == true &&
                   results['backend_domain'] == true &&
                   results['api_endpoint'] == true &&
                   (results['login_endpoint'] as Map)['accessible'] == true;
    
    results['overall_status'] = allGood ? 'HEALTHY' : 'ISSUES_DETECTED';
    
    DebugLogger.logInfo('NetworkTest', '📊 Comprehensive diagnostics complete');
    DebugLogger.logInfo('NetworkTest', '🎯 Overall status: ${results['overall_status']}');
    
    return results;
  }

  /// Quick network check for login screen
  static Future<String> quickNetworkCheck() async {
    try {
      // Create a quick Dio instance with shorter timeouts for this specific check
      final quickDio = Dio(BaseOptions(
        connectTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 5),
        sendTimeout: const Duration(seconds: 5),
      ));

      // Quick test of the login endpoint
      final response = await quickDio.get('https://chit.mobunite.com/api');
      
      if (response.statusCode == 200) {
        return 'Network OK';
      } else {
        return 'Server responding with status: ${response.statusCode}';
      }
    } on DioException catch (e) {
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connection timeout - Check internet connection';
        case DioExceptionType.connectionError:
          return 'Connection error - Server may be down';
        case DioExceptionType.badResponse:
          return 'Server error: ${e.response?.statusCode}';
        default:
          return 'Network error: ${e.message ?? 'Unknown network error'}';
      }
    } catch (e) {
      return 'Unexpected network error: ${e.toString()}';
    }
  }

  /// Log detailed network information
  static void logNetworkInfo() {
    DebugLogger.logInfo('NetworkTest', '📱 NETWORK CONFIGURATION:');
    DebugLogger.logInfo('NetworkTest', '   Base URL: https://chit.mobunite.com/api');
    DebugLogger.logInfo('NetworkTest', '   Login URL: https://chit.mobunite.com/api/auth/login');
    DebugLogger.logInfo('NetworkTest', '   Protocol: HTTPS');
    DebugLogger.logInfo('NetworkTest', '   Timeout: 30 seconds');
    DebugLogger.logInfo('NetworkTest', '   Platform: ${defaultTargetPlatform.name}');
  }
}
