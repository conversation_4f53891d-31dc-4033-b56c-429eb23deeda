import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/chit_model.dart';
import 'api_service.dart';
import 'storage_service.dart';

class MemberService {
  static late Dio _dio;

  static void initialize() {
    ApiService.initialize();
    _dio = Dio(BaseOptions(
      baseUrl: ApiService.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token to requests
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
      ),
    );
  }

  /// Add a member to a chit
  static Future<Result<ChitMember>> addMember({
    required String chitId,
    required String name,
    required String email,
    required String phone,
    MemberRole role = MemberRole.member,
  }) async {
    try {
      final data = {
        'chit_id': chitId,
        'name': name,
        'email': email,
        'phone': phone,
        'role': role.name,
      };

      final response = await _dio.post('/chits/members/$chitId', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final member = ChitMember.fromJson(apiResponse.data!);
          return Success(member);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to add member');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Remove a member from a chit
  static Future<Result<void>> removeMember({
    required String chitId,
    required String memberId,
  }) async {
    try {
      final response = await _dio.delete('/chits/remove-member/$chitId/$memberId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to remove member');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Update member details
  static Future<Result<ChitMember>> updateMember({
    required String chitId,
    required String memberId,
    String? name,
    String? email,
    String? phone,
    MemberRole? role,
    bool? isActive,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (name != null) data['name'] = name;
      if (email != null) data['email'] = email;
      if (phone != null) data['phone'] = phone;
      if (role != null) data['role'] = role.name;
      if (isActive != null) data['is_active'] = isActive;

      final response = await _dio.put('/chits/members/$chitId/$memberId', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final member = ChitMember.fromJson(apiResponse.data!);
          return Success(member);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to update member');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get members for a chit
  static Future<Result<List<ChitMember>>> getChitMembers(String chitId) async {
    try {
      final response = await _dio.get('/chits/members/$chitId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final members = apiResponse.data!
              .map((memberJson) => ChitMember.fromJson(memberJson as Map<String, dynamic>))
              .toList();
          return Success(members);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to get chit members');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Send reminder to member
  static Future<Result<void>> sendReminder({
    required String chitId,
    required String memberId,
    required String message,
  }) async {
    try {
      final data = {
        'member_id': memberId,
        'message': message,
      };

      final response = await _dio.post('/chits/$chitId/send-reminder', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to send reminder');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Send reminder to all members
  static Future<Result<int>> sendReminderToAll({
    required String chitId,
    required String message,
  }) async {
    try {
      final data = {
        'message': message,
      };

      final response = await _dio.post('/chits/$chitId/send-reminder-all', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final count = apiResponse.data!['count'] as int;
          return Success(count);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to send reminders');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle Dio errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Response timeout. Please try again.';
      case DioExceptionType.badResponse:
        if (error.response?.data is Map<String, dynamic>) {
          final data = error.response?.data as Map<String, dynamic>;
          return data['message'] ?? 'Server error occurred';
        } else {
          return 'Server error: ${error.response?.statusCode}';
        }
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      default:
        return 'Network error: ${error.message}';
    }
  }
}
