<?php
/**
 * Health Check Endpoint
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');

try {
    $health = [
        'success' => true,
        'message' => 'API Health Check',
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION,
        'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'checks' => []
    ];
    
    // Check if configuration files exist
    $configFiles = [
        'database.php' => __DIR__ . '/../config/database.php',
        'helpers.php' => __DIR__ . '/../utils/helpers.php',
        'JWT.php' => __DIR__ . '/../utils/JWT.php'
    ];
    
    foreach ($configFiles as $name => $path) {
        $health['checks'][$name] = file_exists($path) ? 'exists' : 'missing';
    }
    
    // Test database connection
    if (file_exists(__DIR__ . '/../config/database.php')) {
        require_once __DIR__ . '/../config/database.php';
        
        if (class_exists('Database')) {
            $db = new Database();
            $conn = $db->getConnection();
            $health['checks']['database_connection'] = $conn ? 'connected' : 'failed';
        } else {
            $health['checks']['database_connection'] = 'class_not_found';
        }
    }
    
    // Test helper functions
    if (file_exists(__DIR__ . '/../utils/helpers.php')) {
        require_once __DIR__ . '/../utils/helpers.php';
        $health['checks']['helper_functions'] = function_exists('sendResponse') ? 'loaded' : 'missing';
    }
    
    echo json_encode($health, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Health check failed',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_PRETTY_PRINT);
}

?>
