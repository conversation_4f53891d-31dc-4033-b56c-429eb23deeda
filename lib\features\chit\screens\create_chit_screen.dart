import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/models/chit_model.dart';
import '../../../core/router/app_router.dart';
import '../../auth/widgets/auth_text_field.dart';
import '../../auth/widgets/auth_button.dart';

class CreateChitScreen extends StatefulWidget {
  const CreateChitScreen({super.key});

  @override
  State<CreateChitScreen> createState() => _CreateChitScreenState();
}

class _CreateChitScreenState extends State<CreateChitScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _totalAmountController = TextEditingController();
  final _numberOfMembersController = TextEditingController();
  final _commissionController = TextEditingController();

  ChitFrequency _selectedFrequency = ChitFrequency.monthly;
  DateTime _startDate = DateTime.now().add(const Duration(days: 7));
  bool _isLoading = false;
  bool _isDraftLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _totalAmountController.dispose();
    _numberOfMembersController.dispose();
    _commissionController.dispose();
    super.dispose();
  }

  Future<void> _handleCreateChit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final chitProvider = Provider.of<ChitProvider>(context, listen: false);

      final success = await chitProvider.createChit(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        totalAmount: double.parse(_totalAmountController.text),
        numberOfMembers: int.parse(_numberOfMembersController.text),
        frequency: _selectedFrequency,
        startDate: _startDate,
        commissionPercentage: _commissionController.text.isNotEmpty
            ? double.parse(_commissionController.text)
            : null,
        isDraft: false, // Create as active chit
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chit created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        AppNavigation.goBack(context);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(chitProvider.errorMessage ?? 'Failed to create chit'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Create New Chit'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header Card
              _buildHeaderCard(),

              const SizedBox(height: 24),

              // Basic Information
              _buildBasicInfoSection(),

              const SizedBox(height: 24),

              // Financial Details
              _buildFinancialSection(),

              const SizedBox(height: 24),

              // Schedule Settings
              _buildScheduleSection(),

              const SizedBox(height: 32),

              // Create Button
              AuthButton(
                text: 'Create Chit Fund',
                onPressed: _isLoading ? null : _handleCreateChit,
                isLoading: _isLoading,
              ),

              const SizedBox(height: 16),

              // Save as Draft Button
              OutlinedButton(
                onPressed: (_isLoading || _isDraftLoading) ? null : _saveDraft,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isDraftLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Save as Draft'),
              ),

              const SizedBox(height: 100), // Bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 48,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 12),
            Text(
              'Create Chit Fund',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Set up a new chit fund and invite members to join',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 16),

            // Chit Name
            AuthTextField(
              controller: _nameController,
              labelText: 'Chit Fund Name',
              hintText: 'Enter a descriptive name for your chit fund',
              prefixIcon: Icons.title,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a chit fund name';
                }
                if (value.length < 3) {
                  return 'Name must be at least 3 characters';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Description
            AuthTextField(
              controller: _descriptionController,
              labelText: 'Description',
              hintText: 'Describe the purpose and rules of this chit fund',
              prefixIcon: Icons.description,
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a description';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Financial Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 16),

            // Total Amount
            AuthTextField(
              controller: _totalAmountController,
              labelText: 'Total Chit Amount',
              hintText: 'Enter the total amount for the chit fund',
              prefixIcon: Icons.currency_rupee,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter the total amount';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Please enter a valid amount';
                }
                if (amount < 1000) {
                  return 'Minimum amount is ₹1,000';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Number of Members
            AuthTextField(
              controller: _numberOfMembersController,
              labelText: 'Number of Members',
              hintText: 'How many members will participate?',
              prefixIcon: Icons.group,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter number of members';
                }
                final members = int.tryParse(value);
                if (members == null || members <= 0) {
                  return 'Please enter a valid number';
                }
                if (members < 5) {
                  return 'Minimum 5 members required';
                }
                if (members > 50) {
                  return 'Maximum 50 members allowed';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // Commission Percentage (Optional)
            AuthTextField(
              controller: _commissionController,
              labelText: 'Commission Percentage (Optional)',
              hintText: 'Enter commission percentage (0-10%)',
              prefixIcon: Icons.percent,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final commission = double.tryParse(value);
                  if (commission == null || commission < 0 || commission > 10) {
                    return 'Commission must be between 0-10%';
                  }
                }
                return null;
              },
            ),

            const SizedBox(height: 12),

            // Calculated Values
            if (_totalAmountController.text.isNotEmpty && _numberOfMembersController.text.isNotEmpty)
              _buildCalculatedValues(),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatedValues() {
    final totalAmount = double.tryParse(_totalAmountController.text) ?? 0;
    final numberOfMembers = int.tryParse(_numberOfMembersController.text) ?? 0;

    if (totalAmount > 0 && numberOfMembers > 0) {
      final memberContribution = totalAmount / numberOfMembers;

      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Member Contribution:',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '₹${memberContribution.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            if (_commissionController.text.isNotEmpty) ...[
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Commission Amount:',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    '₹${(totalAmount * (double.tryParse(_commissionController.text) ?? 0) / 100).toStringAsFixed(0)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildScheduleSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Schedule Settings',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 16),

            // Frequency Selection
            Text(
              'Payment Frequency',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 8),

            Wrap(
              spacing: 8,
              children: ChitFrequency.values.map((frequency) {
                return ChoiceChip(
                  label: Text(frequency.displayName),
                  selected: _selectedFrequency == frequency,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        _selectedFrequency = frequency;
                      });
                    }
                  },
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // Start Date
            Text(
              'Start Date',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 8),

            InkWell(
              onTap: _selectStartDate,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      '${_startDate.day}/${_startDate.month}/${_startDate.year}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_drop_down,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Duration Info
            if (_numberOfMembersController.text.isNotEmpty)
              _buildDurationInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildDurationInfo() {
    final numberOfMembers = int.tryParse(_numberOfMembersController.text) ?? 0;
    if (numberOfMembers <= 0) return const SizedBox.shrink();

    int durationMonths;
    switch (_selectedFrequency) {
      case ChitFrequency.monthly:
        durationMonths = numberOfMembers;
        break;
      case ChitFrequency.quarterly:
        durationMonths = numberOfMembers * 3;
        break;
      case ChitFrequency.halfYearly:
        durationMonths = numberOfMembers * 6;
        break;
      case ChitFrequency.yearly:
        durationMonths = numberOfMembers * 12;
        break;
    }

    final endDate = DateTime(_startDate.year, _startDate.month + durationMonths, _startDate.day);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Duration:',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                '$durationMonths months',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Expected End Date:',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                '${endDate.day}/${endDate.month}/${endDate.year}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
      });
    }
  }

  Future<void> _saveDraft() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isDraftLoading = true;
    });

    try {
      final chitProvider = Provider.of<ChitProvider>(context, listen: false);

      final success = await chitProvider.createChit(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        totalAmount: double.parse(_totalAmountController.text),
        numberOfMembers: int.parse(_numberOfMembersController.text),
        frequency: _selectedFrequency,
        startDate: _startDate,
        commissionPercentage: _commissionController.text.isNotEmpty
            ? double.parse(_commissionController.text)
            : null,
        isDraft: true, // Save as draft
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Draft saved successfully!'),
            backgroundColor: Colors.blue,
          ),
        );
        AppNavigation.goBack(context);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(chitProvider.errorMessage ?? 'Failed to save draft'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving draft: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDraftLoading = false;
        });
      }
    }
  }
}
