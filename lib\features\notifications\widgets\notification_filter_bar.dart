import 'package:flutter/material.dart';
import '../../../core/models/notification_model.dart';

class NotificationFilterBar extends StatelessWidget {
  final NotificationType? selectedType;
  final Function(NotificationType?) onTypeChanged;

  const NotificationFilterBar({
    super.key,
    this.selectedType,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            FilterChip(
              label: const Text('All'),
              selected: selectedType == null,
              onSelected: (selected) {
                onTypeChanged(null);
              },
            ),
            
            const SizedBox(width: 8),
            
            ...NotificationType.values.map((type) => Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getTypeIcon(type),
                      size: 16,
                      color: selectedType == type 
                          ? Colors.white 
                          : _getTypeColor(type),
                    ),
                    const SizedBox(width: 4),
                    Text(type.displayName),
                  ],
                ),
                selected: selectedType == type,
                selectedColor: _getTypeColor(type),
                onSelected: (selected) {
                  onTypeChanged(selected ? type : null);
                },
              ),
            )),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.bidding:
        return Colors.orange;
      case NotificationType.general:
        return Colors.blue;
      case NotificationType.reminder:
        return Colors.purple;
      case NotificationType.system:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.bidding:
        return Icons.gavel;
      case NotificationType.general:
        return Icons.update;
      case NotificationType.reminder:
        return Icons.people;
      case NotificationType.system:
        return Icons.settings;
    }
  }
}

// Quick action buttons for notifications
class NotificationQuickActions extends StatelessWidget {
  final VoidCallback? onMarkAllRead;
  final VoidCallback? onClearAll;
  final VoidCallback? onSettings;

  const NotificationQuickActions({
    super.key,
    this.onMarkAllRead,
    this.onClearAll,
    this.onSettings,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onMarkAllRead,
              icon: const Icon(Icons.mark_email_read, size: 16),
              label: const Text('Mark All Read'),
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onClearAll,
              icon: const Icon(Icons.clear_all, size: 16),
              label: const Text('Clear All'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          IconButton(
            onPressed: onSettings,
            icon: const Icon(Icons.settings),
            tooltip: 'Notification Settings',
          ),
        ],
      ),
    );
  }
}

// Notification summary widget
class NotificationSummary extends StatelessWidget {
  final int totalCount;
  final int unreadCount;
  final int importantCount;

  const NotificationSummary({
    super.key,
    required this.totalCount,
    required this.unreadCount,
    required this.importantCount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryItem(
              context,
              'Total',
              totalCount.toString(),
              Icons.notifications,
              Colors.blue,
            ),
          ),
          
          Container(
            width: 1,
            height: 40,
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
          
          Expanded(
            child: _buildSummaryItem(
              context,
              'Unread',
              unreadCount.toString(),
              Icons.mark_email_unread,
              Colors.orange,
            ),
          ),
          
          Container(
            width: 1,
            height: 40,
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
          
          Expanded(
            child: _buildSummaryItem(
              context,
              'Important',
              importantCount.toString(),
              Icons.priority_high,
              Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    String label,
    String count,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        
        const SizedBox(height: 4),
        
        Text(
          count,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }
}

// Notification type legend
class NotificationTypeLegend extends StatelessWidget {
  const NotificationTypeLegend({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Types',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 12),
            
            ...NotificationType.values.map((type) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    _getTypeIcon(type),
                    color: _getTypeColor(type),
                    size: 20,
                  ),
                  
                  const SizedBox(width: 12),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          type.displayName,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          _getTypeDescription(type),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.bidding:
        return Colors.orange;
      case NotificationType.general:
        return Colors.blue;
      case NotificationType.reminder:
        return Colors.purple;
      case NotificationType.system:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return Icons.payment;
      case NotificationType.bidding:
        return Icons.gavel;
      case NotificationType.general:
        return Icons.update;
      case NotificationType.reminder:
        return Icons.people;
      case NotificationType.system:
        return Icons.settings;
    }
  }

  String _getTypeDescription(NotificationType type) {
    switch (type) {
      case NotificationType.payment:
        return 'Payment reminders and confirmations';
      case NotificationType.bidding:
        return 'Bidding rounds and results';
      case NotificationType.general:
        return 'General chit fund updates';
      case NotificationType.reminder:
        return 'Meeting and event reminders';
      case NotificationType.system:
        return 'System messages and announcements';
    }
  }
}
