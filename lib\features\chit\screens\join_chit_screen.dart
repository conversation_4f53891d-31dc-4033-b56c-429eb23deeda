import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/providers/chit_provider.dart';
import '../../../core/router/app_router.dart';
import '../../auth/widgets/auth_text_field.dart';
import '../../auth/widgets/auth_button.dart';

class JoinChitScreen extends StatefulWidget {
  const JoinChitScreen({super.key});

  @override
  State<JoinChitScreen> createState() => _JoinChitScreenState();
}

class _JoinChitScreenState extends State<JoinChitScreen> {
  final _formKey = GlobalKey<FormState>();
  final _chitCodeController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _chitCodeController.dispose();
    super.dispose();
  }

  Future<void> _handleJoinChit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final chitProvider = Provider.of<ChitProvider>(context, listen: false);
      
      final success = await chitProvider.joinChit(_chitCodeController.text.trim());

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Successfully joined the chit!'),
            backgroundColor: Colors.green,
          ),
        );
        AppNavigation.goBack(context);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(chitProvider.errorMessage ?? 'Failed to join chit'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('Join Chit'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Text(
                  'Join a Chit Fund',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  'Enter the chit code provided by the organizer to join an existing chit fund.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Chit Code Input
                AuthTextField(
                  controller: _chitCodeController,
                  labelText: 'Chit Code',
                  hintText: 'Enter the 6-digit chit code',
                  prefixIcon: Icons.qr_code,
                  textInputAction: TextInputAction.done,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter the chit code';
                    }
                    if (value.length != 6) {
                      return 'Chit code must be 6 characters';
                    }
                    return null;
                  },
                  onFieldSubmitted: (_) => _handleJoinChit(),
                ),
                
                const SizedBox(height: 32),
                
                // Join Button
                SizedBox(
                  width: double.infinity,
                  child: AuthButton(
                    text: 'Join Chit',
                    onPressed: _isLoading ? null : _handleJoinChit,
                    isLoading: _isLoading,
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Info Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'How to Join',
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 12),
                        
                        Text(
                          '1. Get the 6-digit chit code from the organizer\n'
                          '2. Enter the code in the field above\n'
                          '3. Click "Join Chit" to become a member\n'
                          '4. Wait for organizer approval',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
