<?php
/**
 * Push Notification Service - Handles FCM push notifications
 */

require_once __DIR__ . '/../config/env.php';

class PushNotificationService {
    
    /**
     * Send push notification to single device
     */
    public static function sendToDevice($deviceToken, $title, $body, $data = []) {
        if (!PUSH_NOTIFICATIONS_ENABLED) {
            error_log("Push notifications disabled - would send: $title");
            return true;
        }
        
        try {
            $payload = [
                'to' => $deviceToken,
                'notification' => [
                    'title' => $title,
                    'body' => $body,
                    'sound' => 'default',
                    'badge' => 1
                ],
                'data' => $data
            ];
            
            return self::sendFCMRequest($payload);
            
        } catch (Exception $e) {
            error_log("Push notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send push notification to multiple devices
     */
    public static function sendToMultipleDevices($deviceTokens, $title, $body, $data = []) {
        if (!PUSH_NOTIFICATIONS_ENABLED) {
            error_log("Push notifications disabled - would send to " . count($deviceTokens) . " devices: $title");
            return true;
        }
        
        try {
            // FCM supports up to 1000 tokens per request
            $chunks = array_chunk($deviceTokens, 1000);
            $results = [];
            
            foreach ($chunks as $chunk) {
                $payload = [
                    'registration_ids' => $chunk,
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                        'sound' => 'default',
                        'badge' => 1
                    ],
                    'data' => $data
                ];
                
                $results[] = self::sendFCMRequest($payload);
            }
            
            return !in_array(false, $results);
            
        } catch (Exception $e) {
            error_log("Bulk push notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send push notification to topic
     */
    public static function sendToTopic($topic, $title, $body, $data = []) {
        if (!PUSH_NOTIFICATIONS_ENABLED) {
            error_log("Push notifications disabled - would send to topic $topic: $title");
            return true;
        }
        
        try {
            $payload = [
                'to' => '/topics/' . $topic,
                'notification' => [
                    'title' => $title,
                    'body' => $body,
                    'sound' => 'default',
                    'badge' => 1
                ],
                'data' => $data
            ];
            
            return self::sendFCMRequest($payload);
            
        } catch (Exception $e) {
            error_log("Topic push notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send payment reminder notification
     */
    public static function sendPaymentReminder($deviceToken, $chitName, $amount, $dueDate) {
        $title = "Payment Reminder";
        $body = "₹$amount due for $chitName on $dueDate";
        $data = [
            'type' => 'payment_reminder',
            'chit_name' => $chitName,
            'amount' => $amount,
            'due_date' => $dueDate
        ];
        
        return self::sendToDevice($deviceToken, $title, $body, $data);
    }
    
    /**
     * Send bidding notification
     */
    public static function sendBiddingNotification($deviceToken, $chitName, $roundNumber) {
        $title = "Bidding Started";
        $body = "Bidding for $chitName - Round $roundNumber has started";
        $data = [
            'type' => 'bidding_started',
            'chit_name' => $chitName,
            'round_number' => $roundNumber
        ];
        
        return self::sendToDevice($deviceToken, $title, $body, $data);
    }
    
    /**
     * Send chit invitation notification
     */
    public static function sendChitInvitation($deviceToken, $chitName, $inviterName) {
        $title = "Chit Invitation";
        $body = "$inviterName invited you to join $chitName";
        $data = [
            'type' => 'chit_invitation',
            'chit_name' => $chitName,
            'inviter_name' => $inviterName
        ];
        
        return self::sendToDevice($deviceToken, $title, $body, $data);
    }
    
    /**
     * Send general notification
     */
    public static function sendGeneralNotification($deviceToken, $title, $body, $type = 'general') {
        $data = [
            'type' => $type,
            'timestamp' => time()
        ];
        
        return self::sendToDevice($deviceToken, $title, $body, $data);
    }
    
    /**
     * Subscribe device to topic
     */
    public static function subscribeToTopic($deviceToken, $topic) {
        try {
            $url = 'https://iid.googleapis.com/iid/v1/' . $deviceToken . '/rel/topics/' . $topic;
            
            $headers = [
                'Authorization: key=' . FCM_SERVER_KEY,
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            return $httpCode == 200;
            
        } catch (Exception $e) {
            error_log("Topic subscription error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Unsubscribe device from topic
     */
    public static function unsubscribeFromTopic($deviceToken, $topic) {
        try {
            $url = 'https://iid.googleapis.com/iid/v1/' . $deviceToken . '/rel/topics/' . $topic;
            
            $headers = [
                'Authorization: key=' . FCM_SERVER_KEY,
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            return $httpCode == 200;
            
        } catch (Exception $e) {
            error_log("Topic unsubscription error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send FCM request
     */
    private static function sendFCMRequest($payload) {
        $url = 'https://fcm.googleapis.com/fcm/send';
        
        $headers = [
            'Authorization: key=' . FCM_SERVER_KEY,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            $result = json_decode($response, true);
            if ($result && isset($result['success']) && $result['success'] > 0) {
                error_log("Push notification sent successfully");
                self::logNotification($payload, 'success', $response);
                return true;
            } else {
                error_log("FCM error: " . $response);
                self::logNotification($payload, 'failed', $response);
                return false;
            }
        } else {
            error_log("FCM HTTP error: $httpCode - $response");
            self::logNotification($payload, 'failed', "HTTP $httpCode: $response");
            return false;
        }
    }
    
    /**
     * Log notification for audit trail
     */
    private static function logNotification($payload, $status, $response) {
        try {
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $sql = "INSERT INTO push_notification_logs (payload, status, response, sent_at) VALUES (?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                json_encode($payload),
                $status,
                $response
            ]);
            
        } catch (Exception $e) {
            error_log("Notification log error: " . $e->getMessage());
        }
    }
    
    /**
     * Get notification statistics
     */
    public static function getNotificationStats($days = 30) {
        try {
            $db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
            $conn = $db->getConnection();
            
            $sql = "SELECT 
                        status,
                        COUNT(*) as count,
                        DATE(sent_at) as date
                    FROM push_notification_logs 
                    WHERE sent_at > DATE_SUB(NOW(), INTERVAL ? DAY)
                    GROUP BY status, DATE(sent_at)
                    ORDER BY date DESC";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute([$days]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Notification stats error: " . $e->getMessage());
            return [];
        }
    }
}
