import 'package:flutter/foundation.dart';
import '../models/chit_model.dart';
import '../models/api_response.dart';
import '../services/member_service.dart';

class MemberProvider extends ChangeNotifier {
  List<ChitMember> _members = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<ChitMember> get members => _members;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Filtered members
  List<ChitMember> get activeMembers => _members.where((member) => member.isActive).toList();
  List<ChitMember> get inactiveMembers => _members.where((member) => !member.isActive).toList();
  List<ChitMember> get organizers => _members.where((member) => member.role == MemberRole.organizer).toList();
  List<ChitMember> get regularMembers => _members.where((member) => member.role == MemberRole.member).toList();

  // Add a new member
  Future<Result<ChitMember>> addMember({
    required String chitId,
    required String name,
    required String email,
    required String phone,
    MemberRole role = MemberRole.member,
  }) async {
    try {
      _setLoading(true);
      
      final result = await MemberService.addMember(
        chitId: chitId,
        name: name,
        email: email,
        phone: phone,
        role: role,
      );

      if (result.isSuccess) {
        _members.add(result.data!);
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to add member');
        return result;
      }
    } catch (e) {
      final error = 'Failed to add member: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Remove a member
  Future<Result<void>> removeMember({
    required String chitId,
    required String memberId,
  }) async {
    try {
      _setLoading(true);
      
      final result = await MemberService.removeMember(
        chitId: chitId,
        memberId: memberId,
      );

      if (result.isSuccess) {
        _members.removeWhere((member) => member.id == memberId);
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to remove member');
        return result;
      }
    } catch (e) {
      final error = 'Failed to remove member: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Update member details
  Future<Result<ChitMember>> updateMember({
    required String chitId,
    required String memberId,
    String? name,
    String? email,
    String? phone,
    MemberRole? role,
    bool? isActive,
  }) async {
    try {
      _setLoading(true);
      
      final result = await MemberService.updateMember(
        chitId: chitId,
        memberId: memberId,
        name: name,
        email: email,
        phone: phone,
        role: role,
        isActive: isActive,
      );

      if (result.isSuccess) {
        // Update member in local list
        final memberIndex = _members.indexWhere((member) => member.id == memberId);
        if (memberIndex != -1) {
          _members[memberIndex] = result.data!;
        }
        
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to update member');
        return result;
      }
    } catch (e) {
      final error = 'Failed to update member: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Load members for a chit
  Future<void> loadMembers(String chitId) async {
    try {
      _setLoading(true);
      
      final result = await MemberService.getChitMembers(chitId);

      if (result.isSuccess) {
        _members = result.data!;
        _clearError();
      } else {
        _setError(result.error ?? 'Failed to load members');
      }
    } catch (e) {
      _setError('Failed to load members: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Refresh members
  Future<void> refreshMembers(String chitId) async {
    await loadMembers(chitId);
  }

  // Send reminder to a specific member
  Future<Result<void>> sendReminder({
    required String chitId,
    required String memberId,
    required String message,
  }) async {
    try {
      _setLoading(true);
      
      final result = await MemberService.sendReminder(
        chitId: chitId,
        memberId: memberId,
        message: message,
      );

      if (result.isSuccess) {
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to send reminder');
        return result;
      }
    } catch (e) {
      final error = 'Failed to send reminder: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Send reminder to all members
  Future<Result<int>> sendReminderToAll({
    required String chitId,
    required String message,
  }) async {
    try {
      _setLoading(true);
      
      final result = await MemberService.sendReminderToAll(
        chitId: chitId,
        message: message,
      );

      if (result.isSuccess) {
        _clearError();
        return result;
      } else {
        _setError(result.error ?? 'Failed to send reminders');
        return result;
      }
    } catch (e) {
      final error = 'Failed to send reminders: $e';
      _setError(error);
      return Error(error);
    } finally {
      _setLoading(false);
    }
  }

  // Clear members data
  void clearMembers() {
    _members = [];
    _clearError();
    notifyListeners();
  }

  // Get member by ID
  ChitMember? getMemberById(String memberId) {
    try {
      return _members.firstWhere((member) => member.id == memberId);
    } catch (e) {
      return null;
    }
  }

  // Get member statistics
  Map<String, dynamic> getMemberStatistics() {
    return {
      'totalMembers': _members.length,
      'activeMembers': activeMembers.length,
      'inactiveMembers': inactiveMembers.length,
      'organizers': organizers.length,
      'regularMembers': regularMembers.length,
      'winnersCount': _members.where((member) => member.hasWon).length,
      'pendingWinners': _members.where((member) => !member.hasWon && member.isActive).length,
    };
  }

  // Search members
  List<ChitMember> searchMembers(String query) {
    if (query.isEmpty) return _members;
    
    final lowercaseQuery = query.toLowerCase();
    return _members.where((member) {
      return member.name.toLowerCase().contains(lowercaseQuery) ||
             member.email.toLowerCase().contains(lowercaseQuery) ||
             member.phone.contains(query);
    }).toList();
  }

  // Filter members by role
  List<ChitMember> filterMembersByRole(MemberRole? role) {
    if (role == null) return _members;
    return _members.where((member) => member.role == role).toList();
  }

  // Filter members by status
  List<ChitMember> filterMembersByStatus(bool? isActive) {
    if (isActive == null) return _members;
    return _members.where((member) => member.isActive == isActive).toList();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }
}
