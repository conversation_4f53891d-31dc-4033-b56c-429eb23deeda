# LiteSpeed Optimized .htaccess for Chit Fund API
# Optimized for shared hosting environment

# Enable LiteSpeed Cache
<IfModule Litespeed>
    # Enable LiteSpeed Cache
    CacheLookup on
    
    # Cache API responses for 30 minutes
    <LocationMatch "\.(php)$">
        ExpiresActive On
        ExpiresDefault "access plus 30 minutes"
        Header set Cache-Control "public, max-age=1800"
        Header set X-LiteSpeed-Cache-Control "public, max-age=1800"
        Header append X-LiteSpeed-Tag "chitfund-api"
    </LocationMatch>
    
    # Cache static files longer
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, max-age=31536000"
        Header set X-LiteSpeed-Cache-Control "public, max-age=31536000"
    </LocationMatch>
    
    # Don't cache sensitive endpoints
    <LocationMatch "(login|logout|register|password)">
        Header set X-LiteSpeed-Cache-Control "no-cache"
        Header set Cache-Control "no-cache, no-store, must-revalidate"
    </LocationMatch>
</IfModule>

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Enable Gzip compression for LiteSpeed
<IfModule mod_gzip.c>
    mod_gzip_on Yes
    mod_gzip_dechunk Yes
    mod_gzip_item_include file \.(html?|txt|css|js|php|pl|json)$
    mod_gzip_item_include handler ^cgi-script$
    mod_gzip_item_include mime ^text/.*
    mod_gzip_item_include mime ^application/x-javascript.*
    mod_gzip_item_include mime ^application/json.*
    mod_gzip_item_exclude mime ^image/.*
    mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
</IfModule>

# Security headers
<IfModule mod_headers.c>
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # CORS headers for API
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, X-LiteSpeed-Cache-Control"
    Header always set Access-Control-Max-Age "86400"
    
    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# URL Rewriting for clean API URLs
RewriteEngine On

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# API routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# Redirect all requests to HTTPS (for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Deny access to sensitive files
<FilesMatch "\.(env|log|sql|md|txt|conf)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Deny access to directories
Options -Indexes

# Deny access to PHP files in uploads directory
<Directory "uploads">
    <FilesMatch "\.php$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</Directory>

# Deny access to logs directory
<Directory "logs">
    Order allow,deny
    Deny from all
</Directory>

# Deny access to config files
<FilesMatch "(config|database|env)\.php$">
    Order allow,deny
    Deny from all
</FilesMatch>

# PHP settings for shared hosting
<IfModule mod_php7.c>
    php_value memory_limit 128M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value post_max_size 8M
    php_value upload_max_filesize 2M
    php_value max_file_uploads 5
    php_value session.gc_maxlifetime 7200
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>

<IfModule mod_php8.c>
    php_value memory_limit 128M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value post_max_size 8M
    php_value upload_max_filesize 2M
    php_value max_file_uploads 5
    php_value session.gc_maxlifetime 7200
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>

# File upload restrictions
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    <Directory "uploads">
        Order allow,deny
        Deny from all
    </Directory>
</FilesMatch>

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteRule \.(jpg|jpeg|png|gif|pdf)$ - [NC,F,L]

# Browser caching for static files
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # Documents
    ExpiresByType application/pdf "access plus 1 month"
    
    # API responses (short cache)
    ExpiresByType application/json "access plus 30 minutes"
</IfModule>

# Limit request size for shared hosting
LimitRequestBody 10485760

# Rate limiting (if supported by hosting)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# Custom error pages
ErrorDocument 400 /api/error.php?code=400
ErrorDocument 401 /api/error.php?code=401
ErrorDocument 403 /api/error.php?code=403
ErrorDocument 404 /api/error.php?code=404
ErrorDocument 500 /api/error.php?code=500
ErrorDocument 503 /api/error.php?code=503

# Prevent access to version control files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Force UTF-8 encoding
AddDefaultCharset UTF-8

# MIME types for better compatibility
<IfModule mod_mime.c>
    AddType application/json .json
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
</IfModule>
