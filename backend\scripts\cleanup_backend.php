<?php
/**
 * Backend Cleanup Script
 * 
 * Removes unnecessary files and optimizes the backend for production
 */

echo "🧹 Backend Cleanup Script for LiteSpeed Optimization\n";
echo "===================================================\n\n";

$base_dir = dirname(__DIR__);

// Files to remove (if they exist)
$files_to_remove = [
    // Old configuration files
    $base_dir . '/config/config.php',
    $base_dir . '/config/database.php',
    $base_dir . '/config/env.example.php',
    
    // Test files
    $base_dir . '/test_syntax.php',
    $base_dir . '/check_imports.php',
    $base_dir . '/test_api.php',
    
    // Development files
    $base_dir . '/README_DEV.md',
    $base_dir . '/DEVELOPMENT.md',
    
    // Backup files
    $base_dir . '/api/index.php.bak',
    $base_dir . '/controllers/AuthController.php.bak',
    
    // Temporary files
    $base_dir . '/temp/',
    $base_dir . '/tmp/',
];

// Directories to remove (if empty)
$dirs_to_check = [
    $base_dir . '/temp',
    $base_dir . '/tmp',
    $base_dir . '/tests',
    $base_dir . '/docs/old',
];

echo "🗑️ Removing unnecessary files...\n";

$removed_count = 0;
foreach ($files_to_remove as $file) {
    if (file_exists($file)) {
        if (is_dir($file)) {
            if (removeDirectory($file)) {
                echo "✅ Removed directory: " . basename($file) . "\n";
                $removed_count++;
            }
        } else {
            if (unlink($file)) {
                echo "✅ Removed file: " . basename($file) . "\n";
                $removed_count++;
            }
        }
    }
}

echo "\n📁 Checking empty directories...\n";

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir) && isDirEmpty($dir)) {
        if (rmdir($dir)) {
            echo "✅ Removed empty directory: " . basename($dir) . "\n";
            $removed_count++;
        }
    }
}

echo "\n🔧 Optimizing file permissions...\n";

// Set proper permissions
$permission_map = [
    $base_dir . '/config/' => 0755,
    $base_dir . '/config/env.php' => 0600,
    $base_dir . '/logs/' => 0755,
    $base_dir . '/cache/' => 0755,
    $base_dir . '/uploads/' => 0755,
    $base_dir . '/scripts/' => 0755,
    $base_dir . '/.htaccess' => 0644,
];

foreach ($permission_map as $path => $permission) {
    if (file_exists($path)) {
        if (chmod($path, $permission)) {
            echo "✅ Set permissions " . decoct($permission) . " for: " . basename($path) . "\n";
        }
    }
}

echo "\n🧹 Cleaning up log files...\n";

// Clean old log files
$logs_dir = $base_dir . '/logs/';
if (is_dir($logs_dir)) {
    $log_files = glob($logs_dir . '*.log');
    $max_size = 10 * 1024 * 1024; // 10MB
    
    foreach ($log_files as $log_file) {
        if (filesize($log_file) > $max_size) {
            // Truncate large log files
            file_put_contents($log_file, '');
            echo "✅ Truncated large log file: " . basename($log_file) . "\n";
        }
    }
    
    // Remove old log backups
    $backup_files = glob($logs_dir . '*.log.*');
    $keep_days = 7;
    
    foreach ($backup_files as $backup_file) {
        if (filemtime($backup_file) < time() - ($keep_days * 24 * 60 * 60)) {
            if (unlink($backup_file)) {
                echo "✅ Removed old log backup: " . basename($backup_file) . "\n";
                $removed_count++;
            }
        }
    }
}

echo "\n🗂️ Cleaning up cache files...\n";

// Clean old cache files
$cache_dir = $base_dir . '/cache/';
if (is_dir($cache_dir)) {
    $cache_files = glob($cache_dir . '*');
    $max_age = 3600; // 1 hour
    
    foreach ($cache_files as $cache_file) {
        if (is_file($cache_file) && (time() - filemtime($cache_file)) > $max_age) {
            if (unlink($cache_file)) {
                echo "✅ Removed old cache file: " . basename($cache_file) . "\n";
                $removed_count++;
            }
        }
    }
}

echo "\n📊 Creating production file structure report...\n";

// Generate file structure report
$structure = generateFileStructure($base_dir);
file_put_contents($base_dir . '/PRODUCTION_STRUCTURE.txt', $structure);
echo "✅ Created production structure report\n";

echo "\n🔍 Validating essential files...\n";

// Check essential files
$essential_files = [
    $base_dir . '/api/index.php',
    $base_dir . '/api/auth/index.php',
    $base_dir . '/config/litespeed_config.php',
    $base_dir . '/config/litespeed_database.php',
    $base_dir . '/config/env.litespeed.php',
    $base_dir . '/database/schema.sql',
    $base_dir . '/.htaccess',
    $base_dir . '/controllers/AuthController.php',
    $base_dir . '/models/User.php',
    $base_dir . '/utils/helpers.php',
    $base_dir . '/utils/JWT.php',
];

$missing_files = [];
foreach ($essential_files as $file) {
    if (!file_exists($file)) {
        $missing_files[] = basename($file);
        echo "❌ Missing essential file: " . basename($file) . "\n";
    } else {
        echo "✅ Essential file present: " . basename($file) . "\n";
    }
}

echo "\n📋 Cleanup Summary:\n";
echo "==================\n";
echo "Files/directories removed: $removed_count\n";
echo "Missing essential files: " . count($missing_files) . "\n";

if (!empty($missing_files)) {
    echo "⚠️ Missing files: " . implode(', ', $missing_files) . "\n";
    echo "Please ensure all essential files are present before deployment.\n";
}

echo "\n🎯 Production Readiness Checklist:\n";
echo "==================================\n";
echo "✅ Unnecessary files removed\n";
echo "✅ File permissions optimized\n";
echo "✅ Log files cleaned\n";
echo "✅ Cache files cleaned\n";
echo "✅ Essential files validated\n";

if (empty($missing_files)) {
    echo "\n🎉 Backend is ready for LiteSpeed production deployment!\n";
    echo "\nNext steps:\n";
    echo "1. Copy config/env.litespeed.php to config/env.php\n";
    echo "2. Update database credentials in config/env.php\n";
    echo "3. Upload to your LiteSpeed shared hosting\n";
    echo "4. Import database/schema.sql\n";
    echo "5. Test with: curl https://yourdomain.com/api/health\n";
} else {
    echo "\n⚠️ Please fix missing files before deployment.\n";
}

echo "\n📖 Documentation:\n";
echo "- API Documentation: API_DOCUMENTATION.md\n";
echo "- Deployment Guide: LITESPEED_DEPLOYMENT.md\n";
echo "- Production Structure: PRODUCTION_STRUCTURE.txt\n";

/**
 * Helper function to remove directory recursively
 */
function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            removeDirectory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

/**
 * Helper function to check if directory is empty
 */
function isDirEmpty($dir) {
    if (!is_readable($dir)) {
        return false;
    }
    
    $handle = opendir($dir);
    while (false !== ($entry = readdir($handle))) {
        if ($entry != "." && $entry != "..") {
            closedir($handle);
            return false;
        }
    }
    closedir($handle);
    return true;
}

/**
 * Generate file structure report
 */
function generateFileStructure($dir, $prefix = '') {
    $structure = '';
    $items = scandir($dir);
    
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }
        
        $path = $dir . DIRECTORY_SEPARATOR . $item;
        $structure .= $prefix . $item;
        
        if (is_dir($path)) {
            $structure .= "/\n";
            $structure .= generateFileStructure($path, $prefix . '  ');
        } else {
            $size = filesize($path);
            $structure .= " (" . formatBytes($size) . ")\n";
        }
    }
    
    return $structure;
}

/**
 * Format bytes to human readable format
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

?>
