import 'package:dio/dio.dart';
import '../models/api_response.dart';
import '../models/chit_model.dart';
import '../utils/api_validator.dart';
import 'api_service.dart';
import 'storage_service.dart';

class ChitService {
  static late Dio _dio;

  static void initialize() {
    ApiService.initialize();
    _dio = Dio(BaseOptions(
      baseUrl: ApiService.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token to requests
          final token = StorageService.getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          handler.next(options);
        },
      ),
    );
  }

  /// Get list of chits for the current user
  static Future<Result<List<Chit>>> getChits({
    int page = 1,
    int limit = 20,
    String? status,
    String? search,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (status != null) queryParams['status'] = status;
      if (search != null) queryParams['search'] = search;

      final response = await _dio.get('/chits', queryParameters: queryParams);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<List<dynamic>>.fromJson(
          response.data,
          (json) => json as List<dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final chits = apiResponse.data!
              .map((chitJson) => Chit.fromJson(chitJson as Map<String, dynamic>))
              .toList();
          return Success(chits);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch chits');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Get chit details by ID
  static Future<Result<Chit>> getChitDetails(String chitId) async {
    try {
      final response = await _dio.get('/chits/$chitId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final chit = Chit.fromJson(apiResponse.data!);
          return Success(chit);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to fetch chit details');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Create a new chit
  static Future<Result<Chit>> createChit({
    required String name,
    required String description,
    required double totalAmount,
    required int numberOfMembers,
    required ChitFrequency frequency,
    required DateTime startDate,
    double? commissionPercentage,
    List<Map<String, dynamic>>? members,
    bool isDraft = false,
  }) async {
    try {
      // Validate and prepare request data
      final data = ApiValidator.validateCreateChitRequest(
        name: name,
        description: description,
        totalAmount: totalAmount,
        numberOfMembers: numberOfMembers,
        frequency: frequency.name,
        startDate: startDate.toIso8601String(),
        commissionPercentage: commissionPercentage,
        members: members,
      );

      // Add status based on isDraft parameter
      data['status'] = isDraft ? 'draft' : 'active';

      // Log the validated request
      ApiValidator.logApiRequest('/chits', data);

      // Try different URL variations to avoid 301 redirect
      Response? response;

      // Try multiple URL variations
      final urlVariations = ['/chits/', '/chits', '/chit', '/chit/'];

      for (final url in urlVariations) {
        try {
          response = await _dio.post(url, data: data);
          break;
        } catch (e) {
          if (url == urlVariations.last) {
            rethrow; // If all variations fail, throw the last error
          }
        }
      }

      if (response == null) {
        throw Exception('All URL variations failed');
      }

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final chit = Chit.fromJson(apiResponse.data!);
          return Success(chit);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to create chit');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Update chit details
  static Future<Result<Chit>> updateChit({
    required String chitId,
    String? name,
    String? description,
    double? totalAmount,
    int? numberOfMembers,
    ChitFrequency? frequency,
    DateTime? startDate,
    double? commissionPercentage,
    ChitStatus? status,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (name != null) data['name'] = name;
      if (description != null) data['description'] = description;
      if (totalAmount != null) data['total_amount'] = totalAmount;
      if (numberOfMembers != null) data['number_of_members'] = numberOfMembers;
      if (frequency != null) data['frequency'] = frequency.name;
      if (startDate != null) data['start_date'] = startDate.toIso8601String();
      if (commissionPercentage != null) data['commission_percentage'] = commissionPercentage;
      if (status != null) data['status'] = status.name;

      final response = await _dio.put('/chits/$chitId', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final chit = Chit.fromJson(apiResponse.data!);
          return Success(chit);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to update chit');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Join a chit using chit code
  static Future<Result<void>> joinChit(String chitCode) async {
    try {
      // Validate and prepare request data
      final data = ApiValidator.validateJoinChitRequest(chitCode: chitCode);

      // Log the validated request
      ApiValidator.logApiRequest('/chits/join', data);

      final response = await _dio.post('/chits/join', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to join chit');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Leave a chit
  static Future<Result<void>> leaveChit(String chitId) async {
    try {
      final response = await _dio.post('/chits/leave/$chitId');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<void>.fromJson(
          response.data,
          (json) => {},
        );

        if (apiResponse.isSuccess) {
          return const Success(null);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to leave chit');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Start a bidding round
  static Future<Result<BiddingRound>> startBiddingRound({
    required String chitId,
    required double startingBid,
    required DateTime meetingDate,
  }) async {
    try {
      final data = {
        'starting_bid': startingBid,
        'meeting_date': meetingDate.toIso8601String(),
      };

      final response = await _dio.post('/chits/$chitId/bidding/start', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final biddingRound = BiddingRound.fromJson(apiResponse.data!);
          return Success(biddingRound);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to start bidding round');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Place a bid
  static Future<Result<Bid>> placeBid({
    required String chitId,
    required String biddingRoundId,
    required double bidAmount,
  }) async {
    try {
      final data = {
        'bid_amount': bidAmount,
      };

      final response = await _dio.post('/chits/$chitId/bidding/$biddingRoundId/bid', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final bid = Bid.fromJson(apiResponse.data!);
          return Success(bid);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to place bid');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Complete bidding round
  static Future<Result<BiddingRound>> completeBiddingRound({
    required String chitId,
    required String biddingRoundId,
    required String winnerId,
  }) async {
    try {
      final data = {
        'winner_id': winnerId,
      };

      final response = await _dio.post('/chits/$chitId/bidding/$biddingRoundId/complete', data: data);

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final biddingRound = BiddingRound.fromJson(apiResponse.data!);
          return Success(biddingRound);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to complete bidding round');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }

  /// Handle Dio errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Response timeout. Please try again.';
      case DioExceptionType.badResponse:
        if (error.response?.data is Map<String, dynamic>) {
          final data = error.response?.data as Map<String, dynamic>;
          return data['message'] ?? 'Server error occurred';
        } else {
          return 'Server error: ${error.response?.statusCode}';
        }
      case DioExceptionType.cancel:
        return 'Request was cancelled';
      case DioExceptionType.connectionError:
        return 'No internet connection. Please check your network.';
      default:
        return 'Network error: ${error.message}';
    }
  }

  /// Activate a draft chit
  static Future<Result<Chit>> activateChit(String chitId) async {
    try {
      final response = await _dio.put('/chits/$chitId/activate');

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<Map<String, dynamic>>.fromJson(
          response.data,
          (json) => json as Map<String, dynamic>,
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          final chit = Chit.fromJson(apiResponse.data!);
          return Success(chit);
        } else {
          return Error(apiResponse.message);
        }
      } else {
        return Error('Failed to activate chit');
      }
    } on DioException catch (e) {
      return Error(_handleDioError(e));
    } catch (e) {
      return Error('Unexpected error: $e');
    }
  }
}
