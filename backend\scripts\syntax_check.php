<?php
/**
 * PHP Syntax Checker for Security Files
 */

echo "🔍 PHP SYNTAX CHECKER\n";
echo "====================\n\n";

$filesToCheck = [
    'backend/security/AdvancedSecurityManager.php',
    'backend/security/ThreatDetectionEngine.php', 
    'backend/security/DataProtectionManager.php',
    'backend/security/SecurityMonitor.php',
    'backend/middleware/SecurityMiddleware.php',
    'backend/scripts/advanced_security_test.php',
    'backend/utils/helpers.php'
];

$allValid = true;

foreach ($filesToCheck as $file) {
    echo "Checking: $file\n";
    
    if (!file_exists($file)) {
        echo "  ❌ File not found\n";
        $allValid = false;
        continue;
    }
    
    // Check basic PHP syntax
    $output = [];
    $returnCode = 0;
    exec("php -l \"$file\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "  ✅ Syntax OK\n";
    } else {
        echo "  ❌ Syntax Error:\n";
        foreach ($output as $line) {
            echo "     $line\n";
        }
        $allValid = false;
    }
    
    // Check for basic issues
    $content = file_get_contents($file);
    
    // Check for unclosed PHP tags
    if (substr_count($content, '<?php') !== substr_count($content, '?>') + 1) {
        echo "  ⚠️  Warning: Possible unclosed PHP tags\n";
    }
    
    // Check for missing semicolons (basic check)
    $lines = explode("\n", $content);
    foreach ($lines as $lineNum => $line) {
        $line = trim($line);
        if (!empty($line) && 
            !preg_match('/^(\/\/|\/\*|\*|#|<\?php|\?>|class|function|if|else|while|for|foreach|switch|case|default|try|catch|finally|\}|\{|return|break|continue)/', $line) &&
            !preg_match('/[;{}]$/', $line) &&
            !preg_match('/^\s*\*/', $line)) {
            // This is a very basic check and may have false positives
        }
    }
    
    echo "\n";
}

if ($allValid) {
    echo "🎉 ALL FILES HAVE VALID SYNTAX!\n";
    echo "✅ Ready for deployment!\n";
} else {
    echo "❌ Some files have syntax errors.\n";
    echo "Please fix the errors before deployment.\n";
}

// Test basic functionality
echo "\n🧪 TESTING BASIC FUNCTIONALITY\n";
echo "==============================\n";

try {
    // Test if classes can be loaded
    if (class_exists('Database') || class_exists('LiteSpeedDatabase')) {
        echo "✅ Database classes available\n";
    } else {
        echo "⚠️  Database classes not found\n";
    }
    
    // Test helper functions
    if (function_exists('sanitizeInput')) {
        echo "✅ Helper functions available\n";
    } else {
        echo "⚠️  Helper functions not found\n";
    }
    
    // Test security functions
    if (function_exists('authenticateUser')) {
        echo "✅ Security functions available\n";
    } else {
        echo "⚠️  Security functions not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing functionality: " . $e->getMessage() . "\n";
}

echo "\n✅ SYNTAX CHECK COMPLETE!\n";

?>
