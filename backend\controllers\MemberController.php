<?php
/**
 * Member Controller - LiteSpeed Optimized
 * 
 * Handles member management operations
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/ChitMember.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Chit.php';
require_once __DIR__ . '/../utils/helpers.php';

class MemberController {
    
    /**
     * Get all members
     */
    public function getAllMembers() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Get query parameters
            $page = (int)($_GET['page'] ?? 1);
            $limit = min((int)($_GET['limit'] ?? 20), 100);
            $search = $_GET['search'] ?? '';
            $chitId = $_GET['chit_id'] ?? null;
            $status = $_GET['status'] ?? null;
            
            $member = new ChitMember();
            $result = $member->getUserAccessibleMembers($user['id'], $page, $limit, $search, $chitId, $status);
            
            sendSuccess($result, 'Members retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get all members error: " . $e->getMessage());
            sendError('Failed to retrieve members', 500);
        }
    }
    
    /**
     * Get specific member
     */
    public function getMember($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $member = new ChitMember();
            if ($member->findById($memberId)) {
                sendSuccess($member->getDetailedInfo(), 'Member retrieved successfully');
            } else {
                sendError('Member not found', 404);
            }
            
        } catch (Exception $e) {
            error_log("Get member error: " . $e->getMessage());
            sendError('Failed to retrieve member', 500);
        }
    }
    
    /**
     * Create new member
     */
    public function createMember() {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if (!in_array($user->role, ['admin', 'organizer'])) {
                sendError('Insufficient permissions', 403);
            }
            
            $data = getRequestData();
            
            // Validate required fields
            $required = ['chit_id', 'user_id'];
            $missing = validateRequired($data, $required);
            
            if (!empty($missing)) {
                sendError('Missing required fields: ' . implode(', ', $missing), 400);
            }
            
            $member = new ChitMember();
            $member->chit_id = (int)$data['chit_id'];
            $member->user_id = (int)$data['user_id'];
            $member->member_number = $data['member_number'] ?? null;
            $member->status = $data['status'] ?? 'active';
            $member->joined_date = $data['joined_date'] ?? date('Y-m-d');
            
            if ($member->create()) {
                logActivity($user->id, 'member_created', [
                    'member_id' => $member->id,
                    'chit_id' => $member->chit_id,
                    'user_id' => $member->user_id
                ]);
                
                sendSuccess($member->getDetailedInfo(), 'Member created successfully', 201);
            } else {
                sendError('Failed to create member', 500);
            }
            
        } catch (Exception $e) {
            error_log("Create member error: " . $e->getMessage());
            sendError('Failed to create member', 500);
        }
    }
    
    /**
     * Update member
     */
    public function updateMember($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if (!in_array($user->role, ['admin', 'organizer'])) {
                sendError('Insufficient permissions', 403);
            }
            
            $data = getRequestData();
            
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            // Update allowed fields
            if (isset($data['member_number'])) {
                $member->member_number = (int)$data['member_number'];
            }
            if (isset($data['status'])) {
                $member->status = sanitizeInput($data['status']);
            }
            if (isset($data['notes'])) {
                $member->notes = sanitizeInput($data['notes']);
            }
            
            if ($member->update()) {
                logActivity($user->id, 'member_updated', [
                    'member_id' => $member->id,
                    'changes' => array_keys($data)
                ]);
                
                sendSuccess($member->getDetailedInfo(), 'Member updated successfully');
            } else {
                sendError('Failed to update member', 500);
            }
            
        } catch (Exception $e) {
            error_log("Update member error: " . $e->getMessage());
            sendError('Failed to update member', 500);
        }
    }
    
    /**
     * Delete member
     */
    public function deleteMember($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if (!in_array($user->role, ['admin', 'organizer'])) {
                sendError('Insufficient permissions', 403);
            }
            
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            if ($member->delete()) {
                logActivity($user->id, 'member_deleted', [
                    'member_id' => $memberId,
                    'chit_id' => $member->chit_id
                ]);
                
                sendSuccess(null, 'Member deleted successfully');
            } else {
                sendError('Failed to delete member', 500);
            }
            
        } catch (Exception $e) {
            error_log("Delete member error: " . $e->getMessage());
            sendError('Failed to delete member', 500);
        }
    }
    
    /**
     * Get member's chits
     */
    public function getMemberChits($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            $chits = $member->getChits();
            sendSuccess($chits, 'Member chits retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get member chits error: " . $e->getMessage());
            sendError('Failed to retrieve member chits', 500);
        }
    }
    
    /**
     * Get member's payments
     */
    public function getMemberPayments($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            $payments = $member->getPayments();
            sendSuccess($payments, 'Member payments retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get member payments error: " . $e->getMessage());
            sendError('Failed to retrieve member payments', 500);
        }
    }
    
    /**
     * Get member summary
     */
    public function getMemberSummary($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            $summary = $member->getSummary();
            sendSuccess($summary, 'Member summary retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get member summary error: " . $e->getMessage());
            sendError('Failed to retrieve member summary', 500);
        }
    }
    
    /**
     * Send invitation to member
     */
    public function sendInvitation($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if (!in_array($user->role, ['admin', 'organizer'])) {
                sendError('Insufficient permissions', 403);
            }
            
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            // Send invitation logic here
            // This would typically send an email or SMS
            
            logActivity($user->id, 'invitation_sent', [
                'member_id' => $memberId,
                'chit_id' => $member->chit_id
            ]);
            
            sendSuccess(null, 'Invitation sent successfully');
            
        } catch (Exception $e) {
            error_log("Send invitation error: " . $e->getMessage());
            sendError('Failed to send invitation', 500);
        }
    }
    
    /**
     * Activate member
     */
    public function activateMember($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if (!in_array($user->role, ['admin', 'organizer'])) {
                sendError('Insufficient permissions', 403);
            }
            
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            $member->status = 'active';
            
            if ($member->update()) {
                logActivity($user->id, 'member_activated', [
                    'member_id' => $memberId
                ]);
                
                sendSuccess($member->getDetailedInfo(), 'Member activated successfully');
            } else {
                sendError('Failed to activate member', 500);
            }
            
        } catch (Exception $e) {
            error_log("Activate member error: " . $e->getMessage());
            sendError('Failed to activate member', 500);
        }
    }
    
    /**
     * Deactivate member
     */
    public function deactivateMember($memberId) {
        try {
            // Verify authentication
            $user = authenticateUser();
            if (!$user) {
                sendError('Unauthorized', 401);
            }
            
            // Check permissions
            if (!in_array($user->role, ['admin', 'organizer'])) {
                sendError('Insufficient permissions', 403);
            }
            
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            $member->status = 'inactive';
            
            if ($member->update()) {
                logActivity($user->id, 'member_deactivated', [
                    'member_id' => $memberId
                ]);
                
                sendSuccess($member->getDetailedInfo(), 'Member deactivated successfully');
            } else {
                sendError('Failed to deactivate member', 500);
            }
            
        } catch (Exception $e) {
            error_log("Deactivate member error: " . $e->getMessage());
            sendError('Failed to deactivate member', 500);
        }
    }
}

?>
