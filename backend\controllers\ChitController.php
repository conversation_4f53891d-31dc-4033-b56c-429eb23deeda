<?php
/**
 * Chit Controller
 *
 * Handles chit fund operations
 */

// Import required dependencies
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../models/Chit.php';
require_once __DIR__ . '/../models/ChitMember.php';
require_once __DIR__ . '/../models/User.php';

class ChitController {
    
    /**
     * Get all chits
     */
    public function getAllChits() {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        // Get query parameters
        $page = (int)($_GET['page'] ?? 1);
        $limit = min((int)($_GET['limit'] ?? 20), 100);
        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? null;
        $organizer_id = $_GET['organizer_id'] ?? null;

        try {
            $chit = new Chit();
            $result = $chit->getUserChits($user['id'], $page, $limit, $search, $status);

            sendSuccess($result, 'Chits retrieved successfully');

        } catch (Exception $e) {
            error_log("Get all chits error: " . $e->getMessage());
            sendError('Failed to retrieve chits', 500);
        }
    }

    /**
     * Get specific chit
     */
    public function getChit($chitId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        try {
            $chit = new Chit();
            if ($chit->findById($chitId)) {
                // Check if user has access to this chit (as organizer or member)
                if (!$chit->hasUserAccess($user['id'])) {
                    sendError('Access denied', 403);
                }
                sendSuccess($chit->getDetailedInfo(), 'Chit retrieved successfully');
            } else {
                sendError('Chit not found', 404);
            }

        } catch (Exception $e) {
            error_log("Get chit error: " . $e->getMessage());
            sendError('Failed to retrieve chit', 500);
        }
    }

    /**
     * Get all chits for the authenticated user (legacy method)
     */
    public function getChits() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        try {
            $chit = new Chit();
            $chits = $chit->getUserChits($userId);
            
            sendSuccess($chits, 'Chits retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get chits error: " . $e->getMessage());
            sendError('Failed to retrieve chits', 500);
        }
    }
    
    /**
     * Get chit details by ID
     */
    public function getChitDetails($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user has access to this chit
            if (!$chit->hasUserAccess($userId)) {
                sendError('Access denied', 403);
            }
            
            $chitData = $chit->getFullDetails();
            
            sendSuccess($chitData, 'Chit details retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get chit details error: " . $e->getMessage());
            sendError('Failed to retrieve chit details', 500);
        }
    }
    
    /**
     * Create a new chit
     */
    public function createChit() {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['name', 'total_amount', 'monthly_amount', 'number_of_members', 'start_date'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        // Validate data
        if ($data['total_amount'] <= 0) {
            sendError('Total amount must be greater than 0', 400);
        }
        
        if ($data['monthly_amount'] <= 0) {
            sendError('Monthly amount must be greater than 0', 400);
        }
        
        if ($data['number_of_members'] < 2) {
            sendError('Number of members must be at least 2', 400);
        }
        
        if (!strtotime($data['start_date'])) {
            sendError('Invalid start date format', 400);
        }
        
        try {
            $chit = new Chit();
            
            // Set chit properties
            $chit->name = sanitizeInput($data['name']);
            $chit->description = sanitizeInput($data['description'] ?? '');
            $chit->total_amount = $data['total_amount'];
            $chit->monthly_amount = $data['monthly_amount'];
            $chit->number_of_members = $data['number_of_members'];
            $chit->start_date = $data['start_date'];
            $chit->organizer_id = $userId;
            $chit->commission_percentage = $data['commission_percentage'] ?? 0.0;
            $chit->status = $data['status'] ?? 'draft';
            
            if ($chit->create()) {
                // Add organizer as first member
                $member = new ChitMember();
                $member->chit_id = $chit->id;
                $member->user_id = $userId;
                $member->role = 'organizer';
                $member->status = 'active';
                $member->create();
                
                // Log activity
                logActivity($userId, 'chit_created', [
                    'chit_id' => $chit->id,
                    'chit_name' => $chit->name
                ]);
                
                sendSuccess([
                    'chit_id' => $chit->id,
                    'chit' => $chit->getFullDetails()
                ], 'Chit created successfully');
            } else {
                sendError('Failed to create chit', 500);
            }
            
        } catch (Exception $e) {
            error_log("Create chit error: " . $e->getMessage());
            sendError('Failed to create chit', 500);
        }
    }
    
    /**
     * Update chit
     */
    public function updateChit($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        $data = getRequestData();
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user is organizer
            if ($chit->organizer_id != $userId) {
                sendError('Only organizer can update chit', 403);
            }
            
            // Update allowed fields
            if (isset($data['name'])) {
                $chit->name = sanitizeInput($data['name']);
            }
            
            if (isset($data['description'])) {
                $chit->description = sanitizeInput($data['description']);
            }
            
            if (isset($data['status'])) {
                $allowedStatuses = ['draft', 'active', 'completed', 'cancelled'];
                if (in_array($data['status'], $allowedStatuses)) {
                    $chit->status = $data['status'];
                }
            }
            
            if ($chit->update()) {
                // Log activity
                logActivity($userId, 'chit_updated', [
                    'chit_id' => $chit->id,
                    'chit_name' => $chit->name
                ]);
                
                sendSuccess($chit->getFullDetails(), 'Chit updated successfully');
            } else {
                sendError('Failed to update chit', 500);
            }
            
        } catch (Exception $e) {
            error_log("Update chit error: " . $e->getMessage());
            sendError('Failed to update chit', 500);
        }
    }

    /**
     * Activate a draft chit
     */
    public function activateChit($chitId) {
        $user = JWT::getCurrentUser();

        if (!$user) {
            sendError('Authentication required', 401);
        }

        try {
            $chit = new Chit();

            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }

            // Check if user is the organizer
            if ($chit->organizer_id != $user['id']) {
                sendError('Only the organizer can activate this chit', 403);
            }

            // Check if chit is in draft status
            if ($chit->status !== 'draft') {
                sendError('Only draft chits can be activated', 400);
            }

            // Validate chit has minimum required members
            $memberCount = $chit->getMemberCount();
            if ($memberCount < 2) {
                sendError('Chit must have at least 2 active members to be activated', 400);
            }

            // Update status to active
            $chit->status = 'active';

            if ($chit->save()) {
                // Log activity
                logActivity($user['id'], 'chit_activated', [
                    'chit_id' => $chitId,
                    'chit_name' => $chit->name
                ]);

                sendSuccess($chit->toArray(), 'Chit activated successfully');
            } else {
                sendError('Failed to activate chit', 500);
            }

        } catch (Exception $e) {
            error_log("Activate chit error: " . $e->getMessage());
            sendError('Failed to activate chit', 500);
        }
    }

    /**
     * Delete chit
     */
    public function deleteChit($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user is organizer
            if ($chit->organizer_id != $userId) {
                sendError('Only organizer can delete chit', 403);
            }
            
            // Check if chit can be deleted (only draft status)
            if ($chit->status !== 'draft') {
                sendError('Only draft chits can be deleted', 400);
            }
            
            if ($chit->delete()) {
                // Log activity
                logActivity($userId, 'chit_deleted', [
                    'chit_id' => $chit->id,
                    'chit_name' => $chit->name
                ]);
                
                sendSuccess(null, 'Chit deleted successfully');
            } else {
                sendError('Failed to delete chit', 500);
            }
            
        } catch (Exception $e) {
            error_log("Delete chit error: " . $e->getMessage());
            sendError('Failed to delete chit', 500);
        }
    }
    
    /**
     * Add member to chit
     */
    public function addMember($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        $data = getRequestData();
        
        // Validate required fields
        $required = ['user_id'];
        $missing = validateRequired($data, $required);
        
        if (!empty($missing)) {
            sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user is organizer
            if ($chit->organizer_id != $userId) {
                sendError('Only organizer can add members', 403);
            }
            
            // Check if chit is not full
            $currentMemberCount = $chit->getMemberCount();
            if ($currentMemberCount >= $chit->number_of_members) {
                sendError('Chit is already full', 400);
            }
            
            // Check if user is already a member
            $member = new ChitMember();
            if ($member->isMember($chitId, $data['user_id'])) {
                sendError('User is already a member of this chit', 400);
            }
            
            // Add member
            $member->chit_id = $chitId;
            $member->user_id = $data['user_id'];
            $member->role = 'member';
            $member->status = 'active';
            
            if ($member->create()) {
                // Log activity
                logActivity($userId, 'member_added', [
                    'chit_id' => $chitId,
                    'member_user_id' => $data['user_id']
                ]);
                
                sendSuccess($member->getDetails(), 'Member added successfully');
            } else {
                sendError('Failed to add member', 500);
            }
            
        } catch (Exception $e) {
            error_log("Add member error: " . $e->getMessage());
            sendError('Failed to add member', 500);
        }
    }
    
    /**
     * Remove member from chit
     */
    public function removeMember($chitId, $memberId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId || !$memberId) {
            sendError('Chit ID and Member ID are required', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user is organizer
            if ($chit->organizer_id != $userId) {
                sendError('Only organizer can remove members', 403);
            }
            
            $member = new ChitMember();
            
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }
            
            // Check if member belongs to this chit
            if ($member->chit_id != $chitId) {
                sendError('Member does not belong to this chit', 400);
            }
            
            // Cannot remove organizer
            if ($member->role === 'organizer') {
                sendError('Cannot remove organizer', 400);
            }
            
            if ($member->remove()) {
                // Log activity
                logActivity($userId, 'member_removed', [
                    'chit_id' => $chitId,
                    'member_id' => $memberId
                ]);
                
                sendSuccess(null, 'Member removed successfully');
            } else {
                sendError('Failed to remove member', 500);
            }
            
        } catch (Exception $e) {
            error_log("Remove member error: " . $e->getMessage());
            sendError('Failed to remove member', 500);
        }
    }
    
    /**
     * Get chit members
     */
    public function getMembers($chitId) {
        $userId = JWT::getUserIdFromToken();
        
        if (!$userId) {
            sendError('Authentication required', 401);
        }
        
        if (!$chitId) {
            sendError('Chit ID is required', 400);
        }
        
        try {
            $chit = new Chit();
            
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }
            
            // Check if user has access to this chit
            if (!$chit->hasUserAccess($userId)) {
                sendError('Access denied', 403);
            }
            
            $member = new ChitMember();
            $members = $member->getChitMembers($chitId);
            
            sendSuccess($members, 'Members retrieved successfully');
            
        } catch (Exception $e) {
            error_log("Get members error: " . $e->getMessage());
            sendError('Failed to retrieve members', 500);
        }
    }

    /**
     * Get chit members
     */
    public function getChitMembers($chitId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        try {
            $chit = new Chit();
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }

            $members = $chit->getMembers();
            sendSuccess($members, 'Chit members retrieved successfully');

        } catch (Exception $e) {
            error_log("Get chit members error: " . $e->getMessage());
            sendError('Failed to retrieve chit members', 500);
        }
    }

    /**
     * Get chit payments
     */
    public function getChitPayments($chitId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        try {
            $chit = new Chit();
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }

            $payments = $chit->getPayments();
            sendSuccess($payments, 'Chit payments retrieved successfully');

        } catch (Exception $e) {
            error_log("Get chit payments error: " . $e->getMessage());
            sendError('Failed to retrieve chit payments', 500);
        }
    }

    /**
     * Get chit bidding rounds
     */
    public function getChitBidding($chitId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        try {
            $chit = new Chit();
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }

            $bidding = $chit->getBiddingRounds();
            sendSuccess($bidding, 'Chit bidding rounds retrieved successfully');

        } catch (Exception $e) {
            error_log("Get chit bidding error: " . $e->getMessage());
            sendError('Failed to retrieve chit bidding rounds', 500);
        }
    }

    /**
     * Get chit summary
     */
    public function getChitSummary($chitId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        try {
            $chit = new Chit();
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }

            $summary = $chit->getSummary();
            sendSuccess($summary, 'Chit summary retrieved successfully');

        } catch (Exception $e) {
            error_log("Get chit summary error: " . $e->getMessage());
            sendError('Failed to retrieve chit summary', 500);
        }
    }

    /**
     * Add member to chit
     */
    public function addMember($chitId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        // Check permissions
        if (!in_array($user->role, ['admin', 'organizer'])) {
            sendError('Insufficient permissions', 403);
        }

        $data = getRequestData();

        if (!isset($data['user_id'])) {
            sendError('User ID is required', 400);
        }

        try {
            $chit = new Chit();
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }

            $member = new ChitMember();
            $member->chit_id = $chitId;
            $member->user_id = (int)$data['user_id'];
            $member->member_number = $data['member_number'] ?? null;
            $member->status = 'active';

            if ($member->create()) {
                logActivity($user->id, 'member_added_to_chit', [
                    'chit_id' => $chitId,
                    'member_id' => $member->id,
                    'user_id' => $member->user_id
                ]);

                sendSuccess($member->getDetailedInfo(), 'Member added to chit successfully', 201);
            } else {
                sendError('Failed to add member to chit', 500);
            }

        } catch (Exception $e) {
            error_log("Add member to chit error: " . $e->getMessage());
            sendError('Failed to add member to chit', 500);
        }
    }

    /**
     * Start chit
     */
    public function startChit($chitId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        // Check permissions
        if (!in_array($user->role, ['admin', 'organizer'])) {
            sendError('Insufficient permissions', 403);
        }

        try {
            $chit = new Chit();
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }

            if ($chit->status !== 'draft') {
                sendError('Only draft chits can be started', 400);
            }

            $chit->status = 'active';
            $chit->start_date = date('Y-m-d');

            if ($chit->update()) {
                logActivity($user->id, 'chit_started', [
                    'chit_id' => $chitId
                ]);

                sendSuccess($chit->getDetailedInfo(), 'Chit started successfully');
            } else {
                sendError('Failed to start chit', 500);
            }

        } catch (Exception $e) {
            error_log("Start chit error: " . $e->getMessage());
            sendError('Failed to start chit', 500);
        }
    }

    /**
     * Close chit
     */
    public function closeChit($chitId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        // Check permissions
        if (!in_array($user->role, ['admin', 'organizer'])) {
            sendError('Insufficient permissions', 403);
        }

        try {
            $chit = new Chit();
            if (!$chit->findById($chitId)) {
                sendError('Chit not found', 404);
            }

            if ($chit->status !== 'active') {
                sendError('Only active chits can be closed', 400);
            }

            $chit->status = 'completed';
            $chit->end_date = date('Y-m-d');

            if ($chit->update()) {
                logActivity($user->id, 'chit_closed', [
                    'chit_id' => $chitId
                ]);

                sendSuccess($chit->getDetailedInfo(), 'Chit closed successfully');
            } else {
                sendError('Failed to close chit', 500);
            }

        } catch (Exception $e) {
            error_log("Close chit error: " . $e->getMessage());
            sendError('Failed to close chit', 500);
        }
    }

    /**
     * Update member in chit
     */
    public function updateMember($chitId, $memberId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        // Check permissions
        if (!in_array($user->role, ['admin', 'organizer'])) {
            sendError('Insufficient permissions', 403);
        }

        $data = getRequestData();

        try {
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }

            if ($member->chit_id != $chitId) {
                sendError('Member does not belong to this chit', 400);
            }

            // Update allowed fields
            if (isset($data['member_number'])) {
                $member->member_number = (int)$data['member_number'];
            }
            if (isset($data['status'])) {
                $member->status = sanitizeInput($data['status']);
            }
            if (isset($data['notes'])) {
                $member->notes = sanitizeInput($data['notes']);
            }

            if ($member->update()) {
                logActivity($user->id, 'chit_member_updated', [
                    'chit_id' => $chitId,
                    'member_id' => $memberId,
                    'changes' => array_keys($data)
                ]);

                sendSuccess($member->getDetailedInfo(), 'Member updated successfully');
            } else {
                sendError('Failed to update member', 500);
            }

        } catch (Exception $e) {
            error_log("Update chit member error: " . $e->getMessage());
            sendError('Failed to update member', 500);
        }
    }

    /**
     * Remove member from chit
     */
    public function removeMember($chitId, $memberId) {
        // Verify authentication
        $user = authenticateUser();
        if (!$user) {
            sendError('Unauthorized', 401);
        }

        // Check permissions
        if (!in_array($user->role, ['admin', 'organizer'])) {
            sendError('Insufficient permissions', 403);
        }

        try {
            $member = new ChitMember();
            if (!$member->findById($memberId)) {
                sendError('Member not found', 404);
            }

            if ($member->chit_id != $chitId) {
                sendError('Member does not belong to this chit', 400);
            }

            if ($member->delete()) {
                logActivity($user->id, 'chit_member_removed', [
                    'chit_id' => $chitId,
                    'member_id' => $memberId,
                    'user_id' => $member->user_id
                ]);

                sendSuccess(null, 'Member removed from chit successfully');
            } else {
                sendError('Failed to remove member from chit', 500);
            }

        } catch (Exception $e) {
            error_log("Remove chit member error: " . $e->getMessage());
            sendError('Failed to remove member from chit', 500);
        }
    }
}
?>
