<?php
/**
 * Security Test Script
 * 
 * Tests all security implementations to ensure they're working correctly
 * Run this script to validate your security setup
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/helpers.php';
require_once __DIR__ . '/../utils/JWT.php';
require_once __DIR__ . '/../middleware/SecurityMiddleware.php';

class SecurityTester {
    
    private $results = [];
    private $db;
    
    public function __construct() {
        $this->db = class_exists('LiteSpeedDatabase') ? new LiteSpeedDatabase() : new Database();
    }
    
    /**
     * Run all security tests
     */
    public function runAllTests() {
        echo "🔒 CHIT FUND SECURITY TEST SUITE\n";
        echo "================================\n\n";
        
        $this->testDatabaseSecurity();
        $this->testInputSanitization();
        $this->testSQLInjectionProtection();
        $this->testXSSProtection();
        $this->testJWTSecurity();
        $this->testRateLimiting();
        $this->testPasswordSecurity();
        $this->testSecurityLogging();
        
        $this->displayResults();
    }
    
    /**
     * Test database security
     */
    private function testDatabaseSecurity() {
        echo "Testing Database Security...\n";
        
        try {
            $conn = $this->db->getConnection();
            
            // Test if security tables exist
            $tables = ['token_blacklist', 'security_logs', 'failed_login_attempts', 'rate_limits'];
            $allTablesExist = true;
            
            foreach ($tables as $table) {
                $sql = "SHOW TABLES LIKE ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$table]);
                
                if (!$stmt->fetch()) {
                    $allTablesExist = false;
                    break;
                }
            }
            
            $this->results['Database Security'] = $allTablesExist ? '✅ PASS' : '❌ FAIL';
            
        } catch (Exception $e) {
            $this->results['Database Security'] = '❌ FAIL - ' . $e->getMessage();
        }
    }
    
    /**
     * Test input sanitization
     */
    private function testInputSanitization() {
        echo "Testing Input Sanitization...\n";
        
        $testInputs = [
            '<script>alert("xss")</script>' => '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;',
            'SELECT * FROM users' => 'SELECT * FROM users',
            '"DROP TABLE users"' => '&quot;DROP TABLE users&quot;'
        ];
        
        $allPassed = true;
        
        foreach ($testInputs as $input => $expected) {
            $sanitized = sanitizeInput($input);
            if ($sanitized !== $expected) {
                $allPassed = false;
                break;
            }
        }
        
        $this->results['Input Sanitization'] = $allPassed ? '✅ PASS' : '❌ FAIL';
    }
    
    /**
     * Test SQL injection protection
     */
    private function testSQLInjectionProtection() {
        echo "Testing SQL Injection Protection...\n";
        
        $sqlInjectionAttempts = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "UNION SELECT * FROM users",
            "1' AND 1=1 --",
            "admin'--"
        ];
        
        $allBlocked = true;
        
        foreach ($sqlInjectionAttempts as $attempt) {
            if (!detectSQLInjection($attempt)) {
                $allBlocked = false;
                break;
            }
        }
        
        $this->results['SQL Injection Protection'] = $allBlocked ? '✅ PASS' : '❌ FAIL';
    }
    
    /**
     * Test XSS protection
     */
    private function testXSSProtection() {
        echo "Testing XSS Protection...\n";
        
        $xssAttempts = [
            '<script>alert("xss")</script>',
            '<img src="x" onerror="alert(1)">',
            'javascript:alert("xss")',
            '<iframe src="javascript:alert(1)"></iframe>',
            '<object data="javascript:alert(1)"></object>'
        ];
        
        $allSanitized = true;
        
        foreach ($xssAttempts as $attempt) {
            $sanitized = sanitizeInput($attempt);
            if (strpos($sanitized, '<script>') !== false || 
                strpos($sanitized, 'javascript:') !== false ||
                strpos($sanitized, 'onerror=') !== false) {
                $allSanitized = false;
                break;
            }
        }
        
        $this->results['XSS Protection'] = $allSanitized ? '✅ PASS' : '❌ FAIL';
    }
    
    /**
     * Test JWT security
     */
    private function testJWTSecurity() {
        echo "Testing JWT Security...\n";
        
        try {
            // Test token creation
            $token = JWT::createUserToken(1, ['email' => '<EMAIL>', 'role' => 'admin']);
            
            // Test token verification
            $payload = JWT::decode($token);
            
            // Test invalid token
            $invalidToken = $token . 'invalid';
            $invalidPayload = JWT::decode($invalidToken);
            
            $jwtWorking = ($payload !== false && $invalidPayload === false);
            
            $this->results['JWT Security'] = $jwtWorking ? '✅ PASS' : '❌ FAIL';
            
        } catch (Exception $e) {
            $this->results['JWT Security'] = '❌ FAIL - ' . $e->getMessage();
        }
    }
    
    /**
     * Test rate limiting
     */
    private function testRateLimiting() {
        echo "Testing Rate Limiting...\n";
        
        try {
            // Simulate rate limiting check
            $rateLimitWorking = function_exists('checkUserRateLimit');
            
            $this->results['Rate Limiting'] = $rateLimitWorking ? '✅ PASS' : '❌ FAIL';
            
        } catch (Exception $e) {
            $this->results['Rate Limiting'] = '❌ FAIL - ' . $e->getMessage();
        }
    }
    
    /**
     * Test password security
     */
    private function testPasswordSecurity() {
        echo "Testing Password Security...\n";
        
        try {
            $password = 'testpassword123';
            $hash = hashPassword($password);
            
            // Test password hashing
            $hashWorking = password_verify($password, $hash);
            
            // Test wrong password
            $wrongPassword = !password_verify('wrongpassword', $hash);
            
            $passwordSecure = ($hashWorking && $wrongPassword);
            
            $this->results['Password Security'] = $passwordSecure ? '✅ PASS' : '❌ FAIL';
            
        } catch (Exception $e) {
            $this->results['Password Security'] = '❌ FAIL - ' . $e->getMessage();
        }
    }
    
    /**
     * Test security logging
     */
    private function testSecurityLogging() {
        echo "Testing Security Logging...\n";
        
        try {
            $conn = $this->db->getConnection();
            
            // Test if we can write to security logs
            $sql = "INSERT INTO security_logs (user_id, event_type, details, ip_address, severity) 
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute([
                null,
                'security_test',
                json_encode(['test' => 'security logging test']),
                '127.0.0.1',
                'low'
            ]);
            
            // Clean up test log
            $sql = "DELETE FROM security_logs WHERE event_type = 'security_test'";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            
            $this->results['Security Logging'] = $result ? '✅ PASS' : '❌ FAIL';
            
        } catch (Exception $e) {
            $this->results['Security Logging'] = '❌ FAIL - ' . $e->getMessage();
        }
    }
    
    /**
     * Display test results
     */
    private function displayResults() {
        echo "\n🔒 SECURITY TEST RESULTS\n";
        echo "========================\n\n";
        
        $totalTests = count($this->results);
        $passedTests = 0;
        
        foreach ($this->results as $test => $result) {
            echo sprintf("%-25s: %s\n", $test, $result);
            if (strpos($result, '✅ PASS') !== false) {
                $passedTests++;
            }
        }
        
        echo "\n";
        echo "========================\n";
        echo sprintf("TOTAL: %d/%d tests passed\n", $passedTests, $totalTests);
        
        if ($passedTests === $totalTests) {
            echo "🎉 ALL SECURITY TESTS PASSED!\n";
            echo "🛡️ YOUR API IS FULLY SECURED!\n";
        } else {
            echo "⚠️  Some security tests failed. Please review the implementation.\n";
        }
        
        echo "\n";
    }
}

// Run the security tests
if (php_sapi_name() === 'cli') {
    $tester = new SecurityTester();
    $tester->runAllTests();
} else {
    echo "This script should be run from the command line for security reasons.";
}

?>
