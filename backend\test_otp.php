<?php
/**
 * Test OTP functionality
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');

try {
    // Include configuration
    if (file_exists(__DIR__ . '/config/env.php')) {
        require_once __DIR__ . '/config/env.php';
    }
    
    // Include database
    require_once __DIR__ . '/config/database.php';
    require_once __DIR__ . '/utils/helpers.php';
    require_once __DIR__ . '/controllers/AuthController.php';
    
    $db = new Database();
    $conn = $db->getConnection();
    
    $tests = [];
    
    // Test 1: Database connection
    $tests['database_connection'] = $conn ? 'OK' : 'FAILED';
    
    // Test 2: Check if otps table exists
    try {
        $stmt = $conn->query("DESCRIBE otps");
        $tests['otps_table_exists'] = 'OK';
        $tests['otps_table_structure'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $tests['otps_table_exists'] = 'FAILED: ' . $e->getMessage();
    }
    
    // Test 3: Test OTP generation and storage
    try {
        $email = '<EMAIL>';
        $otp = sprintf('%06d', mt_rand(0, 999999));
        $type = 'registration';
        $expires_at = date('Y-m-d H:i:s', time() + 600);
        
        // Delete any existing OTPs for this test email
        $sql = "DELETE FROM otps WHERE email = ? AND type = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$email, $type]);
        
        // Insert test OTP
        $sql = "INSERT INTO otps (email, otp, type, expires_at) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([$email, $otp, $type, $expires_at]);
        
        if ($result) {
            $tests['otp_insert'] = 'OK';
            $tests['test_otp'] = $otp;
            
            // Clean up test data
            $sql = "DELETE FROM otps WHERE email = ? AND type = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$email, $type]);
        } else {
            $tests['otp_insert'] = 'FAILED';
        }
    } catch (Exception $e) {
        $tests['otp_insert'] = 'FAILED: ' . $e->getMessage();
    }
    
    // Test 4: Test email configuration
    $tests['email_config'] = [
        'smtp_host' => defined('SMTP_HOST') ? SMTP_HOST : 'NOT_DEFINED',
        'smtp_username' => defined('SMTP_USERNAME') ? SMTP_USERNAME : 'NOT_DEFINED',
        'from_email' => defined('SMTP_FROM_EMAIL') ? SMTP_FROM_EMAIL : 'NOT_DEFINED',
    ];
    
    // Test 5: Test AuthController sendOTP method
    try {
        // Simulate POST data
        $_POST = [
            'email' => '<EMAIL>',
            'type' => 'registration'
        ];
        
        // Capture output
        ob_start();
        $authController = new AuthController();
        
        // This should work if everything is configured correctly
        $tests['auth_controller'] = 'AuthController instantiated successfully';
        
        ob_end_clean();
    } catch (Exception $e) {
        $tests['auth_controller'] = 'FAILED: ' . $e->getMessage();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'OTP Test Results',
        'tests' => $tests,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Test failed',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_PRETTY_PRINT);
}

?>
